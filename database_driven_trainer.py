"""
基于专家数据库的训练器
Database-Driven Trainer

使用真实的专家数据库训练神经网络动作生成器
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import pickle
import matplotlib.pyplot as plt
from typing import List, Dict
import os

from pretrained_action_generator import ActionGeneratorNet
from expert_database_builder import ExpertDatabaseBuilder

class DatabaseDrivenTrainer:
    """基于专家数据库的训练器"""
    
    def __init__(self, device='cpu'):
        self.device = device
        self.net = ActionGeneratorNet().to(device)
        self.optimizer = torch.optim.Adam(self.net.parameters(), lr=0.001)
        
        # 训练历史
        self.training_history = {
            'total_loss': [],
            'action_loss': [],
            'quality_loss': [],
            'physics_loss': []
        }
        
    def load_expert_database(self, database_path="hybrid_expert_database.pkl") -> List[Dict]:
        """加载专家数据库"""
        if os.path.exists(database_path):
            with open(database_path, 'rb') as f:
                database = pickle.load(f)
            print(f"📂 已加载专家数据库: {len(database)} 条数据")
            return database
        else:
            print(f"❌ 数据库文件不存在: {database_path}")
            return []
    
    def prepare_training_data(self, database: List[Dict]) -> Dict:
        """准备训练数据"""
        print("🔄 准备训练数据...")
        
        states = []
        goals = []
        obstacles_list = []
        expert_actions_list = []
        quality_labels = []
        
        for sample in database:
            states.append(sample['state'][:6])  # 确保是6维状态
            goals.append(sample['goal'])
            obstacles_list.append(sample['obstacles'])
            
            # 处理专家动作：确保有20个动作
            expert_actions = sample['expert_actions']
            if len(expert_actions) < 20:
                # 如果专家动作不足20个，重复最后一个动作
                while len(expert_actions) < 20:
                    expert_actions.append(expert_actions[-1] if expert_actions else np.zeros(3))
            elif len(expert_actions) > 20:
                # 如果超过20个，取前20个
                expert_actions = expert_actions[:20]
            
            expert_actions_list.append(expert_actions)
            quality_labels.append(sample['quality_score'])
        
        # 转换为tensor
        states_tensor = torch.tensor(states, dtype=torch.float32, device=self.device)
        goals_tensor = torch.tensor(goals, dtype=torch.float32, device=self.device)
        expert_actions_tensor = torch.tensor(expert_actions_list, dtype=torch.float32, device=self.device)
        quality_labels_tensor = torch.tensor(quality_labels, dtype=torch.float32, device=self.device)
        
        print(f"✅ 训练数据准备完成:")
        print(f"  状态维度: {states_tensor.shape}")
        print(f"  目标维度: {goals_tensor.shape}")
        print(f"  专家动作维度: {expert_actions_tensor.shape}")
        print(f"  质量标签维度: {quality_labels_tensor.shape}")
        
        return {
            'states': states_tensor,
            'goals': goals_tensor,
            'obstacles': obstacles_list,
            'expert_actions': expert_actions_tensor,
            'quality_labels': quality_labels_tensor
        }
    
    def train_with_database(self, database: List[Dict], num_epochs=100, batch_size=32):
        """使用专家数据库训练网络"""
        print(f"🧠 开始基于数据库的训练，epochs: {num_epochs}")
        
        # 准备训练数据
        training_data = self.prepare_training_data(database)
        
        dataset_size = len(training_data['states'])
        
        for epoch in range(num_epochs):
            epoch_losses = {
                'total': [],
                'action': [],
                'quality': [],
                'physics': []
            }
            
            # 随机打乱数据
            indices = torch.randperm(dataset_size)
            
            for i in range(0, dataset_size, batch_size):
                batch_indices = indices[i:i+batch_size]
                
                # 获取批次数据
                batch_states = training_data['states'][batch_indices]
                batch_goals = training_data['goals'][batch_indices]
                batch_expert_actions = training_data['expert_actions'][batch_indices]
                batch_quality_labels = training_data['quality_labels'][batch_indices]
                
                # 获取批次障碍物（简化处理：使用第一个样本的障碍物）
                batch_obstacles = training_data['obstacles'][batch_indices[0].item()]
                
                # 前向传播
                outputs = self.net(batch_states, batch_goals, batch_obstacles)
                
                # 计算损失
                losses = self._compute_losses(outputs, batch_expert_actions, batch_quality_labels, batch_states)
                
                # 反向传播
                self.optimizer.zero_grad()
                losses['total'].backward()
                self.optimizer.step()
                
                # 记录损失
                for key in epoch_losses:
                    epoch_losses[key].append(losses[key].item())
            
            # 记录epoch平均损失
            for key in epoch_losses:
                avg_loss = np.mean(epoch_losses[key])
                self.training_history[f'{key}_loss'].append(avg_loss)
            
            # 打印进度
            if epoch % 20 == 0:
                total_loss = self.training_history['total_loss'][-1]
                action_loss = self.training_history['action_loss'][-1]
                quality_loss = self.training_history['quality_loss'][-1]
                physics_loss = self.training_history['physics_loss'][-1]
                
                print(f"  Epoch {epoch}: Total={total_loss:.4f}, "
                      f"Action={action_loss:.4f}, Quality={quality_loss:.4f}, Physics={physics_loss:.4f}")
        
        print("✅ 训练完成!")
        return self.training_history
    
    def _compute_losses(self, outputs: Dict, expert_actions: torch.Tensor, 
                       quality_labels: torch.Tensor, states: torch.Tensor) -> Dict:
        """计算各种损失"""
        
        # 1. 动作预测损失
        predicted_means = outputs['action_means']
        action_loss = F.mse_loss(predicted_means, expert_actions)
        
        # 2. 质量预测损失
        predicted_quality = outputs['quality_scores'].mean(dim=1)  # 平均质量评分
        quality_loss = F.mse_loss(predicted_quality, quality_labels)
        
        # 3. 物理约束损失
        physics_loss = self._compute_physics_loss(states, predicted_means)
        
        # 4. 动作多样性损失（鼓励生成多样化的动作）
        diversity_loss = self._compute_diversity_loss(predicted_means)
        
        # 总损失
        total_loss = (action_loss + 
                     0.1 * quality_loss + 
                     0.05 * physics_loss + 
                     0.02 * diversity_loss)
        
        return {
            'total': total_loss,
            'action': action_loss,
            'quality': quality_loss,
            'physics': physics_loss
        }
    
    def _compute_physics_loss(self, states: torch.Tensor, actions: torch.Tensor) -> torch.Tensor:
        """计算物理约束损失"""
        batch_size, num_actions, action_dim = actions.shape
        
        # 速度约束
        action_magnitudes = torch.norm(actions, dim=2)  # [batch, num_actions]
        max_speed = 3.0
        speed_violation = F.relu(action_magnitudes - max_speed)
        speed_loss = speed_violation.mean()
        
        # 加速度约束（相邻动作之间的变化）
        if num_actions > 1:
            action_diffs = actions[:, 1:] - actions[:, :-1]  # [batch, num_actions-1, action_dim]
            accel_magnitudes = torch.norm(action_diffs, dim=2)
            max_accel = 2.0
            accel_violation = F.relu(accel_magnitudes - max_accel)
            accel_loss = accel_violation.mean()
        else:
            accel_loss = torch.tensor(0.0, device=self.device)
        
        return speed_loss + accel_loss
    
    def _compute_diversity_loss(self, actions: torch.Tensor) -> torch.Tensor:
        """计算动作多样性损失"""
        batch_size, num_actions, action_dim = actions.shape
        
        if num_actions < 2:
            return torch.tensor(0.0, device=self.device)
        
        # 计算动作之间的平均距离
        diversity_scores = []
        for b in range(batch_size):
            batch_actions = actions[b]  # [num_actions, action_dim]
            
            # 计算所有动作对之间的距离
            distances = []
            for i in range(num_actions):
                for j in range(i+1, num_actions):
                    dist = torch.norm(batch_actions[i] - batch_actions[j])
                    distances.append(dist)
            
            if distances:
                avg_distance = torch.stack(distances).mean()
                diversity_scores.append(avg_distance)
        
        if diversity_scores:
            diversity = torch.stack(diversity_scores).mean()
            # 多样性损失：鼓励更大的多样性
            diversity_loss = F.relu(2.0 - diversity)  # 期望平均距离至少为2.0
        else:
            diversity_loss = torch.tensor(0.0, device=self.device)
        
        return diversity_loss
    
    def evaluate_on_test_scenarios(self, num_test_scenarios=50):
        """在测试场景上评估训练效果"""
        print(f"🧪 在 {num_test_scenarios} 个测试场景上评估...")
        
        self.net.eval()
        
        # 生成测试场景
        builder = ExpertDatabaseBuilder()
        test_scenarios = []
        for i in range(num_test_scenarios):
            scenario = builder._generate_diverse_scenario(1000 + i)  # 使用不同的seed
            test_scenarios.append(scenario)
        
        evaluation_results = {
            'goal_alignment': [],
            'action_diversity': [],
            'generation_time': []
        }
        
        import time
        
        with torch.no_grad():
            for scenario in test_scenarios:
                start_time = time.time()
                
                # 生成动作
                state_tensor = torch.tensor(scenario['state'][:6], dtype=torch.float32, device=self.device).unsqueeze(0)
                goal_tensor = torch.tensor(scenario['goal'], dtype=torch.float32, device=self.device).unsqueeze(0)
                
                outputs = self.net(state_tensor, goal_tensor, scenario['obstacles'])
                generated_actions = outputs['action_means'][0].cpu().numpy()  # [num_actions, action_dim]
                
                generation_time = time.time() - start_time
                evaluation_results['generation_time'].append(generation_time)
                
                # 评估目标对齐度
                goal_direction = scenario['goal'] - scenario['state'][:3]
                goal_direction = goal_direction / (np.linalg.norm(goal_direction) + 1e-6)
                
                alignments = []
                for action in generated_actions:
                    if np.linalg.norm(action) > 0.1:
                        action_direction = action / np.linalg.norm(action)
                        alignment = max(0, np.dot(action_direction, goal_direction))
                        alignments.append(alignment)
                
                avg_alignment = np.mean(alignments) if alignments else 0
                evaluation_results['goal_alignment'].append(avg_alignment)
                
                # 评估动作多样性
                distances = []
                for i in range(len(generated_actions)):
                    for j in range(i+1, len(generated_actions)):
                        dist = np.linalg.norm(generated_actions[i] - generated_actions[j])
                        distances.append(dist)
                
                avg_diversity = np.mean(distances) if distances else 0
                evaluation_results['action_diversity'].append(avg_diversity)
        
        # 打印评估结果
        print(f"📊 评估结果:")
        print(f"  平均目标对齐度: {np.mean(evaluation_results['goal_alignment']):.3f}")
        print(f"  平均动作多样性: {np.mean(evaluation_results['action_diversity']):.3f}")
        print(f"  平均生成时间: {np.mean(evaluation_results['generation_time']):.4f}s")
        
        return evaluation_results
    
    def save_trained_model(self, filepath="database_trained_model.pth"):
        """保存训练好的模型"""
        torch.save({
            'model_state_dict': self.net.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'training_history': self.training_history
        }, filepath)
        print(f"💾 训练好的模型已保存到: {filepath}")
    
    def plot_training_curves(self):
        """绘制训练曲线"""
        plt.figure(figsize=(15, 4))
        
        plt.subplot(1, 3, 1)
        plt.plot(self.training_history['total_loss'], label='Total Loss')
        plt.plot(self.training_history['action_loss'], label='Action Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.title('Training Losses')
        plt.legend()
        plt.grid(True)
        
        plt.subplot(1, 3, 2)
        plt.plot(self.training_history['quality_loss'], label='Quality Loss', color='green')
        plt.plot(self.training_history['physics_loss'], label='Physics Loss', color='red')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.title('Auxiliary Losses')
        plt.legend()
        plt.grid(True)
        
        plt.subplot(1, 3, 3)
        # 显示损失下降趋势
        total_losses = self.training_history['total_loss']
        if len(total_losses) > 10:
            smoothed_loss = np.convolve(total_losses, np.ones(10)/10, mode='valid')
            plt.plot(range(9, len(total_losses)), smoothed_loss, label='Smoothed Total Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.title('Training Progress')
        plt.legend()
        plt.grid(True)
        
        plt.tight_layout()
        plt.savefig('database_training_curves.png', dpi=300, bbox_inches='tight')
        print("📊 训练曲线已保存: database_training_curves.png")

def main():
    """主函数：演示基于数据库的训练"""
    print("🎓 基于专家数据库的神经网络训练")
    print("=" * 60)
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")
    
    trainer = DatabaseDrivenTrainer(device)
    
    try:
        # 1. 加载专家数据库
        database = trainer.load_expert_database("hybrid_expert_database.pkl")
        
        if not database:
            print("❌ 未找到专家数据库，请先运行 expert_database_builder.py")
            return
        
        # 2. 训练网络
        training_history = trainer.train_with_database(database, num_epochs=100, batch_size=16)
        
        # 3. 评估训练效果
        evaluation_results = trainer.evaluate_on_test_scenarios(30)
        
        # 4. 保存模型
        trainer.save_trained_model()
        
        # 5. 绘制训练曲线
        trainer.plot_training_curves()
        
        print("\n" + "=" * 60)
        print("🎉 基于数据库的训练完成!")
        print("📈 关键指标:")
        print(f"  最终训练损失: {training_history['total_loss'][-1]:.4f}")
        print(f"  目标对齐度: {np.mean(evaluation_results['goal_alignment']):.3f}")
        print(f"  动作多样性: {np.mean(evaluation_results['action_diversity']):.3f}")
        print(f"  生成速度: {np.mean(evaluation_results['generation_time']):.4f}s")
        
        if training_history['total_loss'][-1] < 1.0 and np.mean(evaluation_results['goal_alignment']) > 0.5:
            print("✅ 训练成功！网络学会了专家级的动作生成能力")
        else:
            print("⚠️ 训练效果有待提升，建议增加训练数据或调整超参数")
        
    except Exception as e:
        print(f"❌ 训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
