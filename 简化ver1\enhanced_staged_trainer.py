"""
增强的分阶段训练器 - 简化ver1
实现每个阶段内部的"前面固定，后面随机"训练方式
支持场景复杂度评估和最优场景选择
"""

import os
import time
import json
import numpy as np
import random
from datetime import datetime
from collections import deque

# 导入环境配置
from environment_config import (
    get_environment_config, 
    get_training_stage_config
)

# 导入核心组件
from dwa_rl_core import StabilizedRewardEnvironment, StabilizedTD3Controller, td3_config

class EnhancedStagedTrainer:
    """增强的分阶段训练器"""
    
    def __init__(self, start_stage=1, end_stage=3, seed=42, visualization_interval=20):
        self.start_stage = start_stage
        self.end_stage = end_stage
        self.seed = seed
        self.visualization_interval = visualization_interval
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 设置随机种子
        random.seed(seed)
        np.random.seed(seed)
        
        # 创建输出目录
        self.output_dir = f'enhanced_staged_training_{self.timestamp}'
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 训练结果记录
        self.training_results = {
            "start_time": datetime.now().isoformat(),
            "stages": {},
            "scenario_analysis": {},
            "stage_transitions": []
        }
        
        print(f"🎯 增强分阶段训练系统")
        print(f"📅 训练时间: {self.timestamp}")
        print(f"🎯 训练阶段: {start_stage} 到 {end_stage}")
        print(f"🎲 随机种子: {seed}")
        print(f"📁 输出目录: {self.output_dir}")
        print("=" * 60)
    
    def run_enhanced_staged_training(self):
        """执行增强分阶段训练"""
        print(f"\n🚀 开始增强分阶段训练...")
        
        # 阶段名称映射
        stage_names = {
            1: "stage1_simple",
            2: "stage2_complex", 
            3: "stage3_dynamic"
        }
        
        current_controller = None
        total_start_time = time.time()
        
        # 逐阶段训练
        for stage_num in range(self.start_stage, self.end_stage + 1):
            stage_key = stage_names[stage_num]
            stage_config = get_training_stage_config(stage_key)
            env_config = get_environment_config(stage_config["environment"])
            
            print(f"\n📍 阶段 {stage_num}: {stage_config['description']}")
            print("=" * 60)
            print(f"环境: {stage_config['environment']}")
            print(f"固定场景训练: {stage_config['fixed_episodes']} episodes")
            print(f"随机场景训练: {stage_config['random_episodes']} episodes")
            print(f"总计: {stage_config['total_episodes']} episodes")
            
            try:
                # 训练当前阶段
                stage_results, trained_controller = self._train_stage_with_fixed_random(
                    stage_num, stage_config, env_config, current_controller
                )
                
                # 记录阶段结果
                self.training_results["stages"][f"stage_{stage_num}"] = stage_results
                current_controller = trained_controller
                
                print(f"✅ 阶段 {stage_num} 完成")
                self._print_stage_summary(stage_results)
                
            except Exception as e:
                print(f"❌ 阶段 {stage_num} 训练失败: {e}")
                self.training_results["stages"][f"stage_{stage_num}"] = {"error": str(e)}
                break
        
        total_training_time = time.time() - total_start_time
        
        # 保存训练结果
        self.training_results["end_time"] = datetime.now().isoformat()
        self.training_results["total_training_time"] = total_training_time
        
        results_path = os.path.join(self.output_dir, "enhanced_staged_training_results.json")
        with open(results_path, 'w', encoding='utf-8') as f:
            json.dump(self.training_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📊 训练结果已保存: {results_path}")
        self._print_final_summary()
        
        return self.training_results, current_controller
    
    def _train_stage_with_fixed_random(self, stage_num, stage_config, env_config, previous_controller):
        """训练单个阶段 - 固定+随机方式"""
        stage_start_time = time.time()
        
        # 创建环境
        env = StabilizedRewardEnvironment(
            bounds=[100, 100, 100],
            environment_config=env_config,
            reward_type='simplified'
        )
        
        # 创建或继承控制器
        if previous_controller is not None:
            controller = previous_controller
            print(f"🔄 继承上一阶段的训练模型")
        else:
            controller = StabilizedTD3Controller(td3_config)
            print(f"🆕 创建新的训练模型")
        
        # 阶段训练数据
        stage_data = {
            'fixed_phase': {
                'episodes': [], 'rewards': [], 'success_count': 0, 'trajectories': [],
                'selected_scenario': None, 'scenario_complexity': 0
            },
            'random_phase': {
                'episodes': [], 'rewards': [], 'success_count': 0, 'trajectories': [],
                'scenario_complexities': [], 'best_scenarios': []
            },
            'visualization_episodes': [],
            'phase_transition_data': {}
        }
        
        global_episode_count = 0
        
        # 第一子阶段：随机场景探索（用于选择最佳固定场景）
        print(f"\n🎲 子阶段1: 随机场景探索 ({stage_config['random_episodes']} episodes)")
        print("目标：探索不同随机场景，选择最具挑战性的场景用于后续固定训练")
        
        scenario_candidates = []
        
        for episode in range(stage_config['random_episodes']):
            # 随机生成场景
            state = env.reset()
            scenario_data = env.save_scenario()
            
            # 训练episode
            episode_reward, episode_success, trajectory, episode_stats = self._train_single_episode_detailed(
                env, controller, episode, global_episode_count, None, f"Stage{stage_num}_RandomExplore"
            )
            
            # 评估场景复杂度
            complexity_score = self._evaluate_scenario_complexity(env, episode_stats, scenario_data)
            
            # 记录场景候选
            scenario_candidates.append({
                'scenario': scenario_data,
                'complexity_score': complexity_score,
                'episode_reward': episode_reward,
                'episode_success': episode_success,
                'episode_stats': episode_stats,
                'episode_num': episode
            })
            
            # 记录数据
            stage_data['random_phase']['episodes'].append(episode)
            stage_data['random_phase']['rewards'].append(episode_reward)
            stage_data['random_phase']['scenario_complexities'].append(complexity_score)
            if episode_success:
                stage_data['random_phase']['success_count'] += 1
            
            global_episode_count += 1
            
            # 每10个episode报告一次
            if (episode + 1) % 10 == 0:
                recent_success_rate = sum(1 for i in range(max(0, episode-9), episode+1) 
                                        if scenario_candidates[i]['episode_success']) / min(10, episode+1)
                avg_complexity = np.mean([sc['complexity_score'] for sc in scenario_candidates[-10:]])
                print(f"Episode {episode+1:3d}: 成功率={recent_success_rate:.2%}, "
                      f"平均复杂度={avg_complexity:.1f}, 当前复杂度={complexity_score:.1f}")
        
        # 选择最具挑战性的场景用于固定训练
        # 选择复杂度高但不是完全不可能的场景
        valid_scenarios = [sc for sc in scenario_candidates if sc['complexity_score'] > 0]
        if valid_scenarios:
            # 按复杂度排序，选择前20%中的一个
            valid_scenarios.sort(key=lambda x: x['complexity_score'], reverse=True)
            top_20_percent = max(1, len(valid_scenarios) // 5)
            selected_scenario = random.choice(valid_scenarios[:top_20_percent])
        else:
            selected_scenario = random.choice(scenario_candidates)
        
        stage_data['fixed_phase']['selected_scenario'] = selected_scenario['scenario']
        stage_data['fixed_phase']['scenario_complexity'] = selected_scenario['complexity_score']
        
        print(f"\n🎯 选定固定训练场景:")
        print(f"  • Episode: {selected_scenario['episode_num'] + 1}")
        print(f"  • 复杂度分数: {selected_scenario['complexity_score']:.1f}")
        print(f"  • 障碍物数量: {len(selected_scenario['scenario']['obstacles'])}")
        print(f"  • 当时表现: {'成功' if selected_scenario['episode_success'] else '失败'}")
        
        # 记录阶段转换数据
        stage_data['phase_transition_data'] = {
            'random_phase_success_rate': stage_data['random_phase']['success_count'] / stage_config['random_episodes'],
            'selected_scenario_complexity': selected_scenario['complexity_score'],
            'total_scenarios_evaluated': len(scenario_candidates),
            'avg_scenario_complexity': np.mean([sc['complexity_score'] for sc in scenario_candidates])
        }
        
        # 第二子阶段：固定场景强化训练
        print(f"\n📌 子阶段2: 固定场景强化训练 ({stage_config['fixed_episodes']} episodes)")
        print("目标：在选定的挑战性场景中强化训练，提高特定场景的表现")
        
        for episode in range(stage_config['fixed_episodes']):
            # 使用固定场景
            episode_reward, episode_success, trajectory, episode_stats = self._train_single_episode_detailed(
                env, controller, episode, global_episode_count, selected_scenario['scenario'], f"Stage{stage_num}_FixedTrain"
            )
            
            # 记录数据
            stage_data['fixed_phase']['episodes'].append(episode)
            stage_data['fixed_phase']['rewards'].append(episode_reward)
            if episode_success:
                stage_data['fixed_phase']['success_count'] += 1
            
            global_episode_count += 1
            
            # 每10个episode报告一次
            if (episode + 1) % 10 == 0:
                recent_success_rate = sum(1 for i in range(max(0, episode-9), episode+1) 
                                        if i < len(stage_data['fixed_phase']['rewards']) and 
                                        stage_data['fixed_phase']['episodes'][i] >= episode-9) / min(10, episode+1)
                avg_reward = np.mean(stage_data['fixed_phase']['rewards'][-10:])
                print(f"Episode {episode+1:3d}: 成功率={recent_success_rate:.2%}, "
                      f"平均奖励={avg_reward:.1f}, 当前奖励={episode_reward:.1f}")
        
        stage_training_time = time.time() - stage_start_time
        
        # 计算阶段统计
        stage_results = self._calculate_stage_results(stage_config, stage_data, stage_training_time)
        
        return stage_results, controller

    def _train_single_episode_detailed(self, env, controller, episode, global_episode, fixed_scenario, phase_name):
        """训练单个episode（详细版本）"""
        # 重置环境
        if fixed_scenario is not None:
            state = env.reset(fixed_scenario)
        else:
            state = env.reset()

        full_state = np.concatenate([env.state, state[6:]])

        episode_reward = 0
        step_count = 0
        episode_success = False
        trajectory = []
        collision_count = 0
        min_obstacle_distance = float('inf')

        while step_count < 500:
            # 记录轨迹
            trajectory.append(env.state[:3].copy())

            # 计算当前最小障碍物距离
            pos = env.state[:3]
            for obs in env.obstacles:
                dist = np.linalg.norm(pos - obs['center']) - obs['radius']
                min_obstacle_distance = min(min_obstacle_distance, max(0, dist))

            # 获取动作
            action, info, safe_actions = controller.get_action_with_quality(
                full_state, env.goal, env.obstacles, add_noise=True
            )

            # 执行动作
            next_state, reward, done, env_info = env.step(action)
            next_full_state = np.concatenate([env.state, next_state[6:]])

            episode_reward += reward
            step_count += 1

            # 记录碰撞
            if env_info.get('collision', False):
                collision_count += 1

            # 存储经验
            controller.replay_buffer.add(
                full_state.copy(),
                action.copy(),
                reward,
                next_full_state.copy(),
                done,
                safe_actions,
                env.goal.copy(),
                [obs.copy() for obs in env.obstacles],
                info.get('selected_idx', 0)
            )

            # 训练更新
            controller.immediate_update(batch_size=64)

            if done:
                if env_info.get('success', False):
                    episode_success = True
                break

            full_state = next_full_state

        # 生成可视化（每隔指定间隔）
        if (global_episode + 1) % self.visualization_interval == 0:
            self._generate_3d_trajectory_plot(env, trajectory, global_episode, episode_success, phase_name)

        # 计算episode统计
        episode_stats = {
            'step_count': step_count,
            'collision_count': collision_count,
            'min_obstacle_distance': min_obstacle_distance,
            'final_goal_distance': np.linalg.norm(env.state[:3] - env.goal),
            'trajectory_length': len(trajectory),
            'success': episode_success
        }

        return episode_reward, episode_success, trajectory, episode_stats

    def _evaluate_scenario_complexity(self, env, episode_stats, scenario_data):
        """评估场景复杂度"""
        complexity_score = 0

        # 1. 障碍物数量和密度
        num_obstacles = len(scenario_data['obstacles'])
        complexity_score += num_obstacles * 2

        # 2. 障碍物大小变化
        if num_obstacles > 0:
            radii = [obs['radius'] for obs in scenario_data['obstacles']]
            radius_variance = np.var(radii)
            complexity_score += radius_variance * 5

        # 3. 空间利用率（障碍物覆盖的空间比例）
        total_obstacle_volume = sum(4/3 * np.pi * obs['radius']**3 for obs in scenario_data['obstacles'])
        space_volume = 100 * 100 * 100  # 环境体积
        space_utilization = total_obstacle_volume / space_volume
        complexity_score += space_utilization * 100

        # 4. 路径复杂度（基于episode表现）
        if episode_stats['success']:
            # 成功但步数多说明路径复杂
            complexity_score += (episode_stats['step_count'] / 500) * 20
        else:
            # 失败增加复杂度
            complexity_score += 30

        # 5. 最小障碍物距离（越小越复杂）
        if episode_stats['min_obstacle_distance'] < 10:
            complexity_score += (10 - episode_stats['min_obstacle_distance']) * 3

        # 6. 动态障碍物（如果有）
        if 'dynamic_obstacles' in scenario_data:
            num_dynamic = len(scenario_data['dynamic_obstacles'])
            complexity_score += num_dynamic * 15

        return complexity_score

    def _calculate_stage_results(self, stage_config, stage_data, training_time):
        """计算阶段结果"""
        results = {
            'random_phase': {
                'episodes': stage_config['random_episodes'],
                'success_rate': stage_data['random_phase']['success_count'] / stage_config['random_episodes'],
                'avg_reward': np.mean(stage_data['random_phase']['rewards']),
                'avg_complexity': np.mean(stage_data['random_phase']['scenario_complexities']),
                'final_10_success_rate': sum(1 for i in range(-10, 0)
                                           if i < 0 and abs(i) <= len(stage_data['random_phase']['rewards'])
                                           and stage_data['random_phase']['success_count'] > abs(i)) / min(10, len(stage_data['random_phase']['rewards']))
            },
            'fixed_phase': {
                'episodes': stage_config['fixed_episodes'],
                'success_rate': stage_data['fixed_phase']['success_count'] / stage_config['fixed_episodes'],
                'avg_reward': np.mean(stage_data['fixed_phase']['rewards']),
                'scenario_complexity': stage_data['fixed_phase']['scenario_complexity'],
                'final_10_success_rate': sum(1 for i in range(-10, 0)
                                           if i < 0 and abs(i) <= len(stage_data['fixed_phase']['rewards'])
                                           and stage_data['fixed_phase']['success_count'] > abs(i)) / min(10, len(stage_data['fixed_phase']['rewards']))
            },
            'overall': {
                'total_episodes': stage_config['total_episodes'],
                'overall_success_rate': (stage_data['random_phase']['success_count'] + stage_data['fixed_phase']['success_count']) / stage_config['total_episodes'],
                'training_time': training_time,
                'improvement': stage_data['fixed_phase']['success_count'] / stage_config['fixed_episodes'] -
                              stage_data['random_phase']['success_count'] / stage_config['random_episodes']
            },
            'phase_transition': stage_data['phase_transition_data'],
            'visualization_count': len(stage_data['visualization_episodes'])
        }

        return results

    def _generate_3d_trajectory_plot(self, env, trajectory, episode, success, phase_name):
        """生成3D轨迹图"""
        try:
            import matplotlib.pyplot as plt
            from mpl_toolkits.mplot3d import Axes3D

            fig = plt.figure(figsize=(12, 10))
            ax = fig.add_subplot(111, projection='3d')

            # 绘制起点和终点
            start = env.start
            goal = env.goal
            ax.scatter(start[0], start[1], start[2], c='green', s=200, marker='o', label='Start', alpha=0.8)
            ax.scatter(goal[0], goal[1], goal[2], c='red', s=200, marker='*', label='Goal', alpha=0.8)

            # 绘制静态障碍物
            for obs in env.obstacles:
                center = obs['center']
                radius = obs['radius']
                u = np.linspace(0, 2 * np.pi, 20)
                v = np.linspace(0, np.pi, 20)
                x = radius * np.outer(np.cos(u), np.sin(v)) + center[0]
                y = radius * np.outer(np.sin(u), np.sin(v)) + center[1]
                z = radius * np.outer(np.ones(np.size(u)), np.cos(v)) + center[2]
                ax.plot_surface(x, y, z, alpha=0.3, color='gray')

            # 绘制动态障碍物
            for obs in env.dynamic_obstacles:
                center = obs['center']
                radius = obs['radius']
                u = np.linspace(0, 2 * np.pi, 20)
                v = np.linspace(0, np.pi, 20)
                x = radius * np.outer(np.cos(u), np.sin(v)) + center[0]
                y = radius * np.outer(np.sin(u), np.sin(v)) + center[1]
                z = radius * np.outer(np.ones(np.size(u)), np.cos(v)) + center[2]
                ax.plot_surface(x, y, z, alpha=0.4, color='orange')

            # 绘制轨迹
            if trajectory and len(trajectory) > 1:
                positions = np.array(trajectory)

                if success:
                    color = 'blue'
                    alpha = 0.8
                    linewidth = 3
                    label = f'Episode {episode+1} - Success'
                else:
                    color = 'red'
                    alpha = 0.7
                    linewidth = 2
                    label = f'Episode {episode+1} - Failed'

                ax.plot(positions[:, 0], positions[:, 1], positions[:, 2],
                       color=color, alpha=alpha, linewidth=linewidth, label=label)

                # 标记轨迹起点和终点
                ax.scatter(positions[0, 0], positions[0, 1], positions[0, 2],
                          c='lightgreen', s=100, marker='o', alpha=0.8)
                ax.scatter(positions[-1, 0], positions[-1, 1], positions[-1, 2],
                          c='lightcoral', s=100, marker='x', alpha=0.8)

            # 设置图形属性
            ax.set_xlabel('X (m)')
            ax.set_ylabel('Y (m)')
            ax.set_zlabel('Z (m)')
            ax.set_xlim(0, 100)
            ax.set_ylim(0, 100)
            ax.set_zlim(0, 100)
            ax.grid(True, alpha=0.3)
            ax.legend()

            # 设置标题
            ax.set_title(f'{phase_name} - Episode {episode+1} - {"Success" if success else "Failed"}',
                        fontsize=14, fontweight='bold')

            # 保存图片
            filename = f'{phase_name}_episode_{episode+1:03d}_3d_trajectory.png'
            filepath = os.path.join(self.output_dir, filename)
            plt.savefig(filepath, dpi=150, bbox_inches='tight')
            plt.close()

        except Exception as e:
            print(f"⚠️ 生成3D轨迹图失败: {e}")

    def _print_stage_summary(self, stage_results):
        """打印阶段总结"""
        print(f"   📊 随机探索阶段:")
        print(f"      • 成功率: {stage_results['random_phase']['success_rate']:.2%}")
        print(f"      • 平均奖励: {stage_results['random_phase']['avg_reward']:.1f}")
        print(f"      • 平均复杂度: {stage_results['random_phase']['avg_complexity']:.1f}")

        print(f"   📌 固定强化阶段:")
        print(f"      • 成功率: {stage_results['fixed_phase']['success_rate']:.2%}")
        print(f"      • 平均奖励: {stage_results['fixed_phase']['avg_reward']:.1f}")
        print(f"      • 场景复杂度: {stage_results['fixed_phase']['scenario_complexity']:.1f}")

        print(f"   📈 整体表现:")
        print(f"      • 总体成功率: {stage_results['overall']['overall_success_rate']:.2%}")
        print(f"      • 成功率提升: {stage_results['overall']['improvement']:.2%}")
        print(f"      • 训练时间: {stage_results['overall']['training_time']:.1f}秒")

    def _print_final_summary(self):
        """打印最终总结"""
        print(f"\n📋 增强分阶段训练总结")
        print("=" * 60)

        for stage_key, stage_result in self.training_results["stages"].items():
            if "error" not in stage_result:
                stage_num = stage_key.split('_')[1]
                print(f"阶段 {stage_num}:")
                print(f"  🎲 随机探索: 成功率 {stage_result['random_phase']['success_rate']:.2%}")
                print(f"  📌 固定强化: 成功率 {stage_result['fixed_phase']['success_rate']:.2%}")
                print(f"  📈 成功率提升: {stage_result['overall']['improvement']:.2%}")
                print(f"  ⏱️ 训练时间: {stage_result['overall']['training_time']:.1f}秒")
            else:
                print(f"{stage_key}: 失败 - {stage_result['error']}")

        print(f"\n⏱️ 总训练时间: {self.training_results.get('total_training_time', 0):.1f}秒")
        print(f"📁 结果保存在: {self.output_dir}")

def main():
    import argparse

    parser = argparse.ArgumentParser(description='增强分阶段训练 - 简化ver1')
    parser.add_argument('--start-stage', type=int, default=1, choices=[1, 2, 3],
                       help='开始阶段 (1: 简单, 2: 复杂, 3: 动态)')
    parser.add_argument('--end-stage', type=int, default=3, choices=[1, 2, 3],
                       help='结束阶段 (1: 简单, 2: 复杂, 3: 动态)')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    parser.add_argument('--viz-interval', type=int, default=20, help='3D轨迹图生成间隔')

    args = parser.parse_args()

    # 执行增强分阶段训练
    trainer = EnhancedStagedTrainer(
        start_stage=args.start_stage,
        end_stage=args.end_stage,
        seed=args.seed,
        visualization_interval=args.viz_interval
    )

    results, final_controller = trainer.run_enhanced_staged_training()

    print("\n✅ 增强分阶段训练完成!")
    return results, final_controller

if __name__ == "__main__":
    main()
