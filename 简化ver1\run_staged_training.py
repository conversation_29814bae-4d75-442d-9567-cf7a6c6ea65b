"""
便捷的分阶段训练运行脚本 - 简化ver1
提供交互式界面，让用户轻松启动分阶段训练
支持两种训练模式：基础分阶段训练和增强分阶段训练
"""

import sys
import os
from datetime import datetime

def print_banner():
    """打印欢迎横幅"""
    print("🎯" + "=" * 58 + "🎯")
    print("🚀  DWA-RL 分阶段训练系统 - 简化ver1  🚀")
    print("🎯" + "=" * 58 + "🎯")
    print("📅 当前时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print()

def print_training_modes():
    """打印训练模式说明"""
    print("📋 可用训练模式:")
    print("=" * 50)
    print("1. 基础分阶段训练 (staged_training.py)")
    print("   • 三个阶段：简单 → 复杂 → 动态")
    print("   • 每阶段分为：随机探索 + 固定强化")
    print("   • 适合：快速验证分阶段训练效果")
    print()
    print("2. 增强分阶段训练 (enhanced_staged_trainer.py)")
    print("   • 智能场景选择：随机探索 → 固定强化")
    print("   • 场景复杂度评估和最优场景选择")
    print("   • 详细的训练统计和分析")
    print("   • 适合：深入研究和最佳性能")
    print()
    print("3. 显示环境配置")
    print("   • 查看所有可用的环境配置")
    print("   • 了解各阶段的训练参数")
    print()

def get_user_choice():
    """获取用户选择"""
    while True:
        try:
            choice = input("请选择训练模式 (1/2/3): ").strip()
            if choice in ['1', '2', '3']:
                return int(choice)
            else:
                print("❌ 无效选择，请输入 1、2 或 3")
        except KeyboardInterrupt:
            print("\n👋 用户取消操作")
            sys.exit(0)
        except Exception:
            print("❌ 输入错误，请重新输入")

def get_training_parameters():
    """获取训练参数"""
    print("\n⚙️ 训练参数配置:")
    print("=" * 30)
    
    try:
        # 开始阶段
        start_stage = input("开始阶段 (1-简单/2-复杂/3-动态) [默认: 1]: ").strip()
        start_stage = int(start_stage) if start_stage else 1
        if start_stage not in [1, 2, 3]:
            start_stage = 1
            print("⚠️ 无效输入，使用默认值: 1")
        
        # 结束阶段
        end_stage = input("结束阶段 (1-简单/2-复杂/3-动态) [默认: 3]: ").strip()
        end_stage = int(end_stage) if end_stage else 3
        if end_stage not in [1, 2, 3] or end_stage < start_stage:
            end_stage = 3
            print("⚠️ 无效输入，使用默认值: 3")
        
        # 随机种子
        seed = input("随机种子 [默认: 42]: ").strip()
        seed = int(seed) if seed else 42
        
        # 可视化间隔
        viz_interval = input("3D轨迹图生成间隔 [默认: 20]: ").strip()
        viz_interval = int(viz_interval) if viz_interval else 20
        
        return start_stage, end_stage, seed, viz_interval
        
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
        sys.exit(0)
    except Exception as e:
        print(f"⚠️ 参数输入错误: {e}")
        print("使用默认参数...")
        return 1, 3, 42, 20

def run_basic_staged_training(start_stage, end_stage, seed, viz_interval):
    """运行基础分阶段训练"""
    print("\n🚀 启动基础分阶段训练...")
    print("=" * 50)
    
    try:
        from staged_training import StagedTrainer
        
        trainer = StagedTrainer(
            start_stage=start_stage,
            end_stage=end_stage,
            seed=seed,
            visualization_interval=viz_interval
        )
        
        results = trainer.run_staged_training()
        
        print("\n✅ 基础分阶段训练完成!")
        return results
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保 staged_training.py 文件存在且可导入")
        return None
    except Exception as e:
        print(f"❌ 训练过程中出现错误: {e}")
        return None

def run_enhanced_staged_training(start_stage, end_stage, seed, viz_interval):
    """运行增强分阶段训练"""
    print("\n🚀 启动增强分阶段训练...")
    print("=" * 50)
    
    try:
        from enhanced_staged_trainer import EnhancedStagedTrainer
        
        trainer = EnhancedStagedTrainer(
            start_stage=start_stage,
            end_stage=end_stage,
            seed=seed,
            visualization_interval=viz_interval
        )
        
        results, final_controller = trainer.run_enhanced_staged_training()
        
        print("\n✅ 增强分阶段训练完成!")
        return results, final_controller
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保 enhanced_staged_trainer.py 文件存在且可导入")
        return None
    except Exception as e:
        print(f"❌ 训练过程中出现错误: {e}")
        return None

def show_environment_configs():
    """显示环境配置"""
    print("\n🌍 环境配置信息:")
    print("=" * 50)
    
    try:
        from environment_config import print_all_configs
        print_all_configs()
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保 environment_config.py 文件存在且可导入")
    except Exception as e:
        print(f"❌ 显示配置时出现错误: {e}")

def print_stage_info(start_stage, end_stage):
    """打印阶段信息"""
    stage_names = {
        1: "简单环境 (3-5个静态障碍物)",
        2: "复杂环境 (8-12个复杂静态障碍物)",
        3: "动态环境 (6-10个静态 + 2-4个动态障碍物)"
    }
    
    print(f"\n📋 训练计划:")
    print("=" * 30)
    for stage in range(start_stage, end_stage + 1):
        print(f"阶段 {stage}: {stage_names[stage]}")
    print()

def main():
    """主函数"""
    print_banner()
    print_training_modes()
    
    choice = get_user_choice()
    
    if choice == 3:
        show_environment_configs()
        
        # 询问是否继续训练
        print("\n" + "=" * 50)
        continue_choice = input("是否继续进行训练？(y/n) [默认: n]: ").strip().lower()
        if continue_choice not in ['y', 'yes']:
            print("👋 程序结束")
            return
        
        print_training_modes()
        choice = get_user_choice()
        if choice == 3:
            print("👋 程序结束")
            return
    
    # 获取训练参数
    start_stage, end_stage, seed, viz_interval = get_training_parameters()
    
    # 显示训练计划
    print_stage_info(start_stage, end_stage)
    
    # 确认开始训练
    print("🔍 训练配置确认:")
    print(f"  • 训练模式: {'基础分阶段训练' if choice == 1 else '增强分阶段训练'}")
    print(f"  • 训练阶段: {start_stage} 到 {end_stage}")
    print(f"  • 随机种子: {seed}")
    print(f"  • 可视化间隔: 每 {viz_interval} episodes")
    print()
    
    confirm = input("确认开始训练？(y/n) [默认: y]: ").strip().lower()
    if confirm in ['n', 'no']:
        print("👋 用户取消训练")
        return
    
    # 执行训练
    start_time = datetime.now()
    
    if choice == 1:
        results = run_basic_staged_training(start_stage, end_stage, seed, viz_interval)
    else:  # choice == 2
        results = run_enhanced_staged_training(start_stage, end_stage, seed, viz_interval)
    
    end_time = datetime.now()
    total_time = (end_time - start_time).total_seconds()
    
    # 显示最终结果
    print("\n" + "🎉" * 20)
    print("🎉  分阶段训练完成!  🎉")
    print("🎉" * 20)
    print(f"⏱️ 总耗时: {total_time:.1f} 秒")
    print(f"📅 完成时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    if results:
        print("\n💡 后续操作建议:")
        print("  • 查看生成的3D轨迹图了解训练过程")
        print("  • 分析JSON报告获取详细训练数据")
        print("  • 使用训练好的模型进行测试")
    
    print("\n👋 感谢使用DWA-RL分阶段训练系统!")

if __name__ == "__main__":
    main()
