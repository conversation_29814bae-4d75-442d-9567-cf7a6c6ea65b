"""
清晰的避障效果可视化
专门展示巡飞弹如何避开障碍物
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from loitering_munition_environment import LoiteringMunitionEnvironment
from loitering_munition_dwa import LoiteringMunitionDWA
from environment_config import get_environment_config

def run_clear_obstacle_test():
    """运行清晰的避障测试"""
    print('🚀 开始清晰避障效果测试...')

    # 创建环境
    env_config = get_environment_config('stage1_simple')
    env = LoiteringMunitionEnvironment(
        bounds=[2000, 2000, 2000],
        environment_config=env_config,
        reward_type='simplified'
    )

    # 创建DWA控制器
    dwa = LoiteringMunitionDWA(dt=0.1)

    # 重置环境
    obs = env.reset()
    print(f'起点: {env.start}')
    print(f'目标: {env.goal}')
    print(f'初始距离: {np.linalg.norm(env.start - env.goal):.2f}m')

    # 检查障碍物
    print(f'静态障碍物数量: {len(env.obstacles)}')
    if len(env.obstacles) > 0:
        print('静态障碍物详情:')
        for i, obs_info in enumerate(env.obstacles):
            center = obs_info['center']
            radius = obs_info['radius']
            print(f'  障碍物 {i+1}: 中心 [{center[0]:.1f}, {center[1]:.1f}, {center[2]:.1f}], 半径 {radius:.1f}m')

    # 记录轨迹数据
    trajectory = [env.state[:3].copy()]
    distances_to_goal = []
    distances_to_obstacles = []
    closest_obstacle_ids = []

    episode_reward = 0
    step = 0
    max_steps = 300  # 适中的步数

    print(f'\n开始避障测试 (最大步数: {max_steps})...')

    while step < max_steps:
        # 获取当前距离
        current_dist = np.linalg.norm(env.state[:3] - env.goal)
        distances_to_goal.append(current_dist)
        
        # 计算到最近障碍物的距离
        min_dist_to_obs = float('inf')
        closest_obs_id = -1
        for j, obstacle in enumerate(env.obstacles):
            dist_to_center = np.linalg.norm(env.state[:3] - obstacle['center'])
            dist_to_surface = dist_to_center - obstacle['radius']
            if dist_to_surface < min_dist_to_obs:
                min_dist_to_obs = dist_to_surface
                closest_obs_id = j
        
        distances_to_obstacles.append(max(0, min_dist_to_obs))  # 确保非负
        closest_obstacle_ids.append(closest_obs_id)
        
        # 使用DWA选择动作
        safe_controls = dwa.generate_safe_control_set(env.state, env.obstacles, env.goal, max_actions=5)
        if len(safe_controls) > 0:
            best_control = None
            best_score = -float('inf')
            
            for control in safe_controls:
                score = dwa.evaluate_control(control, env.state, env.goal, env.obstacles)
                if score > best_score:
                    best_score = score
                    best_control = control
            
            action = best_control if best_control is not None else safe_controls[0]
        else:
            action = [0, 0, 0]
        
        # 执行动作
        next_obs, reward, done, info = env.step(action)
        
        # 记录数据
        trajectory.append(env.state[:3].copy())
        episode_reward += reward
        
        # 打印关键信息
        if step % 30 == 0 or min_dist_to_obs < 20:
            print(f'步骤 {step+1:3d}: 位置 [{env.state[0]:.1f}, {env.state[1]:.1f}, {env.state[2]:.1f}], '
                  f'到目标 {current_dist:.1f}m, 到障碍物 {min_dist_to_obs:.1f}m (Obs{closest_obs_id+1})')
        
        step += 1
        
        if done:
            print(f'回合结束于步骤 {step}')
            break

    # 转换为numpy数组，确保长度一致
    trajectory = np.array(trajectory)
    distances_to_goal = np.array(distances_to_goal)
    distances_to_obstacles = np.array(distances_to_obstacles)

    # 确保数组长度一致（轨迹比距离多一个点）
    min_len = min(len(trajectory), len(distances_to_obstacles))
    trajectory = trajectory[:min_len]
    distances_to_goal = distances_to_goal[:min_len]
    distances_to_obstacles = distances_to_obstacles[:min_len]

    print(f'\n📊 避障效果统计:')
    print(f'总步数: {step}')
    print(f'最小障碍物距离: {np.min(distances_to_obstacles):.2f}m')
    print(f'平均障碍物距离: {np.mean(distances_to_obstacles):.2f}m')
    print(f'安全飞行: {"是" if np.min(distances_to_obstacles) >= 5.0 else "否"}')
    print(f'距离改善: {distances_to_goal[0] - distances_to_goal[-1]:.2f}m')

    # 生成清晰的可视化
    generate_clear_visualization(env, trajectory, distances_to_goal, distances_to_obstacles, step)

def generate_clear_visualization(env, trajectory, distances_to_goal, distances_to_obstacles, step_count):
    """生成清晰的避障可视化"""
    
    # 创建大图
    fig = plt.figure(figsize=(20, 12))
    
    # 3D轨迹图 - 使用点和线条，不用复杂的球体
    ax1 = fig.add_subplot(231, projection='3d')
    
    # 绘制轨迹 - 用颜色表示到障碍物的距离
    colors = plt.cm.RdYlGn(np.clip(distances_to_obstacles / 100, 0, 1))  # 红色=危险，绿色=安全
    
    for i in range(len(trajectory)-1):
        ax1.plot(trajectory[i:i+2, 0], trajectory[i:i+2, 1], trajectory[i:i+2, 2], 
                color=colors[i], linewidth=3, alpha=0.8)
    
    # 标记关键点
    ax1.scatter(*env.start, color='green', s=200, marker='o', label='Start', 
               edgecolors='black', linewidth=2)
    ax1.scatter(*env.goal, color='red', s=200, marker='*', label='Target', 
               edgecolors='black', linewidth=2)
    ax1.scatter(*trajectory[-1], color='blue', s=150, marker='X', label='End', 
               edgecolors='black', linewidth=2)
    
    # 绘制障碍物 - 使用简单的散点和标签
    obstacle_colors = ['orange', 'yellow', 'purple', 'brown', 'pink']
    for i, obstacle in enumerate(env.obstacles):
        center = obstacle['center']
        radius = obstacle['radius']
        color = obstacle_colors[i % len(obstacle_colors)]
        
        # 障碍物中心点
        ax1.scatter(*center, color=color, s=300, marker='s', alpha=0.8, 
                   edgecolors='black', linewidth=2)
        
        # 障碍物标签
        ax1.text(center[0], center[1], center[2] + radius + 30, 
                f'Obs{i+1}\\nr={radius:.0f}m', fontsize=12, ha='center', weight='bold',
                bbox=dict(boxstyle="round,pad=0.5", facecolor=color, alpha=0.8, edgecolor='black'))
    
    ax1.set_xlabel('X (m)', fontsize=12)
    ax1.set_ylabel('Y (m)', fontsize=12)
    ax1.set_zlabel('Z (m)', fontsize=12)
    ax1.set_title('3D Trajectory (colored by obstacle distance)', fontsize=14, weight='bold')
    ax1.legend(fontsize=12)
    
    # XY平面投影 - 重点展示避障路径
    ax2 = fig.add_subplot(232)
    
    # 绘制轨迹
    scatter = ax2.scatter(trajectory[:, 0], trajectory[:, 1], 
                         c=distances_to_obstacles, cmap='RdYlGn', s=30, alpha=0.8)
    ax2.plot(trajectory[:, 0], trajectory[:, 1], 'b-', linewidth=2, alpha=0.6)
    
    # 起点和目标
    ax2.scatter(*env.start[:2], color='green', s=150, marker='o', label='Start', 
               edgecolors='black', linewidth=2)
    ax2.scatter(*env.goal[:2], color='red', s=150, marker='*', label='Target', 
               edgecolors='black', linewidth=2)
    
    # 绘制障碍物 - 清晰的圆形
    for i, obstacle in enumerate(env.obstacles):
        center = obstacle['center']
        radius = obstacle['radius']
        color = obstacle_colors[i % len(obstacle_colors)]
        
        # 填充圆形
        circle_fill = plt.Circle(center[:2], radius, fill=True, color=color, alpha=0.4)
        ax2.add_patch(circle_fill)
        
        # 边界圆形
        circle_edge = plt.Circle(center[:2], radius, fill=False, color=color, 
                               linewidth=3, alpha=0.9)
        ax2.add_patch(circle_edge)
        
        # 中心点
        ax2.scatter(*center[:2], color=color, s=100, marker='s', 
                   edgecolors='black', linewidth=2)
        
        # 标签
        ax2.text(center[0], center[1], f'Obs{i+1}\\nr={radius:.0f}m', 
                fontsize=11, ha='center', va='center', weight='bold',
                bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9, 
                         edgecolor=color, linewidth=2))
    
    ax2.set_xlabel('X (m)', fontsize=12)
    ax2.set_ylabel('Y (m)', fontsize=12)
    ax2.set_title('XY Plane - Obstacle Avoidance Path', fontsize=14, weight='bold')
    ax2.legend(fontsize=12)
    ax2.grid(True, alpha=0.3)
    ax2.set_aspect('equal')
    
    # 颜色条
    cbar = plt.colorbar(scatter, ax=ax2)
    cbar.set_label('Distance to Nearest Obstacle (m)', fontsize=12)
    
    # 到目标距离变化
    ax3 = fig.add_subplot(233)
    ax3.plot(range(len(distances_to_goal)), distances_to_goal, 'r-', linewidth=3)
    ax3.set_xlabel('Step', fontsize=12)
    ax3.set_ylabel('Distance to Target (m)', fontsize=12)
    ax3.set_title('Progress Toward Target', fontsize=14, weight='bold')
    ax3.grid(True, alpha=0.3)
    
    # 到障碍物距离变化
    ax4 = fig.add_subplot(234)
    ax4.plot(range(len(distances_to_obstacles)), distances_to_obstacles, 'orange', linewidth=3)
    ax4.axhline(y=5.0, color='red', linestyle='--', linewidth=2, alpha=0.7, label='Safety Threshold')
    ax4.set_xlabel('Step', fontsize=12)
    ax4.set_ylabel('Distance to Nearest Obstacle (m)', fontsize=12)
    ax4.set_title('Obstacle Avoidance Safety', fontsize=14, weight='bold')
    ax4.legend(fontsize=12)
    ax4.grid(True, alpha=0.3)
    
    # 安全性分析
    ax5 = fig.add_subplot(235)
    safety_zones = ['Collision (<0m)', 'Danger (0-5m)', 'Caution (5-20m)', 'Safe (>20m)']
    safety_counts = [
        np.sum(distances_to_obstacles < 0),
        np.sum((distances_to_obstacles >= 0) & (distances_to_obstacles < 5)),
        np.sum((distances_to_obstacles >= 5) & (distances_to_obstacles < 20)),
        np.sum(distances_to_obstacles >= 20)
    ]
    colors_safety = ['red', 'orange', 'yellow', 'green']
    
    bars = ax5.bar(safety_zones, safety_counts, color=colors_safety, alpha=0.7, 
                   edgecolor='black', linewidth=2)
    ax5.set_ylabel('Number of Steps', fontsize=12)
    ax5.set_title('Safety Zone Distribution', fontsize=14, weight='bold')
    ax5.tick_params(axis='x', rotation=45)
    
    # 添加数值标签
    for bar, count in zip(bars, safety_counts):
        if count > 0:
            ax5.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1, 
                    str(count), ha='center', va='bottom', fontsize=11, weight='bold')
    
    # 轨迹统计
    ax6 = fig.add_subplot(236)
    stats_text = f'''
轨迹统计信息:

总步数: {step_count}
起点: [{env.start[0]:.0f}, {env.start[1]:.0f}, {env.start[2]:.0f}]
目标: [{env.goal[0]:.0f}, {env.goal[1]:.0f}, {env.goal[2]:.0f}]

距离改善: {distances_to_goal[0] - distances_to_goal[-1]:.1f}m
改善百分比: {(distances_to_goal[0] - distances_to_goal[-1])/distances_to_goal[0]*100:.1f}%

最小障碍物距离: {np.min(distances_to_obstacles):.2f}m
平均障碍物距离: {np.mean(distances_to_obstacles):.1f}m

安全飞行: {"✅ 是" if np.min(distances_to_obstacles) >= 5.0 else "❌ 否"}
避障成功: {"✅ 是" if np.min(distances_to_obstacles) >= 0 else "❌ 否"}
'''
    
    ax6.text(0.05, 0.95, stats_text, transform=ax6.transAxes, fontsize=12,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle="round,pad=0.5", facecolor='lightblue', alpha=0.8))
    ax6.set_xlim(0, 1)
    ax6.set_ylim(0, 1)
    ax6.axis('off')
    ax6.set_title('Mission Statistics', fontsize=14, weight='bold')
    
    # 总标题
    success_text = "SUCCESS" if np.min(distances_to_obstacles) >= 0 else "COLLISION"
    safety_text = "SAFE" if np.min(distances_to_obstacles) >= 5.0 else "RISKY"
    
    fig.suptitle(f'Obstacle Avoidance Analysis - {success_text} & {safety_text}\\n'
                f'Steps: {step_count}, Min Obstacle Distance: {np.min(distances_to_obstacles):.2f}m', 
                fontsize=18, fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('clear_obstacle_avoidance_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f'✅ 清晰避障分析图已保存: clear_obstacle_avoidance_analysis.png')

if __name__ == "__main__":
    run_clear_obstacle_test()
