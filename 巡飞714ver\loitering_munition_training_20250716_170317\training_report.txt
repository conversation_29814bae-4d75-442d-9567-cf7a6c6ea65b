巡飞弹分阶段训练报告
==================================================

训练时间: 2025-07-16 22:06:39
随机种子: 42

训练配置:
- 环境: 巡飞弹六自由度运动学环境
- 奖励函数: 三项奖励函数（距离+效率+安全）
- 网络架构: TD3（15维状态，3维控制）
- 训练策略: 分阶段渐进式训练

各阶段结果:

stage1:
  训练轮数: 500
  成功率: 11.0%
  平均奖励: 9899.84
  平均步数: 376.3
  训练时间: 7876.8秒

stage2:
  训练轮数: 1000
  成功率: 58.6%
  平均奖励: 14964.07
  平均步数: 425.3
  训练时间: 6774.5秒

stage3:
  训练轮数: 500
  成功率: 53.2%
  平均奖励: 15283.15
  平均步数: 447.8
  训练时间: 3548.9秒

总体结果:
  总训练时间: 18200.3秒
  总训练轮数: 2000
  总体成功率: 40.9%
