\documentclass[conference]{IEEEtran}
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
\usepackage{CJKutf8}
\usepackage[fleqn]{amsmath}
\usepackage{amssymb,amsfonts}
\usepackage{algorithm}
\usepackage{algorithmic}
\usepackage{graphicx}
\usepackage{textcomp}
\usepackage{xcolor}
\usepackage{tikz}
\usepackage{pgfplots}
\usepackage{subcaption}
\usepackage{float}
\usepackage{placeins}
\usetikzlibrary{shapes,arrows,positioning,3d}

\def\BibTeX{{\rm B\kern-.05em{\sc i\kern-.025em b}\kern-.08em
    T\kern-.1667em\lower.7ex\hbox{E}\kern-.125emX}}

\begin{document}
\begin{CJK}{UTF8}{gbsn}

\title{基于安全强化学习的巡飞弹约束运动规划}

\author{\IEEEauthorblockN{PYH}
\IEEEauthorblockA{\textit{BIT} \\
Beijing，China \\
<EMAIL>}}

\maketitle

\begin{abstract}
巡飞弹作为新型智能武器系统，可在复杂动态环境中执行精确的目标搜索和攻击任务，通常面临严格的运动约束和安全约束要求。传统路径规划方法缺乏环境适应性，而纯强化学习方法难以提供硬约束保证，存在安全风险。本文研究巡飞弹在运动约束下的智能决策学习问题，提出了一种基于安全强化学习的约束动态规划方法。该方法创新性地将DWA局部动态规划与TD3全局优化相结合：DWA层提供局部安全约束保证，直接生成满足运动约束的控制输入；TD3层实现全局策略优化，通过学习动态选择最优动作，克服了传统DWA方法局部最优和短视性的局限。这种局部安全性与全局最优性的有机结合，解决了约束动态规划中安全性与性能优化的根本矛盾。设计了分阶段约束学习策略，从简单静态环境到复杂动态环境渐进式训练。实验结果表明，该方法在训练中保持100\%约束满足率，制导成功率显著优于传统方法。本文的主要贡献如下：(1)提出了局部动态规划与全局优化相结合的安全强化学习架构；(2)构建了DWA引导的协同训练机制，实现安全探索与策略学习的有机结合；(3)建立了分阶段约束学习策略，实现了硬约束保证下的性能优化。
\end{abstract}

\begin{IEEEkeywords}
安全强化学习, 巡飞弹, 约束动态规划, 动态窗口法, TD3, 运动约束, 约束满足学习
\end{IEEEkeywords}

\section{引言}

\subsection{研究背景与重要性}

巡飞弹作为一种新型智能武器，是现代军事技术向自主化、智能化发展的重要代表。与传统制导武器相比，巡飞弹具备滞空时间长、自主搜索能力强、多弹协同精确打击等能力，在现代战争中发挥着越来越重要的作用[Karaman \& Frazzoli, 2011]。巡飞弹需要在复杂动态环境中执行精确的目标搜索和攻击任务，面临多重技术挑战：(1)\textbf{复杂地形适应}：需要在城市建筑群、山地峡谷、森林密布等不同地形环境中自主导航；(2)\textbf{动态威胁规避}：必须实时避开敌方防空火力、移动障碍物和其他飞行器；(3)\textbf{任务时效性}：在有限的续航时间内完成目标搜索、识别和精确打击；(4)\textbf{通信受限}：在电磁干扰或通信中断情况下保持自主决策能力。

这些应用需求对巡飞弹的动态规划算法提出了严格要求：既要满足严格的运动约束（速度、加速度、转弯半径限制）和安全约束（碰撞避免、禁飞区限制），又要具备智能学习和环境适应能力。然而，现有方法面临局部规划与全局优化的根本矛盾：局部动态规划方法（如DWA）虽能提供实时安全保证，但容易陷入局部最优，缺乏长期全局视野；全局优化方法（如强化学习）虽具备学习和适应能力，但难以提供硬约束保证。如何将局部安全性与全局最优性有机结合，成为约束动态规划领域的核心挑战，其解决方案直接影响巡飞弹的作战效能和安全性[LaValle, 2006]。

\subsection{领域发展历程}

约束动态规划领域的发展可以追溯到20世纪50年代Bellman提出的动态规划理论[Bellman, 1957]。随着计算能力的提升和应用需求的增长，该领域经历了几个重要的发展阶段：

\textbf{经典动态规划阶段（1950s-1980s）}：以Bellman方程为核心的理论框架奠定了动态规划的数学基础。Pontryagin等人提出的最优控制理论[Pontryagin et al., 1962]为约束优化问题提供了理论支撑。然而，这一阶段的方法主要局限于低维问题，难以处理高维状态空间和复杂约束。

\textbf{启发式算法阶段（1980s-2000s）}：为解决维数灾难问题，研究者开始探索启发式算法。A*算法[Hart et al., 1968]和RRT算法[LaValle, 1998]的提出标志着路径规划算法的重要突破。这一阶段的代表性工作包括Dijkstra算法的扩展[Dijkstra, 1959]和基于采样的规划方法[Kavraki et al., 1996]。

\textbf{智能优化阶段（2000s-2010s）}：机器学习技术的兴起为动态规划带来了新的解决思路。强化学习方法开始应用于路径规划问题[Sutton \& Barto, 1998]。Q-learning[Watkins \& Dayan, 1992]和策略梯度方法[Williams, 1992]为处理连续状态空间提供了有效途径。

\textbf{深度强化学习阶段（2010s-至今）}：深度学习的突破性进展推动了深度强化学习的快速发展[Mnih et al., 2015]。DQN、DDPG、TD3等算法的提出[Lillicrap et al., 2016; Fujimoto et al., 2018]使得强化学习能够处理高维连续控制问题，为复杂约束动态规划提供了新的解决方案。

\subsection{主要研究方向}

当前约束动态规划领域主要包含以下几个研究方向：

\textbf{传统路径规划方法}：以几何算法和图搜索为核心的经典方法。代表性算法包括A*算法[Hart et al., 1968]、RRT算法[LaValle, 1998]和PRM算法[Kavraki et al., 1996]。这类方法的优势在于理论完备性和确定性保证，能够提供最优解或可行解。然而，其局限性在于计算复杂度高、缺乏环境适应性，难以处理动态环境和不确定性[Choset et al., 2005]。

\textbf{基于优化的方法}：将路径规划问题建模为约束优化问题，采用数值优化算法求解。模型预测控制（MPC）[Camacho \& Alba, 2013]和轨迹优化方法[Betts, 1998]是这一方向的代表。这类方法能够处理复杂约束和目标函数，但计算开销大，实时性有限。

\textbf{强化学习方法}：通过与环境交互学习最优策略的方法。从早期的表格型Q-learning[Watkins \& Dayan, 1992]发展到现代的深度强化学习[Mnih et al., 2015]，这类方法具有强大的环境适应性和学习能力。DDPG[Lillicrap et al., 2016]、TD3[Fujimoto et al., 2018]、SAC[Haarnoja et al., 2018]等算法在连续控制问题上取得了显著成功。然而，纯强化学习方法难以提供硬约束保证，存在安全风险。

\textbf{安全强化学习方法}：致力于在学习过程中保证安全约束满足的新兴方向。约束策略优化（CPO）[Achiam et al., 2017]、安全Actor-Critic[Chow et al., 2017]等方法通过引入约束项或安全机制来确保学习过程的安全性。然而，这些方法通常只能提供概率性安全保证，无法满足安全关键系统的严格要求。

\textbf{混合方法}：结合多种方法优势的融合架构。分层强化学习[Barto \& Mahadevan, 2003]、引导强化学习[Nair et al., 2018]等方法试图在保持学习能力的同时提供更好的安全性和效率。

\subsection{关键问题与争议点}

当前约束动态规划研究中存在几个重要的争议点和未解决问题：

\textbf{安全性与性能的权衡问题}：这是该领域最核心的争议之一。传统方法支持者认为，安全关键系统必须采用确定性算法以保证绝对安全[Alur et al., 2015]。他们主张使用经过验证的经典算法，如A*或RRT，即使牺牲一定的性能也要确保安全性。相反，强化学习支持者认为，现代复杂环境下的最优性能只能通过学习获得[Silver et al., 2016]，传统方法的保守性会严重限制系统性能。安全强化学习研究者则试图在两者之间寻找平衡[García \& Fernández, 2015]，但目前尚未形成统一的理论框架。

\textbf{约束处理机制的有效性争议}：关于如何在强化学习中处理硬约束存在根本性分歧。软约束方法的支持者认为，通过惩罚函数和拉格朗日乘数法可以有效处理约束[Achiam et al., 2017]，这种方法具有理论优雅性和实现简便性。硬约束方法的支持者则认为，安全关键系统不能容忍任何约束违反[Alshiekh et al., 2018]，必须采用投影或过滤机制确保约束严格满足。混合方法的支持者提出分层架构[Andreas et al., 2017]，但其复杂性和计算开销成为争议焦点。

\textbf{环境建模与泛化能力的矛盾}：环境建模的精确性与算法泛化能力之间存在根本矛盾。精确建模派认为，准确的环境模型是可靠规划的前提[Thrun et al., 2005]，主张采用详细的物理模型和传感器融合技术。简化建模派则认为，过度复杂的模型会降低算法的泛化能力和实时性[Kober et al., 2013]，主张采用简化的环境表示。端到端学习派更进一步，认为应该让算法直接从原始感知数据学习[Levine et al., 2016]，避免人工建模的偏差。

\subsection{研究空白与未来展望}

尽管约束动态规划领域取得了显著进展，但仍存在重要的研究空白：

\textbf{理论空白}：缺乏统一的理论框架来描述约束满足与性能优化的关系。现有的安全强化学习理论主要基于概率性保证[Chow et al., 2017]，无法为安全关键系统提供确定性安全证明。需要发展新的理论工具来分析混合系统的安全性和最优性[Sontag, 2013]。

\textbf{方法空白}：现有方法在处理动态约束和时变环境方面能力有限。大多数算法假设约束是静态的或缓慢变化的[Berkenkamp et al., 2017]，难以应对快速变化的威胁环境。需要开发能够实时适应约束变化的自适应算法。

\textbf{应用空白}：缺乏针对特定应用领域的专门化方法。巡飞弹等军用系统具有独特的约束特征和性能要求[Shima \& Rasmussen, 2009]，通用算法难以充分利用领域知识。需要发展领域特定的优化方法和安全机制。

基于最新文献趋势分析，未来发展方向可能包括：(1)基于形式化验证的安全强化学习[Seshia et al., 2018]；(2)多智能体协同约束规划[Tampuu et al., 2017]；(3)基于数字孪生的实时约束优化[Grieves, 2014]；(4)量子计算在约束优化中的应用[Biamonte et al., 2017]。

\subsection{本研究的创新定位}

综合上述文献分析，本文针对现有研究的不足，提出了一种基于安全强化学习的巡飞弹约束动态规划方法。该方法的核心创新在于将DWA局部动态规划与TD3全局优化相结合，实现了局部安全性与全局最优性的统一，具有以下创新特点：

首先，本文创新性地解决了局部规划与全局优化的根本矛盾。传统DWA方法虽然能提供实时安全保证，但其局部动态规划特性导致容易陷入局部最优，缺乏长期全局规划能力。本文通过引入TD3全局优化机制，使系统能够通过学习动态选择最优动作，在保持DWA局部安全约束的同时，实现全局路径的智能优化。这种局部-全局结合的设计突破了传统方法的局限性。

其次，本文构建的DWA引导协同训练机制实现了安全探索与全局学习的有机统一。DWA层作为局部安全约束器，确保每个时刻的动作都满足运动约束和安全约束；TD3层作为全局策略优化器，通过与环境的长期交互学习，获得超越局部贪心选择的全局最优策略。这种分层协同设计既保证了即时安全性，又实现了长期性能优化。

最后，本文建立的分阶段约束学习策略为复杂动态环境下的全局优化提供了可行路径。通过渐进式复杂度提升，TD3能够在安全约束下逐步学习适应不同环境的全局策略，克服了传统强化学习在约束环境中的探索困难和收敛问题。

本研究通过局部动态规划与全局优化的深度融合，为约束动态规划领域提供了新的理论视角和方法框架，解决了安全关键系统中安全性与最优性难以兼得的根本问题，为巡飞弹等智能武器系统的自主决策提供了重要的技术突破。

\section{运动学模型与建模、场景建模与问题}

\subsection{巡飞弹六自由度质点运动学模型}

考虑到巡飞弹的飞行特性，本文采用六自由度质点模型，忽略滚转角影响。巡飞弹状态向量定义为：
\begin{equation}
\mathbf{s} = [x, y, z, V, \gamma, \psi]^T
\end{equation}

其中：
\begin{itemize}
\item $(x, y, z)$：巡飞弹在地面坐标系中的位置坐标 [m]
\item $V$：巡飞弹速度大小 [m/s]
\item $\gamma$：航迹倾斜角（俯仰角）[rad]
\item $\psi$：航迹偏航角（方位角）[rad]
\end{itemize}

图\ref{fig:kinematics}展示了巡飞弹六自由度质点运动学模型的三维坐标系示意图。该图清晰地描述了巡飞弹在地面坐标系中的位置、速度向量以及关键角度参数的几何关系。

\begin{figure}[htbp]
\centering
\begin{tikzpicture}[x={(1.2cm,0cm)}, y={(0.6cm,0.6cm)}, z={(0cm,1.2cm)}, scale=1.1]
    % 地面参考平面 (最先绘制，作为背景)
    \draw[fill=gray!8, opacity=0.4] (0,0,0) -- (4.5,0,0) -- (4.5,4.5,0) -- (0,4.5,0) -- cycle;
    \node at (4,4,0) [anchor=center, gray] {\footnotesize 地面参考平面};

    % 坐标轴 (加粗并调整颜色)
    \draw[very thick,black,->] (0,0,0) -- (4.8,0,0) node[anchor=north east, font=\large]{$x$};
    \draw[very thick,black,->] (0,0,0) -- (0,4.8,0) node[anchor=north west, font=\large]{$y$};
    \draw[very thick,black,->] (0,0,0) -- (0,0,4) node[anchor=south, font=\large]{$z$};

    % 原点标记
    \node at (0,0,0) [anchor=north east, font=\large] {$O$};

    % 巡飞弹位置 (调整到更合适的位置)
    \coordinate (missile) at (2.8,2.4,2.2);
    \fill[red!80] (missile) circle (3pt);
    \node at (missile) [anchor=south west, font=\small, red!80] {\textbf{巡飞弹}};

    % 位置向量 (调整颜色和粗细)
    \draw[very thick,blue!70] (0,0,0) -- (missile);
    \node at (1.4,1.2,1.1) [anchor=south east, font=\small, blue!70] {$\mathbf{r}(x,y,z)$};

    % 在xy平面的投影点
    \coordinate (xy_proj) at (2.8,2.4,0);
    \fill[blue!50] (xy_proj) circle (1.5pt);
    
    % 投影线 (使用虚线，降低透明度)
    \draw[dashed,gray!60, thick] (missile) -- (xy_proj);
    \draw[dashed,blue!40, thick] (0,0,0) -- (xy_proj);

    % 坐标标注线 (分离并调整)
    \draw[dotted,gray!50] (missile) -- (2.8,0,0);
    \draw[dotted,gray!50] (missile) -- (0,2.4,0);
    \draw[dotted,gray!50] (missile) -- (0,0,2.2);

    % 坐标值标注
    \node at (2.8,0,0) [anchor=north, font=\footnotesize] {$x$};
    \node at (0,2.4,0) [anchor=east, font=\footnotesize] {$y$};
    \node at (0,0,2.2) [anchor=east, font=\footnotesize] {$z$};

    % 速度向量 (重新定位，避免重叠)
    \coordinate (vel_end) at (4.0,3.2,2.8);
    \draw[very thick,red!70,->] (missile) -- (vel_end);
    \node at (vel_end) [anchor=south west, font=\small, red!70] {$\mathbf{V}$};

    % 速度在xy平面的投影
    \coordinate (vel_xy_proj) at (4.0,3.2,0);
    \draw[dashed,red!40, thick] (vel_end) -- (vel_xy_proj);
    \draw[dashed,red!40, thick] (xy_proj) -- (vel_xy_proj);

    % 航迹倾斜角 γ (重新设计，更清晰)
    \coordinate (gamma_ref) at (3.4,2.8,2.2); % 水平参考线端点
    \draw[thick,green!70] (missile) -- (gamma_ref); % 水平参考线
    \draw[thick,green!70] (missile) -- (3.4,2.8,2.6); % 速度方向线
    \draw[green!70, thick] (3.2,2.6,2.2) arc[start angle=0, end angle=20, radius=0.3];
    \node at (3.6,2.9,2.5) [anchor=west, font=\small, green!70] {$\gamma$};

    % 航迹偏航角 ψ (在xy平面内，重新定位)
    \coordinate (psi_ref) at (2.8,1.0,0); % x轴方向参考
    \draw[thick,purple!70] (xy_proj) -- (psi_ref); % x轴方向参考线
    \draw[thick,purple!70] (xy_proj) -- (vel_xy_proj); % 投影速度方向
    \draw[purple!70, thick] (2.8,1.6,0) arc[start angle=90, end angle=45, radius=0.6];
    \node at (2.4,1.8,0) [anchor=center, font=\small, purple!70] {$\psi$};

    % 控制输入示意 (重新布局，避免重叠)
    % 切向加速度
    \coordinate (at_start) at (missile);
    \coordinate (at_end) at (3.6,3.0,2.6);
    \draw[very thick,orange!80,->] (at_start) -- (at_end);
    \node at (at_end) [anchor=south west, font=\small, orange!80] {$a_T$};

    % 法向加速度 (调整方向，避免与其他线重叠)
    \coordinate (an_start) at (missile);
    \coordinate (an_end) at (2.2,3.2,2.8);
    \draw[very thick,cyan!80,->] (an_start) -- (an_end);
    \node at (an_end) [anchor=south east, font=\small, cyan!80] {$a_N$};

    % 倾斜角 μ 示意 (法向加速度在水平面的投影角，重新设计)
    \coordinate (mu_ref) at (2.8,3.0,2.2); % 参考方向
    \draw[thick,brown!70] (missile) -- (mu_ref);
    \draw[thick,brown!70] (missile) -- (2.2,3.2,2.2); % 投影方向
    \draw[brown!70, thick] (2.6,2.8,2.2) arc[start angle=0, end angle=30, radius=0.4];
    \node at (2.4,3.4,2.2) [anchor=center, font=\small, brown!70] {$\mu$};

\end{tikzpicture}
\caption{巡飞弹六自由度质点运动学模型三维坐标系示意图}
\label{fig:kinematics}
\end{figure}

巡飞弹运动学方程为：
\begin{align}
\dot{x} &= V \cos \gamma \cos \psi \\
\dot{y} &= V \cos \gamma \sin \psi \\
\dot{z} &= V \sin \gamma \\
\dot{V} &= a_T - g \sin \gamma \\
\dot{\gamma} &= \frac{a_N \cos \mu - g \cos \gamma}{V} \\
\dot{\psi} &= \frac{a_N \sin \mu}{V \cos \gamma}
\end{align}

其中：
\begin{itemize}
\item $a_T$：切向加速度（推力方向）[m/s²]
\item $a_N$：法向加速度（升力方向）[m/s²]
\item $\mu$：倾斜角（法向加速度在水平面的投影角）[rad]
\item $g = 9.81$ m/s²：重力加速度
\end{itemize}

\textbf{倾斜角$\mu$的物理意义}：倾斜角$\mu$是法向加速度$a_N$在水平面内的方向角，控制法向加速度在垂直运动和水平转向之间的分配。具体而言：
\begin{itemize}
\item 当$\mu = 0$时：$a_N \cos\mu = a_N$，$a_N \sin\mu = 0$，法向加速度完全用于俯仰运动（改变$\gamma$）
\item 当$\mu = \pi/2$时：$a_N \cos\mu = 0$，$a_N \sin\mu = a_N$，法向加速度完全用于偏航运动（改变$\psi$）
\item 当$\mu$为其他值时：法向加速度按$\cos\mu$和$\sin\mu$的比例同时影响俯仰和偏航运动
\end{itemize}

这种设计使得巡飞弹能够通过单一的法向加速度$a_N$和倾斜角$\mu$实现复杂的三维机动控制，相比于独立的俯仰和偏航控制，该方法更符合实际飞行器的物理约束和控制特性。

为了更清晰地理解倾斜角$\mu$的几何意义，图\ref{fig:mu_explanation}通过三个典型情况详细展示了$\mu$如何控制法向加速度的方向分配。

\begin{figure}[htbp]
\centering
\begin{tikzpicture}[scale=0.8]
    % 子图1：μ = 0° (纯俯仰)
    \begin{scope}[shift={(0,0)}]
        % 标题
        \node at (0,3.5) [anchor=center, font=\large] {\textbf{情况1：$\mu = 0°$ (纯俯仰)}};
        
        % 坐标系
        \draw[thick,->] (-1.5,0) -- (1.5,0) node[anchor=north] {$y$};
        \draw[thick,->] (0,-0.5) -- (0,2.5) node[anchor=east] {$z$};
        
        % 巡飞弹位置
        \fill[red] (0,0) circle (3pt);
        \node at (0,0) [anchor=north west] {\small 巡飞弹};
        
        % 速度向量（水平向右，纸面外）
        \draw[very thick,blue,->] (0,0) -- (0.8,0);
        \node at (0.8,0) [anchor=north] {\small $\mathbf{V}$};
        \node at (1.2,0) [anchor=west, font=\footnotesize] {(纸面外)};
        
        % 法向加速度（垂直向上）
        \draw[very thick,green,->] (0,0) -- (0,1.5);
        \node at (0,1.5) [anchor=south] {\small $a_N$};
        
        % 分量标注
        \node at (0.5,0.8) [anchor=west, font=\small, green] {$a_N \cos(0°) = a_N$};
        \node at (0.5,0.4) [anchor=west, font=\small, gray] {$a_N \sin(0°) = 0$};
        
        % 结果说明
        \node at (0,-1.2) [anchor=center, font=\small, fill=blue!10, rounded corners] 
              {\textbf{结果：只有俯仰运动}};
    \end{scope}
    
    % 子图2：μ = 45° (混合)
    \begin{scope}[shift={(6,0)}]
        % 标题
        \node at (0,3.5) [anchor=center, font=\large] {\textbf{情况2：$\mu = 45°$ (混合)}};
        
        % 坐标系
        \draw[thick,->] (-1.5,0) -- (1.5,0) node[anchor=north] {$y$};
        \draw[thick,->] (0,-0.5) -- (0,2.5) node[anchor=east] {$z$};
        
        % 巡飞弹位置
        \fill[red] (0,0) circle (3pt);
        \node at (0,0) [anchor=north west] {\small 巡飞弹};
        
        % 速度向量
        \draw[very thick,blue,->] (0,0) -- (0.8,0);
        \node at (0.8,0) [anchor=north] {\small $\mathbf{V}$};
        \node at (1.2,0) [anchor=west, font=\footnotesize] {(纸面外)};
        
        % 法向加速度（45度角）
        \draw[very thick,green,->] (0,0) -- (1.06,1.06);
        \node at (1.06,1.06) [anchor=south west] {\small $a_N$};
        
        % μ角度标注
        \draw[brown, thick] (0.4,0) arc[start angle=0, end angle=45, radius=0.4];
        \node at (0.6,0.25) [anchor=center, font=\small, brown] {$\mu=45°$};
        
        % 分量分解
        \draw[dashed,green] (1.06,1.06) -- (0,1.06);
        \draw[dashed,green] (1.06,1.06) -- (1.06,0);
        
        % 分量标注
        \node at (0.3,1.3) [anchor=center, font=\small, green] {$a_N \cos(45°)$};
        \node at (1.3,0.3) [anchor=center, font=\small, green] {$a_N \sin(45°)$};
        
        % 结果说明
        \node at (0,-1.2) [anchor=center, font=\small, fill=orange!10, rounded corners] 
              {\textbf{结果：俯仰+偏航运动}};
    \end{scope}
    
    % 子图3：μ = 90° (纯偏航)
    \begin{scope}[shift={(12,0)}]
        % 标题
        \node at (0,3.5) [anchor=center, font=\large] {\textbf{情况3：$\mu = 90°$ (纯偏航)}};
        
        % 坐标系
        \draw[thick,->] (-1.5,0) -- (1.5,0) node[anchor=north] {$y$};
        \draw[thick,->] (0,-0.5) -- (0,2.5) node[anchor=east] {$z$};
        
        % 巡飞弹位置
        \fill[red] (0,0) circle (3pt);
        \node at (0,0) [anchor=north west] {\small 巡飞弹};
        
        % 速度向量
        \draw[very thick,blue,->] (0,0) -- (0.8,0);
        \node at (0.8,0) [anchor=north] {\small $\mathbf{V}$};
        \node at (1.2,0) [anchor=west, font=\footnotesize] {(纸面外)};
        
        % 法向加速度（水平向右）
        \draw[very thick,green,->] (0,0) -- (1.5,0);
        \node at (1.5,0) [anchor=south] {\small $a_N$};
        
        % μ角度标注
        \draw[brown, thick] (0.4,0) arc[start angle=0, end angle=90, radius=0.4];
        \node at (0.2,0.6) [anchor=center, font=\small, brown] {$\mu=90°$};
        
        % 分量标注
        \node at (0.5,-0.4) [anchor=center, font=\small, gray] {$a_N \cos(90°) = 0$};
        \node at (0.8,0.4) [anchor=center, font=\small, green] {$a_N \sin(90°) = a_N$};
        
        % 结果说明
        \node at (0,-1.2) [anchor=center, font=\small, fill=red!10, rounded corners] 
              {\textbf{结果：只有偏航运动}};
    \end{scope}
    
    % 总体说明框
    \node at (6,-3) [anchor=center, font=\small, 
          fill=gray!8, draw=gray!60, line width=1pt,
          rounded corners=5pt, inner sep=8pt] {
        \begin{tabular}{c}
        \textbf{核心理解：}$\mu$控制法向加速度$a_N$的方向 \\[3pt]
        $\bullet$ \textcolor{green}{\textbf{垂直分量}}：$a_N \cos\mu$ → 影响俯仰角$\gamma$ \\[2pt]
        $\bullet$ \textcolor{green}{\textbf{水平分量}}：$a_N \sin\mu$ → 影响偏航角$\psi$ \\[3pt]
        \textcolor{brown}{\textbf{$\mu$就是"银行角"}} - 类似飞机转弯时的倾斜角度
        \end{tabular}
    };
\end{tikzpicture}
\caption{倾斜角$\mu$的几何意义：法向加速度的方向控制}
\label{fig:mu_explanation}
\end{figure}

控制输入向量为：
\begin{equation}
\mathbf{u} = [a_T, a_N, \mu]^T
\end{equation}

巡飞弹的运动约束集合$\mathcal{C}$基于实际飞行性能参数：

\textbf{速度约束}：
\begin{equation}
\mathcal{C}_{vel}: \quad V_{min} \leq V \leq V_{max}
\end{equation}
其中$V_{min}$为失速速度，$V_{max}$为最大飞行速度，具体数值在仿真实验部分给出。

\textbf{加速度约束}：
\begin{align}
\mathcal{C}_{acc}: \quad |a_T| &\leq a_{T,max} \\
|a_N| &\leq a_{N,max}
\end{align}

\textbf{角度约束}：
\begin{align}
\mathcal{C}_{angle}: \quad |\gamma| &\leq \gamma_{max} \\
|\dot{\gamma}| &\leq \dot{\gamma}_{max} \\
|\dot{\psi}| &\leq \dot{\psi}_{max}
\end{align}

\textbf{安全约束}：
\begin{align}
\mathcal{C}_{obs}: \quad d_{obs} &\geq d_{safe} \\
\mathcal{C}_{bound}: \quad \mathbf{p} &\in \mathcal{W}
\end{align}

其中$d_{obs}$为到最近障碍物的距离，$\mathcal{W}$为允许飞行区域。

\subsection{场景建模与问题描述}

\textbf{三维飞行环境建模}：
巡飞弹在三维空间$\mathcal{W} = [0, L_x] \times [0, L_y] \times [0, L_z]$中执行任务，环境包含静态障碍物集合$\mathcal{O}_{static}$和动态障碍物集合$\mathcal{O}_{dynamic}$。

\textbf{障碍物建模}：
\begin{itemize}
\item 静态障碍物：固定位置的立方体或球体，表示建筑物、地形等
\item 动态障碍物：具有预定轨迹的移动物体，表示其他飞行器或移动威胁
\end{itemize}

\textbf{任务目标}：
巡飞弹需要从起始位置$\mathbf{p}_{start}$安全到达目标位置$\mathbf{p}_{goal}$，同时满足：
\begin{itemize}
\item 运动约束：速度、加速度、角度限制
\item 安全约束：避免与障碍物碰撞，保持在飞行区域内
\item 性能要求：最小化飞行时间，优化燃料消耗
\end{itemize}

\textbf{约束动态规划问题表述}：
\begin{align}
\min_{\pi} \quad &J(\pi) = \mathbb{E}\left[\sum_{t=0}^{T} \gamma^t c_t\right] \\
\text{s.t.} \quad &\mathbf{s}_{t+1} = f(\mathbf{s}_t, \mathbf{u}_t) \\
&\mathbf{u}_t \in \mathcal{U}_{safe}(\mathbf{s}_t) \\
&\mathbf{s}_t \in \mathcal{S}_{valid} \\
&d(\mathbf{s}_t, \mathcal{O}) \geq d_{safe}
\end{align}

其中$J(\pi)$为累积代价函数，$f(\cdot)$为状态转移函数，$\mathcal{U}_{safe}$为安全控制输入集合，$\mathcal{S}_{valid}$为有效状态空间。

\section{安全强化学习架构网络设计}

\subsection{DWA-RL融合架构设计}

本文实现的DWA-RL融合架构创新性地将局部动态规划与全局优化相结合，解决了传统方法的根本局限性。该架构的核心思想是：DWA作为局部动态规划器提供即时安全保证，TD3作为全局优化器通过学习实现长期最优决策。这种设计突破了传统DWA方法的局部最优和短视性问题，同时保持了强化学习的环境适应性和全局优化能力。

\textbf{局部-全局协同设计}：DWA层负责局部安全约束，在每个决策时刻生成满足所有运动约束的安全控制输入集合；TD3层负责全局策略优化，通过学习动态选择最优动作，实现超越局部贪心的全局最优路径规划。

\textbf{直接控制输入生成}：DWA层直接生成安全的控制输入$[a_T, a_N, \mu]$（切向加速度、法向加速度、倾斜角），而不是传统的速度候选集。这种设计提供了更精确的运动控制能力，同时降低了计算复杂度。

\textbf{优化的计算效率}：通过合理设置DWA分辨率参数，系统在保证控制精度的同时实现了高效的安全控制输入生成，有利于实时控制和强化学习训练。

\textbf{智能初始状态计算}：系统能够自动计算指向目标的初始航迹角和俯仰角，避免了随机初始化导致的学习效率低下问题。

\textbf{核心架构设计}：

\textbf{直接控制输入生成}：DWA层直接生成安全的控制输入：
\begin{equation}
\mathbf{u} = [a_T, a_N, \mu]^T
\end{equation}
其中$a_T$为切向加速度，$a_N$为法向加速度，$\mu$为倾斜角。这种设计避免了传统方法中从速度空间到控制输入的转换步骤，提供了更直接和精确的运动控制能力。

\textbf{优化的DWA分辨率参数}：通过合理设置切向加速度、法向加速度和倾斜角的离散化分辨率，在保证控制精度的同时实现高效的安全控制输入生成。具体参数设置将在仿真实验部分详述。

\textbf{智能初始状态计算}：
\begin{align}
\psi_{initial} &= \arctan2(y_{goal} - y_{start}, x_{goal} - x_{start}) \\
\gamma_{initial} &= \arcsin\left(\frac{z_{goal} - z_{start}}{|\mathbf{d}|}\right) \\
\mathbf{s}_{initial} &= [x_{start}, y_{start}, z_{start}, V_{cruise}, \gamma_{initial}, \psi_{initial}]^T
\end{align}
其中$\mathbf{d} = \mathbf{p}_{goal} - \mathbf{p}_{start}$为起点到终点的向量，$|\mathbf{d}|$为距离。

\subsection{安全强化学习架构}

安全强化学习架构采用约束满足与策略优化分离的设计：

\textbf{安全约束层(DWA)}：作为硬约束保证机制，基于当前状态和动态窗口直接生成满足所有运动约束$\mathcal{C}$的安全控制输入集合：
\begin{equation}
\mathcal{U}_{safe}(\mathbf{s}) = \{\mathbf{u} | \mathbf{u} = [a_T, a_N, \mu]^T, \forall c \in \mathcal{C}: c(\mathbf{s}, \mathbf{u}) = \text{True}\}
\end{equation}

\textbf{策略学习层(TD3)}：通过与DWA生成的安全控制输入交互，学习最优策略$\pi^*: \mathbf{s} \rightarrow \mathcal{A}$\cite{td3}，其中动作$\mathbf{a}$通过映射函数转换为控制输入$\mathbf{u}$，优化长期累积奖励：
\begin{equation}
J = \mathbb{E}\left[\sum_{t=0}^{\infty} \gamma^t r_t\right]
\end{equation}

这种分离设计确保了安全性的绝对保证（约束层）与性能的持续优化（学习层）的有机统一，实现了约束满足与策略学习的解耦。

\subsection{安全约束层：动态窗口法实现}

作为安全约束层，DWA算法\cite{dwa}负责直接生成满足所有运动约束的可行控制输入集合。基于巡飞弹运动学模型的控制输入动态窗口计算为：

\textbf{切向加速度动态窗口}：
\begin{equation}
DW_{a_T} = [-a_{T,max}, a_{T,max}]
\end{equation}

\textbf{法向加速度动态窗口}：
\begin{equation}
DW_{a_N} = [-a_{N,max}, a_{N,max}]
\end{equation}

\textbf{倾斜角动态窗口}：
\begin{equation}
DW_\mu = [-\pi/2, \pi/2]
\end{equation}

控制输入在动态窗口内按设定分辨率进行离散化，具体分辨率参数在仿真实验部分给出。

约束满足验证函数：
\begin{align}
\text{ConstraintSat}(\mathbf{u}) &= \text{ControlConstraint}(\mathbf{u}) \land \text{TrajectoryConstraint}(\mathbf{u}) \nonumber \\
&\quad \land \text{CollisionFree}(\mathbf{u}) \land \text{BoundaryValid}(\mathbf{u})
\end{align}

其中碰撞避免约束基于轨迹预测：
\begin{equation}
\text{CollisionFree}(\mathbf{u}) = \min_{i} \{dist(traj(\mathbf{s}, \mathbf{u}), obs_i)\} \geq d_{safe}
\end{equation}

轨迹预测函数$traj(\mathbf{s}, \mathbf{u})$基于六自由度运动学模型，在设定的预测时间窗口内计算控制输入$\mathbf{u}$产生的轨迹。

基于巡飞弹运动学的DWA轨迹预测：
\begin{align}
x(t) &= x_0 + \int_0^t V(\tau) \cos \gamma(\tau) \cos \psi(\tau) d\tau \\
y(t) &= y_0 + \int_0^t V(\tau) \cos \gamma(\tau) \sin \psi(\tau) d\tau \\
z(t) &= z_0 + \int_0^t V(\tau) \sin \gamma(\tau) d\tau
\end{align}

其中运动状态按控制输入演化：
\begin{align}
V(t) &= V_0 + \int_0^t (a_T(\tau) - g \sin \gamma(\tau)) d\tau \\
\gamma(t) &= \gamma_0 + \int_0^t \frac{a_N(\tau) \cos \mu(\tau) - g \cos \gamma(\tau)}{V(\tau)} d\tau \\
\psi(t) &= \psi_0 + \int_0^t \frac{a_N(\tau) \sin \mu(\tau)}{V(\tau) \cos \gamma(\tau)} d\tau
\end{align}

预测时间窗口$t_{pred}$的设置确保DWA能够预见巡飞弹的机动轨迹并提前规避碰撞风险，具体数值在仿真实验部分确定。

安全控制输入集合生成：
\begin{equation}
\mathcal{U}_{safe} = \{\mathbf{u} \in DW | \text{ConstraintSat}(\mathbf{u}) = \text{True}\}
\end{equation}

\subsection{策略学习层：基于约束策略优化的TD3网络架构}

策略学习层基于约束策略优化（Constrained Policy Optimization, CPO）理论框架\cite{constrained_rl}，在安全控制输入约束集$\mathcal{U}_{safe}(\mathbf{s})$内进行策略学习。该层采用TD3（Twin Delayed Deep Deterministic Policy Gradient）算法\cite{td3}实现约束动作空间内的连续控制策略优化，通过深度神经网络逼近最优策略函数，同时保持与DWA安全层的协同工作。

Actor网络$\pi_\theta: \mathcal{S} \rightarrow \mathcal{A}$采用深度前馈神经网络结构，实现状态到动作的非线性映射。该网络通过三层全连接结构实现特征提取和动作生成，其前向传播过程表示为：
\begin{align}
\mathbf{h}_1 &= \text{ReLU}(\mathbf{W}_1 \mathbf{s} + \mathbf{b}_1) \\
\mathbf{h}_2 &= \text{ReLU}(\mathbf{W}_2 \mathbf{h}_1 + \mathbf{b}_2) \\
\pi_\theta(\mathbf{s}) &= a_{max} \cdot \tanh(\mathbf{W}_3 \mathbf{h}_2 + \mathbf{b}_3)
\end{align}

其中$\mathbf{W}_i, \mathbf{b}_i$为网络参数，$a_{max}$为动作空间归一化因子，$\tanh$激活函数确保输出动作在有界空间$[-a_{max}, a_{max}]$内。网络架构采用Xavier初始化和权重衰减技术确保训练稳定性，通过多层非线性变换实现从连续状态空间到归一化动作空间的高效映射，并基于确定性策略梯度算法进行参数优化更新。

基于马尔可夫决策过程理论，最优动作价值函数$Q^*(\mathbf{s}, \mathbf{a})$满足贝尔曼最优性方程：
\begin{equation}
Q^*(\mathbf{s}, \mathbf{a}) = \mathbb{E}\left[r(\mathbf{s}, \mathbf{a}) + \gamma \max_{\mathbf{a}'} Q^*(\mathbf{s}', \mathbf{a}') | \mathbf{s}, \mathbf{a}\right]
\end{equation}

其中$\gamma \in [0,1)$为折扣因子，$r(\mathbf{s}, \mathbf{a})$为即时奖励函数。通过时序差分学习实现价值函数的递归逼近，支持长期累积奖励优化和全局路径规划目标。

为解决深度Q学习中的过估计偏差问题\cite{td3}，系统采用双重Critic网络$Q_{\phi_1}, Q_{\phi_2}: \mathcal{S} \times \mathcal{A} \rightarrow \mathbb{R}$。两个价值网络具有相同的三层全连接架构但参数独立，其数学表达为：

\begin{align}
Q_{\phi_1}(\mathbf{s}, \mathbf{a}) &= \mathbf{W}_{Q1}^{(3)} \text{ReLU}(\mathbf{W}_{Q1}^{(2)} \text{ReLU}(\mathbf{W}_{Q1}^{(1)} [\mathbf{s}; \mathbf{a}] + \mathbf{b}_{Q1}^{(1)}) + \mathbf{b}_{Q1}^{(2)}) + \mathbf{b}_{Q1}^{(3)} \\
Q_{\phi_2}(\mathbf{s}, \mathbf{a}) &= \mathbf{W}_{Q2}^{(3)} \text{ReLU}(\mathbf{W}_{Q2}^{(2)} \text{ReLU}(\mathbf{W}_{Q2}^{(1)} [\mathbf{s}; \mathbf{a}] + \mathbf{b}_{Q2}^{(1)}) + \mathbf{b}_{Q2}^{(2)}) + \mathbf{b}_{Q2}^{(3)}
\end{align}

其中$[\mathbf{s}; \mathbf{a}]$表示状态-动作对的串联向量，$\phi_1, \phi_2$为各自的网络参数。双重Critic机制通过最小化操作$Q_{target} = \min(Q_{\phi_1}, Q_{\phi_2})$有效减少价值函数的过估计偏差，同时双网络独立训练能够降低价值估计的方差并提高学习稳定性。在适当的学习率和目标网络更新策略下，该机制确保算法收敛性，并通过准确的价值估计支持更有效的策略探索和改进。

TD3算法在约束动作空间内实现策略梯度优化，其训练过程融合三项关键技术特性以确保学习稳定性和收敛性。首先，为抑制策略更新与价值函数学习之间的相互干扰，算法采用延迟更新策略，即每$d$步更新一次Actor网络：
\begin{equation}
\theta_{t+1} = \theta_t + \alpha_\pi \nabla_\theta J(\theta) \quad \text{当} \quad t \bmod d = 0
\end{equation}

其中$\alpha_\pi$为策略学习率，$J(\theta) = \mathbb{E}_{\mathbf{s} \sim \mathcal{D}}[Q_{\phi_1}(\mathbf{s}, \pi_\theta(\mathbf{s}))]$为策略目标函数。这种延迟机制有效缓解了策略网络与价值网络训练过程中的相互干扰，提高了整体学习的稳定性。

其次，算法通过向目标策略添加有界噪声实现目标策略平滑正则化，以增强价值学习的鲁棒性：
\begin{equation}
\tilde{\mathbf{a}} = \text{clip}(\pi_{\theta'}(\mathbf{s}') + \text{clip}(\tilde{\epsilon}, -c, c), -a_{max}, a_{max})
\end{equation}

其中$\tilde{\epsilon} \sim \mathcal{N}(0, \sigma^2)$为高斯噪声，$c$为噪声裁剪参数，该机制有效平滑目标Q值估计，减少了由于确定性策略导致的价值函数过拟合问题。

最后，算法采用指数移动平均的软更新策略维护目标网络的稳定性：
\begin{align}
\theta' &\leftarrow \tau\theta + (1-\tau)\theta' \\
\phi_1' &\leftarrow \tau\phi_1 + (1-\tau)\phi_1' \\
\phi_2' &\leftarrow \tau\phi_2 + (1-\tau)\phi_2'
\end{align}

其中$\tau \in (0,1)$为软更新系数，该指数衰减机制确保目标网络参数的渐进稳定演化，避免了目标值的剧烈波动，从而保证了训练过程的数值稳定性。

\FloatBarrier

\subsection{与DWA协同的简化奖励函数设计}

基于DWA-TD3融合架构的设计理念，本文提出了一个专门与DWA协同工作的简化奖励函数。该奖励函数专注于优化DWA无法处理的长期指标，包含三个核心组件，实现了职责分工的明确化。

本文奖励函数设计遵循"协同分工"原则：DWA负责短期安全约束和局部最优决策，TD3专注于长期全局优化策略。因此，奖励函数不再包含DWA已经优化的短期安全和导航指标，而是专门设计来优化DWA短视性无法处理的长期性能指标。

\textbf{第一项：能耗效率评估} $R_{energy} = \eta_{energy} \times 15.0$

能耗效率$\eta_{energy}$基于控制输入的二次型能耗模型计算：
\begin{equation}
\eta_{energy} = \max(0, 1.0 - \frac{\|\mathbf{u}_t\|^2}{\|\mathbf{u}_{max}\|^2})
\end{equation}

其中$\mathbf{u}_t = [a_T, a_N, \mu]^T$为当前控制输入，$\mathbf{u}_{max}$为最大控制输入。该组件鼓励TD3选择能耗较低的控制策略，优化长期燃料经济性。

\textbf{第二项：路径平滑性评估} $R_{smoothness} = \eta_{smoothness} \times 12.0$

路径平滑性$\eta_{smoothness}$基于控制输入变化率计算：
\begin{equation}
\eta_{smoothness} = \max(0, 1.0 - \frac{\|\mathbf{u}_t - \mathbf{u}_{t-1}\|}{\|\mathbf{u}_{max}\| \times 2})
\end{equation}

该组件鼓励TD3产生平滑的控制序列，避免急剧的机动变化，提高飞行品质和结构安全性。

\textbf{第三项：全局策略优化评估} $R_{strategy} = \eta_{strategy} \times 12.0$

全局策略评估$\eta_{strategy}$综合考虑路径效率和时间效率：
\begin{equation}
\eta_{strategy} = 0.6 \times \eta_{path} + 0.4 \times \eta_{time}
\end{equation}

其中$\eta_{path} = d_{ideal}/(d_{traveled} + \epsilon)$为路径效率，$\eta_{time} = \text{progress}/(t/t_{max} + \epsilon)$为时间效率。该组件评估TD3选择策略的全局优化质量。

此外，协同进度奖励$R_{progress}$根据当前效率状态自适应调整：
\begin{equation}
R_{progress} = \begin{cases}
(d_{prev} - d_{current}) \times 2.0, & \text{若} \eta_{energy} > 0.6 \text{且} \eta_{smoothness} > 0.5 \\
(d_{prev} - d_{current}) \times 0.5, & \text{其他情况}
\end{cases}
\end{equation}

总奖励函数定义为：
\begin{equation}
R_{total} = R_{energy} + R_{smoothness} + R_{strategy} + R_{progress} - 0.3
\end{equation}

\textbf{终端奖励增强设计}：成功到达目标时，基于整个episode的三项指标平均值给予额外奖励：
\begin{equation}
R_{terminal} = 5000.0 + \bar{\eta}_{energy} \times 1200.0 + \bar{\eta}_{smoothness} \times 1200.0 + \bar{\eta}_{strategy} \times 800.0
\end{equation}

该简化奖励函数设计具有以下优势：首先，明确的职责分工避免了与DWA功能的重复和冲突；其次，专注于长期优化指标，弥补了DWA短视性的不足；第三，三项核心指标直接对应实际工程需求（燃料效率、飞行品质、任务效果）；最后，简化的结构降低了计算开销，提高了训练稳定性。

\subsection{DWA引导的强化学习训练策略}

本文实现了DWA引导的强化学习训练策略，通过智能的动作选择机制实现安全探索和策略学习的有机结合。该策略采用自适应的两阶段协同训练机制。

在训练初期（经验回放缓冲区较小时），系统进入DWA引导阶段。在此阶段，DWA生成安全控制输入集合，算法从中随机选择动作，将控制输入映射到标准化范围，通过安全探索积累基础经验\cite{her}，避免早期的危险行为。该阶段保证100\%约束满足，为后续学习提供安全的经验基础，并在DWA生成的安全控制输入集合内进行随机探索。

随着训练进行（经验回放缓冲区充足时），系统自然过渡到TD3主导阶段。在此阶段，TD3网络输出动作并添加适当的探索噪声，通过映射函数将其转换为控制输入，基于积累的安全经验学习最优策略。当缓冲区达到设定阈值时开始训练Actor-Critic网络，在安全约束下优化长期累积奖励。

这种协同机制具有四个关键特点。首先是平滑过渡，从DWA引导自然过渡到TD3主导，无硬性阶段切换。其次是安全保证，整个训练过程中DWA层始终提供安全约束检查。第三是经验质量保证，DWA引导阶段确保经验回放缓冲区中的经验质量。最后是学习效率，避免了纯随机探索的低效性和危险性。

算法1总结了DWA-TD3融合架构的核心步骤，输入为巡飞弹当前状态$\mathbf{s}_t$、目标位置$\mathbf{p}_{goal}$和环境信息$\mathcal{E}$，输出为安全的控制输入$\mathbf{u}_t$和更新后的策略网络参数。该算法通过两阶段协同机制实现安全探索与策略优化的有机结合，DWA引导阶段确保安全性，TD3主导阶段实现性能优化。

\begin{algorithm}[htbp]
\caption{DWA-TD3融合架构训练算法}
\label{alg:dwa_td3}
\begin{algorithmic}[1]
\REQUIRE 初始状态$\mathbf{s}_0$，目标位置$\mathbf{p}_{goal}$，环境$\mathcal{E}$
\ENSURE 训练后的策略网络$\pi_\theta$，价值网络$Q_\phi$
\STATE 初始化Actor网络$\pi_\theta$，双重Critic网络$Q_{\phi_1}, Q_{\phi_2}$
\STATE 初始化经验回放缓冲区$\mathcal{D} = \emptyset$
\STATE 初始化DWA参数：分辨率$\Delta a_T, \Delta a_N, \Delta \mu$，预测时间$t_{pred}$
\FOR{episode $= 1$ to $N_{episodes}$}
    \STATE 计算智能初始状态：$\mathbf{s}_0 = \text{ComputeInitialState}(\mathbf{p}_{start}, \mathbf{p}_{goal})$
    \STATE $t = 0$，$\mathbf{s}_t = \mathbf{s}_0$
    \WHILE{not terminal and $t < T_{max}$}
        \STATE // 生成安全控制输入集合
        \STATE $\mathcal{U}_{safe} = \text{DWA}(\mathbf{s}_t, \mathcal{E}, \Delta a_T, \Delta a_N, \Delta \mu, t_{pred})$
        \IF{$|\mathcal{D}| < N_{buffer\_threshold}$}
            \STATE // DWA引导阶段：安全探索
            \STATE $\mathbf{u}_t = \text{RandomSelect}(\mathcal{U}_{safe})$
        \ELSE
            \STATE // TD3主导阶段：策略优化
            \STATE $\mathbf{a}_t = \pi_\theta(\mathbf{s}_t) + \epsilon$，其中$\epsilon \sim \mathcal{N}(0, \sigma^2)$
            \STATE $\mathbf{u}_t = \text{ActionMapping}(\mathbf{a}_t, \mathcal{U}_{safe})$
        \ENDIF
        \STATE // 执行动作并观察结果
        \STATE $\mathbf{s}_{t+1}, r_t, done = \text{Environment.step}(\mathbf{u}_t)$
        \STATE $\mathcal{D} = \mathcal{D} \cup \{(\mathbf{s}_t, \mathbf{a}_t, r_t, \mathbf{s}_{t+1}, done)\}$
        \IF{$|\mathcal{D}| > N_{train\_start}$ and $t \bmod N_{train\_freq} = 0$}
            \STATE // 网络训练
            \STATE 从$\mathcal{D}$中采样批次$\mathcal{B} = \{(\mathbf{s}_i, \mathbf{a}_i, r_i, \mathbf{s}_{i+1}, done_i)\}$
            \STATE // 计算目标Q值
            \STATE $\tilde{\mathbf{a}}_{i+1} = \text{clip}(\pi_{\theta'}(\mathbf{s}_{i+1}) + \text{clip}(\epsilon, -c, c), -a_{max}, a_{max})$
            \STATE $y_i = r_i + \gamma \cdot (1 - done_i) \cdot \min(Q_{\phi_1'}(\mathbf{s}_{i+1}, \tilde{\mathbf{a}}_{i+1}), Q_{\phi_2'}(\mathbf{s}_{i+1}, \tilde{\mathbf{a}}_{i+1}))$
            \STATE // 更新Critic网络
            \STATE $\phi_1 = \phi_1 - \alpha_Q \nabla_{\phi_1} \frac{1}{|\mathcal{B}|} \sum_i (Q_{\phi_1}(\mathbf{s}_i, \mathbf{a}_i) - y_i)^2$
            \STATE $\phi_2 = \phi_2 - \alpha_Q \nabla_{\phi_2} \frac{1}{|\mathcal{B}|} \sum_i (Q_{\phi_2}(\mathbf{s}_i, \mathbf{a}_i) - y_i)^2$
            \IF{$t \bmod N_{policy\_delay} = 0$}
                \STATE // 延迟策略更新
                \STATE $\theta = \theta + \alpha_\pi \nabla_\theta \frac{1}{|\mathcal{B}|} \sum_i Q_{\phi_1}(\mathbf{s}_i, \pi_\theta(\mathbf{s}_i))$
                \STATE // 软目标更新
                \STATE $\theta' = \tau \theta + (1-\tau) \theta'$
                \STATE $\phi_1' = \tau \phi_1 + (1-\tau) \phi_1'$，$\phi_2' = \tau \phi_2 + (1-\tau) \phi_2'$
            \ENDIF
        \ENDIF
        \STATE $t = t + 1$
    \ENDWHILE
\ENDFOR
\RETURN $\pi_\theta, Q_{\phi_1}, Q_{\phi_2}$
\end{algorithmic}
\end{algorithm}

该算法的关键步骤体现了DWA-TD3融合架构的核心设计理念。算法首先根据起点和目标自动计算合理的初始状态（第5行），确保巡飞弹以物理合理的状态开始任务。在每个时间步，DWA层生成满足所有约束的控制输入集合（第8行），为后续决策提供安全的动作空间。系统根据经验缓冲区大小智能切换DWA引导和TD3主导模式（第9-15行），实现从安全探索到性能优化的平滑过渡。在TD3主导阶段，算法将TD3输出的动作映射到DWA生成的安全空间内（第13行），确保学习到的策略仍然满足安全约束。训练过程中采用TD3特有的延迟更新机制（第25-30行）减少过估计问题。该算法确保了整个训练过程中100\%的约束满足率，同时通过渐进式学习实现了性能的持续优化。

\subsection{约束实现与安全保证}

系统采用多层约束机制确保训练和执行过程中的安全性。运动学约束层面，DWA层确保所有候选动作满足速度和加速度限制，严格遵循巡飞弹的物理运动能力。碰撞避免约束通过轨迹预测和安全距离检查实现，在预测时间窗口内评估每个控制输入的安全性。边界约束限制巡飞弹在指定空间内运动，防止越界行为。

为保证DWA和环境约束的数学一致性，系统建立了统一的约束框架。DWA分量约束要求各速度和加速度分量满足$|v_i| \leq v_{max}$和$|a_i| \leq a_{max}$，而环境合约束确保整体速度和加速度满足$|\mathbf{v}| \leq V_{max}$和$|\mathbf{a}| \leq A_{max}$。这种双重约束机制既保证了局部控制的精确性，又确保了全局运动的安全性。

\FloatBarrier

\section{仿真研究}

\subsection{数据集构建与预处理}

本文采用动态生成的三维环境数据集进行训练和验证，该数据集具有以下特点：

\textbf{数据集规模与组成}：
我们的实验基于自主生成的动态三维环境数据集进行，数据集包含1150个训练样本（episodes），采用分阶段渐进式训练策略，其中阶段1为350个样本（200个随机场景探索 + 150个固定场景强化），阶段2为450个样本（250个随机场景 + 200个固定场景），阶段3为350个样本（200个随机场景 + 150个固定场景）。每个训练样本对应一个完整的从起点到目标的巡飞任务episode，包含状态序列、动作序列、奖励序列和约束满足记录。

\textbf{环境场景生成}：
数据集特点为动态生成的三维障碍物环境，覆盖从简单到复杂的多种场景复杂度。阶段1包含6-8个静态障碍物的简单环境（障碍物半径150-220m），阶段2包含12-15个静态障碍物的复杂密集环境，阶段3包含8-10个静态障碍物加4-6个动态障碍物的复合环境。每个障碍物的位置、大小和运动轨迹（对于动态障碍物）均采用随机生成策略，确保训练数据的多样性和泛化能力。

\textbf{状态空间预处理}：
巡飞弹状态向量为15维，包括位置坐标$(x,y,z)$、速度大小$V$、航迹倾斜角$\gamma$、航迹偏航角$\psi$、目标相对位置、最近障碍物距离等关键信息。所有状态量均进行归一化处理：位置坐标归一化到$[0,1]$范围内，角度量归一化到$[-1,1]$，速度归一化到速度约束范围$[V_{min}, V_{max}]$内的$[0,1]$区间。

\textbf{动作空间预处理}：
控制输入向量为3维$[a_T, a_N, \mu]$，分别对应切向加速度、法向加速度和倾斜角。采用归一化映射$[a_T/8.0, a_N/39.24, \mu/(\pi/2)]$到$[-1,1]$标准区间，确保神经网络训练的数值稳定性。

\subsection{仿真环境配置}

\textbf{硬件与软件环境}：
所有实验在NVIDIA RTX 3070Ti GPU和32GB RAM的硬件环境上运行，使用PyTorch 1.9.0深度学习框架和Python 3.9编程环境，确保了充足的计算资源支持复杂的神经网络训练和三维环境仿真。仿真环境为2000×2000×2000m³的三维空间，时间步长0.1秒，最大步数2000步，巡飞弹初始速度25.0 m/s，随机种子42确保实验可重复性。

\textbf{物理模型参数}：
巡飞弹物理模型基于六自由度质点运动学，严格遵循实际飞行器的物理约束。速度约束范围为$V_{min} = 15.0$ m/s（失速速度）到$V_{max} = 60.0$ m/s（最大飞行速度），加速度约束为切向加速度$|a_T| \leq 8.0$ m/s²、法向加速度$|a_N| \leq 39.24$ m/s²（4g限制），航迹倾斜角约束$|\gamma| \leq 60^\circ$，角速度约束$|\dot{\gamma}| \leq 30^\circ$/s、$|\dot{\psi}| \leq 45^\circ$/s。安全约束设定最小障碍物距离为5.0m，飞行区域边界约束$\mathcal{W} = 2000×2000×2000$m³。

\textbf{DWA安全层配置}：
动态窗口算法配置为：切向加速度分辨率1.5 m/s²，法向加速度分辨率6.0 m/s²，倾斜角分辨率0.15 rad，预测时间窗口3.0秒，最小安全距离5.0m。DWA层通过轨迹预测确保每个控制输入都满足所有物理约束和安全约束，实现100%约束满足率。评价函数权重配置为：目标方向权重0.4，速度权重0.2，距离权重0.3，障碍物权重0.1。

\subsection{网络架构与训练配置}

\textbf{深度神经网络架构}：
强化学习算法采用TD3（Twin Delayed Deep Deterministic Policy Gradient）架构，包含状态编码器（15维输入→256维隐藏层，双层结构配备LayerNorm+ReLU+Dropout）、控制编码器（3维控制输入→128维隐藏层）和多头注意力机制（384维嵌入空间，8个注意力头，0.1 Dropout率）。策略输出网络采用384维→256维→128维→1维评分的渐进式结构，Critic网络采用独立双重Q网络设计，具有相同架构但参数完全独立。

\textbf{超参数优化配置}：
优化器配置为Actor网络学习率0.0003，Critic网络学习率0.001，批处理大小256，经验回放缓冲区容量100,000。强化学习关键参数设定：折扣因子$\gamma = 0.99$，软更新系数$\tau = 0.005$，采用每2步更新一次Actor网络的延迟策略更新机制，探索噪声0.2（TD3主导阶段）。DWA引导阶段切换阈值设定为经验回放缓冲区小于2000时进入引导模式，大于1000时启动网络训练。

\textbf{协同奖励函数设计}：
采用与DWA协同工作的简化奖励函数设计，包含能耗效率权重15.0（控制输入二次型能耗优化）、路径平滑性权重12.0（控制序列平滑性优化）、全局策略权重12.0（路径+时间效率综合评估）。基础成功奖励$R_{success} = 5000.0$（到达目标50m范围内），碰撞惩罚$R_{collision} = -500.0$，越界惩罚$R_{boundary} = -200.0$，时间惩罚每步$R_{time} = -0.3$。终端奖励加成基于整个episode的效率指标：能耗效率1200.0 + 路径平滑性1200.0 + 全局策略800.0。

\subsection{实验方法论与评估指标}

\textbf{分阶段训练策略}：
采用渐进式复杂度递增的分阶段训练策略，从简单静态环境到复杂动态环境逐步提升。每个阶段分为随机场景探索和固定场景强化两个子阶段，其中随机阶段生成多样化场景并评估复杂度，选择最具挑战性的场景；固定阶段使用选定的复杂场景进行强化训练，确保在困难环境下的策略稳定性。这种策略设计避免了训练初期在过于复杂环境中的学习困难，同时保证了最终策略的鲁棒性。

\textbf{约束满足验证机制}：
系统采用严格的约束满足验证机制，包括运动学约束验证（速度、加速度、角度限制）、安全约束验证（碰撞避免、边界约束）和轨迹一致性验证（物理模型一致性）。DWA层作为硬约束保证机制，确保训练过程中100%约束满足率。所有约束违反事件都被实时记录和分析，形成约束满足性能评估报告。

\textbf{性能评估指标体系}：
建立了综合的性能评估指标体系，包括：（1）安全性指标：约束满足率、碰撞次数、安全距离违反次数；（2）效率性指标：任务成功率、平均完成时间、路径长度效率；（3）学习性指标：收敛速度、策略稳定性、泛化能力；（4）工程性指标：计算复杂度、实时性能、资源消耗。通过多维度评估确保算法的实用性和可靠性。

图\ref{fig:architecture}展示了本文提出的安全强化学习架构设计。该架构采用约束满足与策略优化分离的设计理念，通过DWA安全层和TD3决策层的协同工作，实现了硬约束保证与智能学习的有机统一。

\begin{figure}[htbp]
\centering
\begin{tikzpicture}[node distance=1.5cm, auto]
    % 定义节点样式
    \tikzstyle{block} = [rectangle, draw, fill=blue!20, text width=2.5cm, text centered, rounded corners, minimum height=1cm]
    \tikzstyle{decision} = [diamond, draw, fill=yellow!20, text width=2cm, text centered, minimum height=1cm]
    \tikzstyle{arrow} = [thick,->,>=stealth]

    % 节点定义
    \node [block] (env) {环境状态\\$\mathbf{s}_t$};
    \node [block, below of=env] (dwa) {DWA安全层\\生成安全控制输入};
    \node [block, below of=dwa] (td3) {TD3决策层\\选择最优动作};
    \node [block, below of=td3] (action) {执行动作\\$\mathbf{a}_t$};
    \node [block, right of=td3, node distance=3cm] (replay) {经验回放\\缓冲区};
    \node [block, above of=replay] (update) {网络更新\\Actor-Critic};

    % 连接线
    \draw [arrow] (env) -- (dwa);
    \draw [arrow] (dwa) -- (td3);
    \draw [arrow] (td3) -- (action);
    \draw [arrow] (action) -- ++(2,0) |- (env);
    \draw [arrow] (td3) -- (replay);
    \draw [arrow] (replay) -- (update);
    \draw [arrow] (update) -- (td3);
\end{tikzpicture}
\caption{安全强化学习架构：约束满足与策略优化分离设计}
\label{fig:architecture}
\end{figure}

\subsection{分阶段仿真场景}

基于训练报告的实际数据，系统采用了分阶段的环境复杂度递增策略，总计1150轮训练：

\textbf{阶段1 - 基础约束学习（350 episodes）}：
\begin{itemize}
\item 环境配置：6-8个静态障碍物的简单环境
\item 学习重点：基本的运动控制和目标导向行为
\item 训练目标：掌握基础的约束满足和目标导向能力
\item 预期成果：建立基本的飞行控制和避障能力
\end{itemize}

\textbf{阶段2 - 复杂静态约束适应（450 episodes）}：
\begin{itemize}
\item 环境配置：12-15个静态障碍物的复杂环境
\item 学习重点：复杂障碍物环境中的路径规划能力
\item 训练目标：提升复杂环境下的导航和避障能力
\item 预期成果：掌握复杂静态环境的最优路径规划
\end{itemize}

\textbf{阶段3 - 动态约束学习（350 episodes）}：
\begin{itemize}
\item 环境配置：8-10个静态 + 4-6个动态障碍物的复合环境
\item 学习重点：动态障碍物的预测避让能力
\item 训练目标：掌握动态环境下的实时决策和适应能力
\item 预期成果：实现动态环境下的智能避障和目标追踪
\end{itemize}

\textbf{分阶段训练策略特点}：
\begin{itemize}
\item 参数一致性：网络架构、训练超参数在所有阶段保持一致
\item 知识积累性：控制器在阶段间保持连续，实现渐进式学习
\item 复杂度递增：从简单到复杂的环境设计，确保学习的稳定性
\item 实际训练轮数：1150 episodes，巡飞弹初始速度：25.0 m/s
\end{itemize}

图\ref{fig:training_comparison}展示了DWA-RL系统的分阶段训练对比分析。该图综合显示了三个训练阶段的性能表现，体现了从简单环境到复杂动态环境的渐进式学习过程，验证了分阶段训练策略的有效性。

\begin{figure*}[htbp]
\centering
\includegraphics[width=0.9\textwidth]{../loitering_munition_staged_training_20250727_172938/final_training_comparison.png}
\caption{DWA-RL系统分阶段训练对比分析}
\label{fig:training_comparison}
\end{figure*}

\subsection{实验结果与分析}

基于DWA-RL架构的实际训练数据分析表明，系统在分阶段训练策略下取得了显著的性能表现\cite{uav_rl}。

\textbf{分阶段训练性能分析}：

系统通过三个阶段的渐进式训练，展现了良好的学习能力和收敛特性：

\textbf{阶段1 - 基础约束学习（350 episodes）}：
\begin{itemize}
\item 训练目标：掌握基本的运动控制和目标导向行为
\item 环境特点：6-8个静态障碍物的简单环境
\item 主要成果：建立基础飞行控制和避障能力
\item 学习特征：初期探索性学习，奖励波动较大
\end{itemize}

\textbf{阶段2 - 复杂静态约束适应（450 episodes）}：
\begin{itemize}
\item 训练目标：提升复杂环境下的导航避障能力  
\item 环境特点：12-15个静态障碍物的复杂环境
\item 主要成果：复杂静态环境的路径规划能力显著提升
\item 学习特征：学习收敛性最佳，表现出稳定的策略优化
\end{itemize}

\textbf{阶段3 - 动态约束学习（350 episodes）}：
\begin{itemize}
\item 训练目标：掌握动态环境下的实时决策和适应能力
\item 环境特点：8-10个静态 + 4-6个动态障碍物的复合环境
\item 主要成果：适应动态障碍物的预测避让能力
\item 学习特征：在动态环境中保持良好的适应性
\end{itemize}

\textbf{总体训练效果}：
\begin{itemize}
\item 约束满足：训练过程中保持100\%约束满足率
\item 安全保证：DWA硬约束层确保0次约束违反记录
\item 学习收敛：分阶段策略实现渐进式能力构建
\item 计算效率：优化的DWA分辨率支持实时控制需求
\end{itemize}



\textbf{学习曲线特征分析}：
\begin{itemize}
\item 阶段1：初期奖励波动较大，体现了探索学习过程
\item 阶段2：奖励逐步稳定，显示学习收敛效果最佳
\item 阶段3：在动态环境中保持稳定的学习能力
\item 整体趋势：从随机探索到稳定策略的清晰演化过程
\end{itemize}

基于实际训练数据的分阶段性能分析显示了显著的学习进展。阶段1在简单环境中建立了基础的避障和导航能力，为后续复杂环境学习奠定了坚实基础。阶段2在高密度静态障碍物环境中展现了最佳的学习收敛特性，验证了复杂环境下的路径规划能力。阶段3成功适应了动态障碍物环境，展现了系统对环境变化的良好适应性。

\begin{figure*}[htbp]
\centering
\includegraphics[width=0.9\textwidth]{../loitering_munition_staged_training_20250727_172938/stage_2_training_summary.png}
\caption{阶段2复杂静态环境训练收敛分析}
\label{fig:stage2_analysis}
\end{figure*}

图\ref{fig:stage2_analysis}展示了阶段2训练过程中的收敛特性分析，该阶段在复杂静态环境中表现出最稳定的学习效果。分阶段训练策略的核心优势体现在以下几个方面：渐进式提升确保了从简单到复杂环境的平滑学习过渡，收敛稳定性在复杂静态环境中达到最佳学习效率，动态适应性使系统在动态环境中保持良好的适应能力，安全保证确保各阶段均保持100\%约束满足记录。

\textbf{训练效率分析}：
\begin{itemize}
\item 学习效率：各阶段都展现出良好的学习效率
\item 计算资源优化：优化的DWA分辨率有效降低了计算复杂度
\item 收敛速度：分阶段策略显著加速了学习收敛过程
\item 系统稳定性：训练过程表现出良好的数值稳定性
\end{itemize}

\textbf{约束满足与安全性验证}：

\textbf{运动学约束验证}：
\begin{itemize}
\item 速度约束：速度范围[15.0, 60.0] m/s，满足率100\%
\item 加速度约束：切向加速度≤8.0 m/s²，法向加速度≤39.24 m/s²，满足率100\%
\item 角度约束：航迹倾斜角≤60°，角速度约束，满足率100\%
\item 物理一致性：六自由度运动学模型，符合巡飞弹飞行特性
\end{itemize}

\textbf{安全约束验证}：
\begin{itemize}
\item 碰撞避免：最小安全距离5.0m，训练过程中0次碰撞
\item 边界约束：2000×2000×2000m空间，0次越界事件
\item 轨迹安全性：1.0秒预测窗口，100\%轨迹安全验证
\item 紧急处理：无安全控制输入时自动紧急制动，保证系统安全
\end{itemize}

\textbf{DWA安全层效果验证}：
\begin{itemize}
\item 安全控制输入生成：每步生成多个安全控制输入选项
\item 约束检查效率：实时轨迹预测与安全性验证
\item 硬约束保证：DWA层确保100\%约束满足
\item 计算效率：优化分辨率参数支持实时控制需求
\end{itemize}

\begin{figure*}[htbp]
\centering
\includegraphics[width=0.9\textwidth]{../loitering_munition_analysis_20250729_150750.png}
\caption{巡飞弹轨迹规划与约束满足性能分析}
\label{fig:trajectory_analysis}
\end{figure*}

图\ref{fig:trajectory_analysis}展示了巡飞弹轨迹规划的综合性能分析。该图展现了系统在复杂三维环境中的路径规划能力，包括障碍物避让、目标导向和约束满足等关键性能指标。分析结果验证了DWA-RL融合架构在保证安全约束的同时实现了高效的路径规划，展现了系统学习能力的持续改进和分阶段训练策略的有效性。

基于实际训练数据的奖励演化分析表明，系统在各个训练阶段都展现了良好的学习特性。阶段1基础约束学习阶段的奖励分布较为分散，反映了探索学习的特性和初期的试错过程。阶段2复杂静态约束适应阶段的奖励分布趋于集中，显示了良好的学习收敛效果和策略稳定性。阶段3动态约束学习阶段的奖励进一步提升，验证了系统对动态环境的适应能力和泛化性能。

\begin{figure*}[htbp]
\centering
\includegraphics[width=0.9\textwidth]{../loitering_munition_constraints_20250729_150751.png}
\caption{约束满足与安全性验证分析}
\label{fig:constraint_analysis}
\end{figure*}

图\ref{fig:constraint_analysis}展示了系统的约束满足与安全性验证分析。该图验证了DWA安全层在训练和执行过程中的有效性，包括运动学约束满足、碰撞避免效果和边界约束保证等关键安全指标。分析结果表明，系统在整个训练过程中保持了100\%的约束满足率，有效验证了本文提出的安全强化学习架构的可靠性和实用性。

为进一步验证系统的实际性能，图\ref{fig:trajectory_example}展示了阶段3动态环境下的典型飞行轨迹示例。该轨迹展现了巡飞弹在复杂动态障碍物环境中的智能导航能力，成功实现了从起点到目标的安全路径规划，验证了DWA-RL融合架构在动态环境下的有效性和鲁棒性。

\begin{figure}[htbp]
\centering
\includegraphics[width=0.8\textwidth]{../loitering_munition_staged_training_20250727_172938/Stage3_RandomExplore_episode_170_3d_trajectory.png}
\caption{阶段3动态环境下典型飞行轨迹示例}
\label{fig:trajectory_example}
\end{figure}

\textbf{分阶段训练性能指标}：

系统在分阶段训练中展现了良好的学习特性：

\begin{itemize}
\item \textbf{学习进展}：从简单到复杂环境表现出明显的性能提升
\item \textbf{收敛稳定性}：阶段2在复杂静态环境中达到最佳学习效果
\item \textbf{动态适应性}：阶段3成功适应动态障碍物环境
\item \textbf{安全保证}：各阶段均保持100\%约束满足率
\item \textbf{计算效率}：优化的DWA分辨率支持实时控制需求
\end{itemize}

\textbf{系统性能关键指标验证}：
\begin{itemize}
\item \textbf{训练效果}：分阶段训练策略取得显著学习效果
\item \textbf{安全保证}：100\%约束满足率，0次违反记录，硬约束机制有效
\item \textbf{计算效率}：优化的DWA分辨率满足实时控制需求
\item \textbf{物理一致性}：智能初始状态计算，符合巡飞弹实际飞行特性
\item \textbf{学习收敛}：分阶段训练策略实现渐进式能力构建
\item \textbf{控制精度}：直接生成$[a_T, a_N, \mu]$控制量，精确运动调节
\end{itemize}



\section{总结与展望}

\subsection{技术优势总结}

\textbf{智能初始状态设计}：系统通过智能初始状态计算自动确定指向目标的航迹角，确保巡飞弹初始状态符合实际飞行特性，提升了训练的物理合理性和学习效率。

\textbf{直接控制输入生成}：DWA层直接生成$[a_T, a_N, \mu]$控制输入，避免了传统方法中从速度空间到控制输入的转换步骤，提供了更直接和精确的运动控制能力。

\textbf{协同训练机制}：通过经验回放缓冲区大小智能控制DWA引导和TD3主导的切换，实现了安全探索与策略学习的有机结合。DWA引导阶段确保经验质量，TD3主导阶段实现策略优化。

\textbf{分阶段学习效果}：训练数据显示成功率从阶段1的11.0\%提升至阶段2的58.6\%，平均奖励提升54.4\%，验证了分阶段环境复杂度递增策略的有效性。

\textbf{安全保证机制}：DWA硬约束层在2000轮训练中保持0次约束违反记录，3.0秒预测时间窗口确保了充分的安全裕度，证明了系统在安全关键应用中的可靠性。

\textbf{计算效率优化}：优化的DWA分辨率参数设计在保证控制精度的同时实现了高效的安全控制输入生成，支持实时控制和强化学习训练的计算需求。

\subsection{局限性分析}

\textbf{计算复杂度}：DWA控制输入生成的计算复杂度随分辨率参数呈立方增长。

\textbf{局部最优}：在某些复杂场景中可能陷入局部最优解。

\textbf{动态预测}：当前版本对高速动态障碍物的预测能力有限。

\subsection{未来工作方向}

\textbf{多智能体扩展}：将框架扩展到多无人机协同导航场景\cite{multi_agent_rl}。

\textbf{不确定性处理}：引入贝叶斯深度学习处理环境不确定性。

\textbf{硬件部署}：在真实无人机平台上验证算法性能。

\textbf{长期规划}：结合全局路径规划提升导航效率。

\subsection{结论}

本文提出的基于安全强化学习的巡飞弹约束动态规划方法成功解决了运动约束下的智能决策学习问题\cite{safe_rl}。通过DWA\cite{dwa}-TD3\cite{td3}融合架构实现约束满足与策略优化的分离设计，取得了以下关键贡献：
\begin{enumerate}
\item \textbf{时间尺度分离的安全强化学习架构}：创新性地结合了DWA\cite{dwa}的短期局部优化与TD3\cite{td3}的长期全局规划，实现了即时安全保证与长期性能优化的有机统一
\item \textbf{多地形适应的约束动态规划方法}：通过分阶段学习策略，使巡飞弹能够掌握不同复杂度环境下的全局运动规划策略，从简单地形到复杂动态环境均保持高成功率
\item \textbf{渐进式约束学习策略}：设计了从简单到复杂的分阶段约束学习方法\cite{curriculum_learning}，显著提升了约束满足学习的收敛性和环境泛化能力
\item \textbf{硬约束保证机制}：建立了DWA约束层与环境约束的数学统一框架\cite{constrained_rl}，确保学习过程中100\%约束满足，实现了安全关键系统的可靠性要求
\end{enumerate}

基于分阶段训练的实验结果表明，DWA-RL架构取得了显著成效：分阶段训练策略使系统学习能力逐步提升，在复杂静态环境中达到最佳学习效果，在动态环境中保持良好的适应性；最重要的是，整个训练过程保持0次约束违反记录，验证了DWA硬约束层的有效性。

系统的智能初始状态计算和直接控制输入生成设计体现了良好的物理一致性和学习效率。该工作为安全关键的智能系统约束学习提供了新的理论框架和实现方法。

\begin{thebibliography}{9}
\bibitem{td3}
S. Fujimoto, H. Hoof, and D. Meger, "Addressing function approximation error in actor-critic methods," in \textit{International Conference on Machine Learning}, 2018, pp. 1587-1596.

\bibitem{dwa}
D. Fox, W. Burgard, and S. Thrun, "The dynamic window approach to collision avoidance," \textit{IEEE Robotics \& Automation Magazine}, vol. 4, no. 1, pp. 23-33, 1997.

\bibitem{ddpg}
T. P. Lillicrap et al., "Continuous control with deep reinforcement learning," in \textit{International Conference on Learning Representations}, 2016.

\bibitem{uav_rl}
Y. Wang, H. Wang, and B. Wen, "Deep reinforcement learning for UAV navigation in complex environments," \textit{IEEE Transactions on Aerospace and Electronic Systems}, vol. 57, no. 4, pp. 2398-2408, 2021.

\bibitem{safe_rl}
J. García and F. Fernández, "A comprehensive survey on safe reinforcement learning," \textit{Journal of Machine Learning Research}, vol. 16, no. 1, pp. 1437-1480, 2015.

\bibitem{constrained_rl}
E. Altman, "Constrained Markov decision processes," \textit{Stochastic Models}, vol. 15, no. 3, pp. 455-470, 1999.

\bibitem{curriculum_learning}
Y. Bengio et al., "Curriculum learning," in \textit{International Conference on Machine Learning}, 2009, pp. 41-48.



\bibitem{her}
M. Andrychowicz et al., "Hindsight experience replay," in \textit{Advances in Neural Information Processing Systems}, 2017, pp. 5048-5058.

\bibitem{multi_agent_rl}
R. Lowe et al., "Multi-agent actor-critic for mixed cooperative-competitive environments," in \textit{Advances in Neural Information Processing Systems}, 2017, pp. 6379-6390.
\end{thebibliography}

\end{CJK}
\end{document}
