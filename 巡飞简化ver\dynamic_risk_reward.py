"""
动态风险感知奖励函数 - 巡飞弹创新奖励机制
"""

import numpy as np
import torch
import torch.nn as nn
from typing import Dict, List, Tuple, Optional
from enum import Enum

class RiskLevel(Enum):
    """风险等级枚举"""
    LOW = 1      # 低风险
    MEDIUM = 2   # 中等风险
    HIGH = 3     # 高风险
    CRITICAL = 4 # 临界风险

class DynamicRiskAwareReward:
    """动态风险感知奖励函数"""
    
    def __init__(self):
        # 基础奖励权重
        self.base_weights = {
            'distance': 1.0,
            'progress': 1.0,
            'safety': 1.0,
            'efficiency': 1.0,
            'smoothness': 1.0
        }
        
        # 风险等级对应的权重调整系数
        self.risk_adjustments = {
            RiskLevel.LOW: {
                'distance': 1.2,    # 低风险时更注重效率
                'progress': 1.1,
                'safety': 0.8,      # 降低安全权重
                'efficiency': 1.3,
                'smoothness': 1.0
            },
            RiskLevel.MEDIUM: {
                'distance': 1.0,    # 平衡各目标
                'progress': 1.0,
                'safety': 1.0,
                'efficiency': 1.0,
                'smoothness': 1.0
            },
            RiskLevel.HIGH: {
                'distance': 0.8,    # 高风险时更注重安全
                'progress': 0.9,
                'safety': 1.5,      # 提高安全权重
                'efficiency': 0.7,
                'smoothness': 1.2
            },
            RiskLevel.CRITICAL: {
                'distance': 0.5,    # 临界风险时安全第一
                'progress': 0.6,
                'safety': 2.0,      # 大幅提高安全权重
                'efficiency': 0.4,
                'smoothness': 1.5
            }
        }
        
        # 风险评估参数
        self.risk_thresholds = {
            'low': 0.3,
            'medium': 0.6,
            'high': 0.8,
            'critical': 0.95
        }
        
        # 历史风险评估记录
        self.risk_history = []
        self.reward_history = []
    
    def calculate_reward(self, state, action, next_state, context=None):
        """计算动态风险感知奖励"""
        # 评估当前风险等级
        risk_level = self._assess_risk_level(state, context)
        
        # 获取动态权重
        dynamic_weights = self._get_dynamic_weights(risk_level)
        
        # 计算各组件奖励
        reward_components = self._calculate_reward_components(
            state, action, next_state, context
        )
        
        # 应用动态权重
        total_reward = self._apply_dynamic_weights(
            reward_components, dynamic_weights
        )
        
        # 记录历史
        self._record_history(risk_level, reward_components, total_reward)
        
        return total_reward, reward_components, risk_level
    
    def _assess_risk_level(self, state, context=None):
        """评估风险等级"""
        risk_score = 0.0
        
        # 1. 障碍物风险
        obstacle_risk = self._calculate_obstacle_risk(state, context)
        risk_score += obstacle_risk * 0.4
        
        # 2. 边界风险
        boundary_risk = self._calculate_boundary_risk(state, context)
        risk_score += boundary_risk * 0.2
        
        # 3. 运动状态风险
        motion_risk = self._calculate_motion_risk(state)
        risk_score += motion_risk * 0.2
        
        # 4. 任务风险
        mission_risk = self._calculate_mission_risk(state, context)
        risk_score += mission_risk * 0.2
        
        # 确定风险等级
        if risk_score < self.risk_thresholds['low']:
            return RiskLevel.LOW
        elif risk_score < self.risk_thresholds['medium']:
            return RiskLevel.MEDIUM
        elif risk_score < self.risk_thresholds['high']:
            return RiskLevel.HIGH
        else:
            return RiskLevel.CRITICAL
    
    def _calculate_obstacle_risk(self, state, context):
        """计算障碍物风险"""
        if not context or 'obstacles' not in context:
            return 0.0
        
        position = state[:3]
        obstacles = context['obstacles']
        
        min_distance = float('inf')
        for obstacle in obstacles:
            distance = np.linalg.norm(position - obstacle['center'])
            safe_distance = obstacle['radius'] + 5.0  # 安全距离
            if distance < safe_distance:
                risk = 1.0 - (distance / safe_distance)
                min_distance = min(min_distance, risk)
        
        return min_distance if min_distance != float('inf') else 0.0
    
    def _calculate_boundary_risk(self, state, context):
        """计算边界风险"""
        if not context or 'bounds' not in context:
            return 0.0
        
        position = state[:3]
        bounds = context['bounds']
        
        # 计算到边界的距离
        distances_to_boundary = [
            position[0], position[1], position[2],  # 到下边界
            bounds[0] - position[0], bounds[1] - position[1], bounds[2] - position[2]  # 到上边界
        ]
        
        min_boundary_distance = min(distances_to_boundary)
        safe_boundary_distance = 50.0  # 安全边界距离
        
        if min_boundary_distance < safe_boundary_distance:
            return 1.0 - (min_boundary_distance / safe_boundary_distance)
        return 0.0
    
    def _calculate_motion_risk(self, state):
        """计算运动状态风险"""
        V, gamma, psi = state[3], state[4], state[5]
        
        # 速度风险
        V_risk = 0.0
        if V < 18.0 or V > 55.0:  # 接近失速或最大速度
            V_risk = 0.5
        elif V < 20.0 or V > 50.0:  # 接近危险速度范围
            V_risk = 0.2
        
        # 角度风险
        angle_risk = 0.0
        if abs(gamma) > np.pi/4:  # 倾斜角过大
            angle_risk = 0.3
        elif abs(gamma) > np.pi/6:  # 倾斜角较大
            angle_risk = 0.1
        
        return (V_risk + angle_risk) / 2.0
    
    def _calculate_mission_risk(self, state, context):
        """计算任务风险"""
        if not context:
            return 0.0
        
        # 燃料风险
        fuel_risk = 0.0
        if 'fuel' in context and 'max_fuel' in context:
            fuel_ratio = context['fuel'] / context['max_fuel']
            if fuel_ratio < 0.2:  # 燃料不足
                fuel_risk = 0.8
            elif fuel_ratio < 0.4:  # 燃料较少
                fuel_risk = 0.4
        
        # 时间风险
        time_risk = 0.0
        if 'time' in context and 'max_time' in context:
            time_ratio = context['time'] / context['max_time']
            if time_ratio > 0.8:  # 时间不足
                time_risk = 0.6
            elif time_ratio > 0.6:  # 时间紧张
                time_risk = 0.3
        
        return (fuel_risk + time_risk) / 2.0
    
    def _get_dynamic_weights(self, risk_level):
        """获取动态权重"""
        base_weights = self.base_weights.copy()
        adjustments = self.risk_adjustments[risk_level]
        
        dynamic_weights = {}
        for key in base_weights:
            dynamic_weights[key] = base_weights[key] * adjustments[key]
        
        return dynamic_weights
    
    def _calculate_reward_components(self, state, action, next_state, context):
        """计算各组件奖励"""
        components = {}
        
        # 1. 距离奖励
        components['distance'] = self._calculate_distance_reward(state, next_state, context)
        
        # 2. 进度奖励
        components['progress'] = self._calculate_progress_reward(state, next_state, context)
        
        # 3. 安全奖励
        components['safety'] = self._calculate_safety_reward(state, action, context)
        
        # 4. 效率奖励
        components['efficiency'] = self._calculate_efficiency_reward(state, action)
        
        # 5. 平滑性奖励
        components['smoothness'] = self._calculate_smoothness_reward(state, action)
        
        return components
    
    def _calculate_distance_reward(self, state, next_state, context):
        """计算距离奖励"""
        if not context or 'goal' not in context:
            return 0.0
        
        goal = context['goal']
        current_dist = np.linalg.norm(state[:3] - goal)
        next_dist = np.linalg.norm(next_state[:3] - goal)
        
        # 距离改善奖励
        distance_improvement = current_dist - next_dist
        return distance_improvement * 0.1
    
    def _calculate_progress_reward(self, state, next_state, context):
        """计算进度奖励"""
        if not context or 'goal' not in context or 'start' not in context:
            return 0.0
        
        start = context['start']
        goal = context['goal']
        
        # 计算在起点到目标连线上的投影进度
        path_vector = goal - start
        path_length = np.linalg.norm(path_vector)
        
        if path_length == 0:
            return 0.0
        
        current_projection = np.dot(state[:3] - start, path_vector) / path_length
        next_projection = np.dot(next_state[:3] - start, path_vector) / path_length
        
        progress_improvement = next_projection - current_projection
        return progress_improvement * 0.2
    
    def _calculate_safety_reward(self, state, action, context):
        """计算安全奖励"""
        # 基于风险等级的安全奖励
        risk_level = self._assess_risk_level(state, context)
        
        if risk_level == RiskLevel.LOW:
            return 2.0  # 低风险时给予安全奖励
        elif risk_level == RiskLevel.MEDIUM:
            return 0.0  # 中等风险时中性
        elif risk_level == RiskLevel.HIGH:
            return -1.0  # 高风险时惩罚
        else:  # CRITICAL
            return -3.0  # 临界风险时严重惩罚
    
    def _calculate_efficiency_reward(self, state, action):
        """计算效率奖励"""
        V = state[3]
        target_speed = 25.0  # 目标巡航速度
        
        # 速度效率奖励
        speed_efficiency = 1.0 - abs(V - target_speed) / target_speed
        speed_reward = max(0, speed_efficiency) * 0.5
        
        # 控制效率奖励（避免过度控制）
        a_T, a_N, mu = action
        control_magnitude = np.sqrt(a_T**2 + a_N**2)
        control_efficiency = max(0, 1.0 - control_magnitude / 10.0)
        
        return speed_reward + control_efficiency * 0.3
    
    def _calculate_smoothness_reward(self, state, action):
        """计算平滑性奖励"""
        # 基于控制输入变化的平滑性
        if hasattr(self, '_prev_action') and self._prev_action is not None:
            prev_action = self._prev_action
            action_change = np.linalg.norm(action - prev_action)
            smoothness = max(0, 1.0 - action_change / 5.0)
            self._prev_action = action.copy()
            return smoothness * 0.2
        else:
            self._prev_action = action.copy()
            return 0.0
    
    def _apply_dynamic_weights(self, components, weights):
        """应用动态权重"""
        total_reward = 0.0
        
        for component_name, component_value in components.items():
            if component_name in weights:
                weighted_value = component_value * weights[component_name]
                total_reward += weighted_value
        
        return total_reward
    
    def _record_history(self, risk_level, components, total_reward):
        """记录历史数据"""
        self.risk_history.append({
            'risk_level': risk_level,
            'risk_score': self._risk_level_to_score(risk_level),
            'timestamp': len(self.risk_history)
        })
        
        self.reward_history.append({
            'components': components,
            'total_reward': total_reward,
            'timestamp': len(self.reward_history)
        })
    
    def _risk_level_to_score(self, risk_level):
        """将风险等级转换为数值分数"""
        return {
            RiskLevel.LOW: 0.2,
            RiskLevel.MEDIUM: 0.5,
            RiskLevel.HIGH: 0.8,
            RiskLevel.CRITICAL: 1.0
        }[risk_level]
    
    def get_risk_statistics(self):
        """获取风险统计信息"""
        if not self.risk_history:
            return {}
        
        risk_scores = [record['risk_score'] for record in self.risk_history]
        
        return {
            'mean_risk': np.mean(risk_scores),
            'max_risk': np.max(risk_scores),
            'min_risk': np.min(risk_scores),
            'risk_std': np.std(risk_scores),
            'high_risk_ratio': sum(1 for score in risk_scores if score > 0.7) / len(risk_scores)
        }
    
    def get_reward_statistics(self):
        """获取奖励统计信息"""
        if not self.reward_history:
            return {}
        
        total_rewards = [record['total_reward'] for record in self.reward_history]
        
        return {
            'mean_reward': np.mean(total_rewards),
            'max_reward': np.max(total_rewards),
            'min_reward': np.min(total_rewards),
            'reward_std': np.std(total_rewards)
        }

class AdaptiveRiskReward(DynamicRiskAwareReward):
    """自适应风险奖励函数"""
    
    def __init__(self):
        super().__init__()
        self.adaptation_rate = 0.01
        self.performance_window = 100
        
    def adapt_weights(self, performance_metrics):
        """根据性能指标自适应调整权重"""
        if len(self.reward_history) < self.performance_window:
            return
        
        # 分析最近性能
        recent_rewards = [record['total_reward'] for record in self.reward_history[-self.performance_window:]]
        recent_risks = [record['risk_score'] for record in self.risk_history[-self.performance_window:]]
        
        avg_reward = np.mean(recent_rewards)
        avg_risk = np.mean(recent_risks)
        
        # 根据性能调整权重
        if avg_reward < -1.0 and avg_risk > 0.6:
            # 性能差且风险高，加强安全权重
            self._strengthen_safety_weights()
        elif avg_reward > 1.0 and avg_risk < 0.3:
            # 性能好且风险低，加强效率权重
            self._strengthen_efficiency_weights()
    
    def _strengthen_safety_weights(self):
        """加强安全权重"""
        for risk_level in RiskLevel:
            self.risk_adjustments[risk_level]['safety'] *= (1 + self.adaptation_rate)
            self.risk_adjustments[risk_level]['efficiency'] *= (1 - self.adaptation_rate * 0.5)
    
    def _strengthen_efficiency_weights(self):
        """加强效率权重"""
        for risk_level in RiskLevel:
            self.risk_adjustments[risk_level]['efficiency'] *= (1 + self.adaptation_rate)
            self.risk_adjustments[risk_level]['safety'] *= (1 - self.adaptation_rate * 0.5)

# 使用示例
if __name__ == "__main__":
    # 创建动态风险感知奖励函数
    reward_function = DynamicRiskAwareReward()
    
    # 示例状态和上下文
    state = np.array([100, 100, 100, 25, 0, 0])
    next_state = np.array([105, 102, 101, 26, 0.1, 0.05])
    action = np.array([1.0, 2.0, 0.1])
    
    context = {
        'goal': np.array([1000, 1000, 1000]),
        'start': np.array([0, 0, 0]),
        'obstacles': [{'center': np.array([200, 200, 200]), 'radius': 50}],
        'bounds': [2000, 2000, 200],
        'fuel': 800,
        'max_fuel': 1000,
        'time': 100,
        'max_time': 2000
    }
    
    # 计算奖励
    total_reward, components, risk_level = reward_function.calculate_reward(
        state, action, next_state, context
    )
    
    print(f"风险等级: {risk_level.name}")
    print(f"总奖励: {total_reward:.3f}")
    print(f"奖励组件: {components}")
    
    # 获取统计信息
    risk_stats = reward_function.get_risk_statistics()
    reward_stats = reward_function.get_reward_statistics()
    
    print(f"风险统计: {risk_stats}")
    print(f"奖励统计: {reward_stats}")
