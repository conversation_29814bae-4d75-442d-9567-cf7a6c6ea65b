"""
融合系统演示脚本
展示巡飞弹分阶段训练系统的基本功能
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from loitering_munition_environment import LoiteringMunitionEnvironment
from loitering_munition_dwa import LoiteringMunitionDWA
from environment_config import (
    get_environment_config, get_loitering_munition_config, print_config_summary
)

def demo_environment_visualization():
    """演示环境可视化"""
    print("🎭 环境可视化演示")
    print("=" * 30)
    
    # 创建不同复杂度的环境
    env_configs = [
        ("简单环境", "stage1_simple"),
        ("复杂环境", "stage2_complex"),
        ("动态环境", "stage3_dynamic")
    ]
    
    lm_config = get_loitering_munition_config()
    
    fig = plt.figure(figsize=(15, 5))
    
    for i, (name, env_name) in enumerate(env_configs):
        env_config = get_environment_config(env_name)
        env = LoiteringMunitionEnvironment(
            bounds=lm_config['bounds'],
            environment_config=env_config,
            reward_type='simplified'
        )
        
        state = env.reset()
        
        ax = fig.add_subplot(1, 3, i+1, projection='3d')
        
        # 绘制起点和目标
        ax.scatter(*env.start, color='green', s=100, label='起点')
        ax.scatter(*env.goal, color='red', s=100, label='目标')
        
        # 绘制静态障碍物
        for obs in env.obstacles:
            u = np.linspace(0, 2 * np.pi, 10)
            v = np.linspace(0, np.pi, 10)
            x = obs['center'][0] + obs['radius'] * np.outer(np.cos(u), np.sin(v))
            y = obs['center'][1] + obs['radius'] * np.outer(np.sin(u), np.sin(v))
            z = obs['center'][2] + obs['radius'] * np.outer(np.ones(np.size(u)), np.cos(v))
            ax.plot_surface(x, y, z, alpha=0.3, color='gray')
        
        # 绘制动态障碍物
        for obs in env.dynamic_obstacles:
            u = np.linspace(0, 2 * np.pi, 10)
            v = np.linspace(0, np.pi, 10)
            x = obs['center'][0] + obs['radius'] * np.outer(np.cos(u), np.sin(v))
            y = obs['center'][1] + obs['radius'] * np.outer(np.sin(u), np.sin(v))
            z = obs['center'][2] + obs['radius'] * np.outer(np.ones(np.size(u)), np.cos(v))
            ax.plot_surface(x, y, z, alpha=0.4, color='orange')
        
        ax.set_xlim(0, env.bounds[0])
        ax.set_ylim(0, env.bounds[1])
        ax.set_zlim(0, env.bounds[2])
        ax.set_xlabel('X (m)')
        ax.set_ylabel('Y (m)')
        ax.set_zlabel('Z (m)')
        ax.set_title(f'{name}\n静态:{len(env.obstacles)} 动态:{len(env.dynamic_obstacles)}')

        # 设置等比例坐标轴，确保球体显示为圆形
        try:
            ax.set_box_aspect([env.bounds[0], env.bounds[1], env.bounds[2]])
        except:
            # 如果set_box_aspect不支持，使用备用方法
            ax.set_aspect('equal')

        ax.legend()
        
        print(f"  {name}: 静态障碍物 {len(env.obstacles)}, 动态障碍物 {len(env.dynamic_obstacles)}")
    
    plt.tight_layout()
    plt.savefig('demo_environments.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print("  ✅ 环境可视化完成，图片已保存为 demo_environments.png")

def demo_dwa_control():
    """演示DWA控制"""
    print("\n🎭 DWA控制演示")
    print("=" * 30)
    
    # 创建环境和DWA控制器
    env_config = get_environment_config("stage1_simple")
    lm_config = get_loitering_munition_config()
    
    env = LoiteringMunitionEnvironment(
        bounds=lm_config['bounds'],
        environment_config=env_config,
        reward_type='simplified'
    )
    
    dwa = LoiteringMunitionDWA(dt=lm_config['dt'])
    
    # 重置环境
    state = env.reset()
    trajectory = []
    rewards = []
    
    print(f"  🎯 起点: {env.start}")
    print(f"  🏁 目标: {env.goal}")
    print(f"  📏 初始距离: {np.linalg.norm(env.start - env.goal):.2f}m")
    
    # 运行DWA控制
    max_steps = 100
    for step in range(max_steps):
        trajectory.append(env.state[:3].copy())
        
        # 使用DWA选择控制输入
        best_control = dwa.select_best_control(
            env.state, env.obstacles + env.dynamic_obstacles, env.goal
        )
        
        # 执行控制
        next_state, reward, done, info = env.step(best_control)
        rewards.append(reward)
        
        if done:
            print(f"  🏁 第 {step+1} 步完成")
            if info.get('success', False):
                print(f"  🏆 成功到达目标!")
            elif info.get('collision', False):
                print(f"  💥 发生碰撞")
            else:
                print(f"  🚫 其他终止原因")
            break
    
    trajectory = np.array(trajectory)
    
    # 可视化轨迹
    fig = plt.figure(figsize=(12, 9))
    ax = fig.add_subplot(111, projection='3d')
    
    # 绘制轨迹
    ax.plot(trajectory[:, 0], trajectory[:, 1], trajectory[:, 2], 
           'b-', linewidth=3, label='DWA轨迹')
    
    # 绘制起点和目标
    ax.scatter(*env.start, color='green', s=150, label='起点')
    ax.scatter(*env.goal, color='red', s=150, label='目标')
    
    # 绘制当前位置
    if len(trajectory) > 0:
        ax.scatter(*trajectory[-1], color='blue', s=100, label='最终位置')
    
    # 绘制障碍物
    for obs in env.obstacles:
        u = np.linspace(0, 2 * np.pi, 20)
        v = np.linspace(0, np.pi, 20)
        x = obs['center'][0] + obs['radius'] * np.outer(np.cos(u), np.sin(v))
        y = obs['center'][1] + obs['radius'] * np.outer(np.sin(u), np.sin(v))
        z = obs['center'][2] + obs['radius'] * np.outer(np.ones(np.size(u)), np.cos(v))
        ax.plot_surface(x, y, z, alpha=0.3, color='gray')
    
    ax.set_xlim(0, env.bounds[0])
    ax.set_ylim(0, env.bounds[1])
    ax.set_zlim(0, env.bounds[2])
    ax.set_xlabel('X (m)')
    ax.set_ylabel('Y (m)')
    ax.set_zlabel('Z (m)')
    ax.set_title('DWA控制轨迹演示')
    ax.legend()
    
    plt.savefig('demo_dwa_trajectory.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    # 显示统计信息
    final_distance = np.linalg.norm(trajectory[-1] - env.goal)
    total_reward = sum(rewards)
    
    print(f"  📊 轨迹统计:")
    print(f"    步数: {len(trajectory)}")
    print(f"    最终距离: {final_distance:.2f}m")
    print(f"    总奖励: {total_reward:.2f}")
    print(f"    平均奖励: {total_reward/len(rewards):.2f}")
    print("  ✅ DWA控制演示完成，轨迹图已保存为 demo_dwa_trajectory.png")

def demo_reward_function():
    """演示奖励函数"""
    print("\n🎭 奖励函数演示")
    print("=" * 30)
    
    # 创建环境
    env_config = get_environment_config("stage1_simple")
    lm_config = get_loitering_munition_config()
    
    env = LoiteringMunitionEnvironment(
        bounds=lm_config['bounds'],
        environment_config=env_config,
        reward_type='simplified'
    )
    
    state = env.reset()
    
    # 测试不同的控制输入对奖励的影响
    test_controls = [
        ([0.0, 0.0, 0.0], "无控制"),
        ([2.0, 0.0, 0.0], "加速"),
        ([-2.0, 0.0, 0.0], "减速"),
        ([0.0, 10.0, 0.0], "转向"),
        ([1.0, 5.0, 0.1], "组合控制")
    ]
    
    print(f"  🎯 初始位置: {env.state[:3]}")
    print(f"  🏁 目标位置: {env.goal}")
    print(f"  📏 初始距离: {np.linalg.norm(env.state[:3] - env.goal):.2f}m")
    print()
    
    for control, description in test_controls:
        # 重置到相同状态
        env.reset()
        
        # 执行控制
        next_state, reward, done, info = env.step(np.array(control))
        
        new_distance = np.linalg.norm(env.state[:3] - env.goal)
        
        print(f"  {description:10s}: 奖励 {reward:8.2f}, 新距离 {new_distance:6.2f}m")
        
        if done:
            if info.get('success', False):
                print(f"    🏆 到达目标!")
            elif info.get('collision', False):
                print(f"    💥 发生碰撞!")
            elif info.get('out_of_bounds', False):
                print(f"    🚫 超出边界!")
    
    print("  ✅ 奖励函数演示完成")

def demo_motion_model():
    """演示运动模型"""
    print("\n🎭 六自由度运动模型演示")
    print("=" * 30)
    
    # 创建环境
    env_config = get_environment_config("test_simple")
    lm_config = get_loitering_munition_config()
    
    env = LoiteringMunitionEnvironment(
        bounds=[1000, 1000, 100],  # 较小的环境便于观察
        environment_config=env_config,
        reward_type='simplified'
    )
    
    # 设置固定的起点和目标
    env.start = np.array([100, 100, 50])
    env.goal = np.array([800, 800, 50])
    env.obstacles = []  # 移除障碍物便于观察
    env.dynamic_obstacles = []
    
    state = env.reset()
    
    # 测试不同的运动模式
    motion_tests = [
        ([2.0, 0.0, 0.0], "直线加速", 20),
        ([0.0, 10.0, 0.0], "水平转向", 15),
        ([0.0, 0.0, 0.3], "倾斜转向", 15),
        ([1.0, 5.0, 0.1], "复合机动", 25)
    ]
    
    fig = plt.figure(figsize=(15, 10))
    
    for i, (control, description, steps) in enumerate(motion_tests):
        # 重置环境
        env.reset()
        trajectory = []
        velocities = []
        
        for step in range(steps):
            trajectory.append(env.state[:3].copy())
            velocities.append(env.state[3])  # 速度
            
            next_state, reward, done, info = env.step(np.array(control))
            
            if done:
                break
        
        trajectory = np.array(trajectory)
        
        # 绘制3D轨迹
        ax = fig.add_subplot(2, 2, i+1, projection='3d')
        ax.plot(trajectory[:, 0], trajectory[:, 1], trajectory[:, 2], 
               'b-', linewidth=2, label='轨迹')
        ax.scatter(*env.start, color='green', s=100, label='起点')
        ax.scatter(*env.goal, color='red', s=100, label='目标')
        
        if len(trajectory) > 0:
            ax.scatter(*trajectory[-1], color='blue', s=80, label='终点')
        
        ax.set_xlabel('X (m)')
        ax.set_ylabel('Y (m)')
        ax.set_zlabel('Z (m)')
        ax.set_title(f'{description}\n控制输入: {control}')
        ax.legend()
        
        print(f"  {description}: {len(trajectory)} 步, 最终速度 {velocities[-1]:.2f} m/s")
    
    plt.tight_layout()
    plt.savefig('demo_motion_model.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print("  ✅ 运动模型演示完成，图片已保存为 demo_motion_model.png")

def run_full_demo():
    """运行完整演示"""
    print("🎭 巡飞弹分阶段训练系统 - 完整演示")
    print("=" * 60)
    
    # 显示配置信息
    print_config_summary()
    
    # 运行各个演示
    demos = [
        demo_environment_visualization,
        demo_motion_model,
        demo_reward_function,
        demo_dwa_control
    ]
    
    for demo_func in demos:
        try:
            demo_func()
        except Exception as e:
            print(f"❌ 演示失败: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n🎉 完整演示结束!")
    print("📁 生成的图片文件:")
    print("  - demo_environments.png: 环境配置对比")
    print("  - demo_motion_model.png: 运动模型演示")
    print("  - demo_dwa_trajectory.png: DWA控制轨迹")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        demo_name = sys.argv[1].lower()
        if demo_name == "env":
            demo_environment_visualization()
        elif demo_name == "motion":
            demo_motion_model()
        elif demo_name == "reward":
            demo_reward_function()
        elif demo_name == "dwa":
            demo_dwa_control()
        else:
            print("可用的演示: env, motion, reward, dwa")
    else:
        run_full_demo()
