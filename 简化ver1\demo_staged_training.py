"""
分阶段训练演示脚本 - 简化ver1
运行一个简短的分阶段训练演示
"""

import os
import sys
from datetime import datetime

def demo_basic_staged_training():
    """演示基础分阶段训练"""
    print("🎯 分阶段训练演示 - 基础版本")
    print("=" * 50)
    
    try:
        from staged_training import StagedTrainer
        
        # 创建训练器（只训练第一阶段，每个阶段只训练很少的episodes）
        trainer = StagedTrainer(
            start_stage=1,
            end_stage=1,  # 只训练第一阶段
            seed=42,
            visualization_interval=5  # 每5个episodes生成一次图
        )
        
        # 修改训练配置为更少的episodes
        from environment_config import TRAINING_STAGES
        original_config = TRAINING_STAGES["stage1_simple"].copy()
        TRAINING_STAGES["stage1_simple"]["fixed_episodes"] = 5
        TRAINING_STAGES["stage1_simple"]["random_episodes"] = 5
        TRAINING_STAGES["stage1_simple"]["total_episodes"] = 10
        
        print("📋 演示配置:")
        print(f"  • 训练阶段: 1 (简单环境)")
        print(f"  • 随机场景探索: 5 episodes")
        print(f"  • 固定场景强化: 5 episodes")
        print(f"  • 总计: 10 episodes")
        print()
        
        # 运行训练
        results = trainer.run_staged_training()
        
        # 恢复原始配置
        TRAINING_STAGES["stage1_simple"] = original_config
        
        if results:
            print("\n✅ 基础分阶段训练演示完成!")
            return True
        else:
            print("\n❌ 基础分阶段训练演示失败")
            return False
            
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def demo_enhanced_staged_training():
    """演示增强分阶段训练"""
    print("\n🎯 分阶段训练演示 - 增强版本")
    print("=" * 50)
    
    try:
        from enhanced_staged_trainer import EnhancedStagedTrainer
        
        # 创建训练器
        trainer = EnhancedStagedTrainer(
            start_stage=1,
            end_stage=1,  # 只训练第一阶段
            seed=42,
            visualization_interval=5
        )
        
        # 修改训练配置
        from environment_config import TRAINING_STAGES
        original_config = TRAINING_STAGES["stage1_simple"].copy()
        TRAINING_STAGES["stage1_simple"]["fixed_episodes"] = 5
        TRAINING_STAGES["stage1_simple"]["random_episodes"] = 5
        TRAINING_STAGES["stage1_simple"]["total_episodes"] = 10
        
        print("📋 演示配置:")
        print(f"  • 训练阶段: 1 (简单环境)")
        print(f"  • 随机场景探索: 5 episodes")
        print(f"  • 固定场景强化: 5 episodes")
        print(f"  • 总计: 10 episodes")
        print()
        
        # 运行训练
        results, controller = trainer.run_enhanced_staged_training()
        
        # 恢复原始配置
        TRAINING_STAGES["stage1_simple"] = original_config
        
        if results:
            print("\n✅ 增强分阶段训练演示完成!")
            return True
        else:
            print("\n❌ 增强分阶段训练演示失败")
            return False
            
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def demo_environment_configs():
    """演示环境配置"""
    print("\n🌍 环境配置演示")
    print("=" * 50)
    
    try:
        from environment_config import print_all_configs
        print_all_configs()
        return True
    except Exception as e:
        print(f"❌ 环境配置演示失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 DWA-RL 分阶段训练系统演示")
    print("📅 演示时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print()
    
    # 演示环境配置
    print("1️⃣ 环境配置演示")
    config_success = demo_environment_configs()
    
    if not config_success:
        print("❌ 环境配置演示失败，停止演示")
        return
    
    # 演示基础分阶段训练
    print("\n2️⃣ 基础分阶段训练演示")
    basic_success = demo_basic_staged_training()
    
    # 演示增强分阶段训练
    print("\n3️⃣ 增强分阶段训练演示")
    enhanced_success = demo_enhanced_staged_training()
    
    # 总结
    print("\n" + "🎉" * 30)
    print("📊 演示结果总结:")
    print(f"  • 环境配置: {'✅ 成功' if config_success else '❌ 失败'}")
    print(f"  • 基础分阶段训练: {'✅ 成功' if basic_success else '❌ 失败'}")
    print(f"  • 增强分阶段训练: {'✅ 成功' if enhanced_success else '❌ 失败'}")
    
    if all([config_success, basic_success, enhanced_success]):
        print("\n🎉 所有演示都成功完成！")
        print("\n💡 现在您可以使用以下命令开始完整训练:")
        print("   python run_staged_training.py")
        print("   或者")
        print("   python staged_training.py --start-stage 1 --end-stage 3")
        print("   或者")
        print("   python enhanced_staged_trainer.py --start-stage 1 --end-stage 3")
    else:
        print("\n⚠️ 部分演示失败，请检查相关问题")
    
    print("\n👋 演示结束")

if __name__ == "__main__":
    main()
