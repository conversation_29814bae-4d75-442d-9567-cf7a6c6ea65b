"""
基础测试脚本
"""

import os
import sys

def main():
    print("🔧 基础测试开始")
    print(f"Python版本: {sys.version}")
    print(f"当前目录: {os.getcwd()}")
    
    # 检查目标目录
    target_dir = "../loitering_munition_staged_training_20250726_085626"
    print(f"目标目录存在: {os.path.exists(target_dir)}")
    
    if os.path.exists(target_dir):
        files = os.listdir(target_dir)
        model_files = [f for f in files if f.endswith('.pth')]
        print(f"找到模型文件: {model_files}")
    
    print("✅ 基础测试完成")

if __name__ == "__main__":
    main()
