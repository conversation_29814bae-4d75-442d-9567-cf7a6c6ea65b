# 字体显示修复指南

## 🔍 问题描述

在运行训练脚本时，您可能遇到以下问题：
- 图表中的中文文字显示为小方框 □□□
- matplotlib警告：`Glyph missing from font(s)`
- 图表标题和标签无法正常显示中文

## 🛠️ 解决方案

### 方法1: 自动修复（推荐）

我们已经在训练脚本中集成了自动字体检测和修复功能：

```bash
cd 简化ver
python train_simplified_reward.py --episodes 200
```

脚本会自动：
1. 检测系统可用的中文字体
2. 设置最佳字体配置
3. 如果没有中文字体，自动使用英文标签

### 方法2: 手动检测和修复

运行字体修复工具：

```bash
cd 简化ver
python fix_font_display.py
```

这个工具会：
- 检测系统所有可用字体
- 测试中文字体显示效果
- 生成字体配置文件
- 提供字体安装建议

## 📊 修复效果对比

### 修复前
```
⚠️ UserWarning: Glyph 31616 (\N{CJK UNIFIED IDEOGRAPH-7B80}) missing from font(s) DejaVu Sans.
```
图表显示：`□□□□□□□ - Episode 1`

### 修复后
```
✅ 中文字体设置成功: SimHei
```
图表显示：`简化奖励函数训练 - Episode 1`

## 🎯 字体支持状态

### Windows系统
- ✅ **SimHei** (黑体) - 推荐
- ✅ **Microsoft YaHei** (微软雅黑)
- ✅ **SimSun** (宋体)
- ✅ **KaiTi** (楷体)

### macOS系统
- ✅ **PingFang SC** (苹方)
- ✅ **Hiragino Sans GB** (冬青黑体)

### Linux系统
- ✅ **WenQuanYi Micro Hei** (文泉驿微米黑)
- ✅ **Noto Sans CJK SC** (思源黑体)

## 🔧 手动安装字体（如果需要）

### Windows
1. 下载字体文件（.ttf 或 .otf）
2. 右键点击字体文件 → "安装"
3. 重启Python程序

### macOS
```bash
# 使用Homebrew安装
brew install font-noto-sans-cjk
```

### Linux (Ubuntu/Debian)
```bash
sudo apt-get install fonts-noto-cjk
# 或
sudo apt-get install fonts-wqy-microhei
```

### Linux (CentOS/RHEL)
```bash
sudo yum install google-noto-sans-cjk-fonts
```

## 🎨 显示模式说明

### 中文模式（有中文字体）
- 图表标题：`简化奖励函数训练 - Episode 1`
- 轴标签：`Episode`, `奖励`, `成功率`
- 图例：`成功`, `碰撞`, `超时`

### 英文模式（无中文字体）
- 图表标题：`Simplified Reward Training - Episode 1`
- 轴标签：`Episode`, `Reward`, `Success Rate`
- 图例：`Success`, `Collision`, `Timeout`

## 🧪 测试字体效果

运行测试脚本：
```bash
python fix_font_display.py
```

会生成 `font_test_result.png` 文件，检查：
- 中文文字是否正常显示
- 数字和符号是否正确
- 负号是否显示为方框

## 📝 常见问题

### Q: 为什么有些字体显示不正常？
A: 可能是字体文件损坏或不完整，尝试重新安装字体。

### Q: Linux系统下字体安装后仍不生效？
A: 运行以下命令刷新字体缓存：
```bash
fc-cache -fv
```

### Q: 可以强制使用英文标签吗？
A: 可以，修改训练脚本中的 `self.chinese_font_available = False`

### Q: 字体文件在哪里？
A: 
- Windows: `C:\Windows\Fonts\`
- macOS: `/System/Library/Fonts/` 或 `/Library/Fonts/`
- Linux: `/usr/share/fonts/` 或 `~/.fonts/`

## 🎯 推荐配置

### 最佳字体选择
1. **Windows**: SimHei (黑体)
2. **macOS**: PingFang SC (苹方)
3. **Linux**: WenQuanYi Micro Hei (文泉驿微米黑)

### 备用方案
如果中文字体有问题，脚本会自动切换到英文模式，功能完全不受影响。

## ✅ 验证修复成功

修复成功的标志：
1. 运行训练脚本时显示：`✅ 中文字体设置成功: [字体名]`
2. 生成的图片中中文正常显示
3. 没有字体警告信息

---

**现在您可以享受完美的中文字体显示效果了！** 🎉
