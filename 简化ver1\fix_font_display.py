"""
字体显示修复脚本
解决matplotlib中文字体显示问题
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import platform
import os

def check_available_fonts():
    """检查系统可用字体"""
    print("🔍 检查系统可用字体...")
    
    # 获取所有字体
    font_list = [f.name for f in fm.fontManager.ttflist]
    
    # 常用中文字体
    chinese_fonts = [
        'SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi', 'FangSong',
        'Microsoft JhengHei', 'PingFang SC', 'Hiragino Sans GB',
        'WenQuanYi Micro Hei', 'Noto Sans CJK SC', 'Source Han Sans SC'
    ]
    
    print(f"📋 系统: {platform.system()}")
    print(f"📊 总字体数量: {len(font_list)}")
    
    available_chinese = []
    for font in chinese_fonts:
        if font in font_list:
            available_chinese.append(font)
            print(f"✅ 找到中文字体: {font}")
    
    if not available_chinese:
        print("❌ 未找到常用中文字体")
        print("📝 建议安装以下字体之一:")
        for font in chinese_fonts[:3]:
            print(f"   • {font}")
    
    return available_chinese

def setup_font():
    """设置最佳字体"""
    available_fonts = check_available_fonts()
    
    if available_fonts:
        # 使用第一个可用的中文字体
        best_font = available_fonts[0]
        plt.rcParams['font.sans-serif'] = [best_font, 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        print(f"🎯 设置字体为: {best_font}")
        return True, best_font
    else:
        # 使用英文字体
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial']
        plt.rcParams['axes.unicode_minus'] = False
        print("🎯 使用英文字体: DejaVu Sans")
        return False, 'DejaVu Sans'

def test_font_display():
    """测试字体显示效果"""
    print("\n🧪 测试字体显示效果...")
    
    has_chinese, font_name = setup_font()
    
    # 创建测试图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # 测试中文显示
    if has_chinese:
        ax1.plot([1, 2, 3], [1, 4, 2], 'b-o')
        ax1.set_title('中文字体测试')
        ax1.set_xlabel('横轴标签')
        ax1.set_ylabel('纵轴标签')
        ax1.text(2, 3, '这是中文文本', fontsize=12, ha='center')
    else:
        ax1.plot([1, 2, 3], [1, 4, 2], 'b-o')
        ax1.set_title('Font Test (English)')
        ax1.set_xlabel('X Axis Label')
        ax1.set_ylabel('Y Axis Label')
        ax1.text(2, 3, 'English Text', fontsize=12, ha='center')
    
    # 测试数字和符号
    ax2.plot([1, 2, 3], [-1, -2, -3], 'r-s')
    ax2.set_title('Numbers & Symbols Test')
    ax2.set_xlabel('Episode')
    ax2.set_ylabel('Reward')
    ax2.text(2, -2, 'Reward: -123.45', fontsize=12, ha='center')
    
    plt.tight_layout()
    
    # 保存测试图
    test_file = 'font_test_result.png'
    plt.savefig(test_file, dpi=150, bbox_inches='tight')
    print(f"💾 字体测试图已保存: {test_file}")
    
    plt.show()
    
    return has_chinese, font_name

def download_font_instructions():
    """提供字体下载说明"""
    print("\n📥 字体下载说明:")
    print("=" * 50)
    
    if platform.system() == 'Windows':
        print("Windows系统:")
        print("1. 通常已预装 SimHei 或 Microsoft YaHei")
        print("2. 如果没有，可以从以下位置下载:")
        print("   • Microsoft Store 搜索 '中文字体'")
        print("   • 下载 Noto Sans CJK SC 字体")
        print("3. 下载后双击安装即可")
        
    elif platform.system() == 'Darwin':  # macOS
        print("macOS系统:")
        print("1. 通常已预装 PingFang SC")
        print("2. 如果显示有问题，可以安装:")
        print("   • brew install font-noto-sans-cjk")
        
    else:  # Linux
        print("Linux系统:")
        print("1. Ubuntu/Debian:")
        print("   sudo apt-get install fonts-noto-cjk")
        print("2. CentOS/RHEL:")
        print("   sudo yum install google-noto-sans-cjk-fonts")
        print("3. 或下载 WenQuanYi 字体")

def create_font_config():
    """创建字体配置文件"""
    has_chinese, font_name = setup_font()
    
    config_content = f"""# matplotlib 字体配置
# 由 fix_font_display.py 自动生成

import matplotlib.pyplot as plt

def setup_matplotlib_font():
    \"\"\"设置matplotlib字体\"\"\"
    plt.rcParams['font.sans-serif'] = ['{font_name}', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    return {has_chinese}

# 自动执行
CHINESE_FONT_AVAILABLE = setup_matplotlib_font()
"""
    
    with open('font_config.py', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print(f"📄 字体配置文件已创建: font_config.py")
    print(f"🎯 推荐字体: {font_name}")
    print(f"🌏 中文支持: {'是' if has_chinese else '否'}")

def main():
    """主函数"""
    print("🔧 matplotlib 中文字体修复工具")
    print("=" * 50)
    
    # 检查字体
    available_fonts = check_available_fonts()
    
    # 测试显示
    has_chinese, font_name = test_font_display()
    
    # 创建配置
    create_font_config()
    
    # 提供建议
    if not has_chinese:
        download_font_instructions()
        print("\n💡 临时解决方案:")
        print("   • 训练脚本会自动使用英文标签")
        print("   • 功能不受影响，只是显示语言不同")
    else:
        print(f"\n✅ 字体配置成功！")
        print(f"   • 使用字体: {font_name}")
        print(f"   • 中文显示: 正常")
    
    print(f"\n🎯 下次运行训练脚本时会自动应用最佳字体设置")

if __name__ == "__main__":
    main()
