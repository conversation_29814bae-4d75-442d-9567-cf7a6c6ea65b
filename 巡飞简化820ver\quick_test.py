"""
快速测试DWA修复效果
"""

import numpy as np

def test_weight_fix():
    """测试权重修复"""
    print("测试DWA权重修复...")
    
    # 模拟修复前后的权重计算
    # 修复前的问题
    distance_weight_class = 0.3  # 类属性
    gamma_angle = 0.1  # 航迹角（会覆盖类属性）
    
    # 模拟评价分数
    heading_score = 0.8
    speed_score = 0.9
    distance_score = 0.7
    safety_score = 1.0
    
    # 修复前（错误）- gamma被覆盖
    wrong_total = (0.4 * heading_score +
                   0.2 * speed_score +
                   gamma_angle * distance_score +  # 使用了角度值0.1
                   0.1 * safety_score)
    
    # 修复后（正确）- 使用独立的distance_weight
    correct_total = (0.4 * heading_score +
                     0.2 * speed_score +
                     distance_weight_class * distance_score +  # 使用正确的0.3
                     0.1 * safety_score)
    
    print(f"修复前总分: {wrong_total:.3f}")
    print(f"修复后总分: {correct_total:.3f}")
    print(f"分数提升: {correct_total - wrong_total:.3f}")
    
    print(f"\n各项贡献:")
    print(f"  方向项: {0.4 * heading_score:.3f}")
    print(f"  速度项: {0.2 * speed_score:.3f}")
    print(f"  距离项(修复前): {gamma_angle * distance_score:.3f}")
    print(f"  距离项(修复后): {distance_weight_class * distance_score:.3f}")
    print(f"  安全项: {0.1 * safety_score:.3f}")
    
    improvement = (distance_weight_class - gamma_angle) * distance_score
    print(f"\n距离项改善: {improvement:.3f}")
    print(f"改善百分比: {(improvement/wrong_total)*100:.1f}%")
    
    return correct_total > wrong_total

def test_direction_preference():
    """测试方向偏好"""
    print("\n测试方向偏好...")
    
    # 模拟两个控制选项
    # 控制A: 朝向目标
    # 控制B: 偏离目标
    
    # 假设其他分数相同
    heading_score_A = 0.9  # 朝向目标
    heading_score_B = 0.1  # 偏离目标
    
    speed_score = 0.8
    distance_score = 0.7
    safety_score = 1.0
    
    # 使用修复后的权重
    alpha = 0.4
    beta = 0.2
    distance_weight = 0.3
    delta = 0.1
    
    score_A = (alpha * heading_score_A +
               beta * speed_score +
               distance_weight * distance_score +
               delta * safety_score)
    
    score_B = (alpha * heading_score_B +
               beta * speed_score +
               distance_weight * distance_score +
               delta * safety_score)
    
    print(f"朝向目标的控制分数: {score_A:.3f}")
    print(f"偏离目标的控制分数: {score_B:.3f}")
    print(f"分数差异: {score_A - score_B:.3f}")
    print(f"选择正确: {'✅' if score_A > score_B else '❌'}")
    
    return score_A > score_B

def test_import():
    """测试导入"""
    print("\n测试模块导入...")
    
    try:
        from loitering_munition_dwa import LoiteringMunitionDWA
        dwa = LoiteringMunitionDWA()
        
        # 检查修复后的属性
        if hasattr(dwa, 'distance_weight'):
            print(f"✅ distance_weight存在: {dwa.distance_weight}")
        else:
            print("❌ distance_weight不存在")
            return False
        
        # 检查是否还有旧的gamma属性冲突
        if hasattr(dwa, 'gamma') and not hasattr(dwa, 'distance_weight'):
            print("❌ 仍然存在gamma权重冲突")
            return False
        
        print("✅ DWA类导入成功，权重修复正确")
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

if __name__ == "__main__":
    print("🔧 快速测试DWA修复效果")
    print("=" * 40)
    
    test1 = test_weight_fix()
    test2 = test_direction_preference()
    test3 = test_import()
    
    print("\n" + "=" * 40)
    print("📊 测试结果:")
    print(f"  权重修复: {'✅' if test1 else '❌'}")
    print(f"  方向偏好: {'✅' if test2 else '❌'}")
    print(f"  模块导入: {'✅' if test3 else '❌'}")
    
    if all([test1, test2, test3]):
        print("\n🎉 修复成功！DWA现在应该能正确引导巡飞弹朝向目标。")
        print("建议：现在可以重新运行训练，应该会看到明显改善。")
    else:
        print("\n⚠️  修复可能不完整，需要进一步检查。")
