"""
神经网络约束预测器 - 替代传统DWA的核心创新
Physics-Informed Neural Network (PINN) for 3D Constraint Prediction
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Tuple, Dict, Optional

class PhysicsInformedConstraintNet(nn.Module):
    """
    物理信息神经网络约束预测器
    核心创新：用神经网络学习复杂的三维约束关系，替代传统DWA的离散化搜索
    """
    
    def __init__(self, state_dim=6, action_dim=3, hidden_dim=256, num_layers=4):
        super().__init__()
        
        # 状态编码器：处理位置、速度、环境信息
        self.state_encoder = nn.Sequential(
            nn.Linear(state_dim + 64, hidden_dim),  # +64 for environment encoding
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU()
        )
        
        # 物理约束嵌入层：学习物理规律
        self.physics_embedding = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim)
        )
        
        # 约束预测头：预测动作的安全性和可行性
        self.constraint_predictor = nn.Sequential(
            nn.Linear(hidden_dim + action_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, 4)  # [safety_score, feasibility, physics_violation, collision_risk]
        )
        
        # 轨迹预测头：预测未来轨迹
        self.trajectory_predictor = nn.Sequential(
            nn.Linear(hidden_dim + action_dim, hidden_dim),
            nn.ReLU(),
            nn.LSTM(hidden_dim, hidden_dim // 2, batch_first=True),
        )
        self.traj_output = nn.Linear(hidden_dim // 2, 3)  # 预测位置
        
        # 环境编码器：处理障碍物信息
        self.obstacle_encoder = nn.Sequential(
            nn.Linear(7, 32),  # [x,y,z,r,vx,vy,vz] for each obstacle
            nn.ReLU(),
            nn.Linear(32, 16)
        )
        
        # 注意力机制：处理可变数量的障碍物
        self.attention = nn.MultiheadAttention(16, num_heads=4, batch_first=True)
        
    def encode_environment(self, obstacles: List[Dict]) -> torch.Tensor:
        """编码环境信息（障碍物）"""
        if not obstacles:
            return torch.zeros(1, 64)
            
        # 编码每个障碍物
        obs_features = []
        for obs in obstacles[:10]:  # 最多处理10个障碍物
            center = obs.get('center', [0, 0, 0])
            radius = obs.get('radius', 1.0)
            velocity = obs.get('velocity', [0, 0, 0])
            
            obs_vec = torch.tensor([
                center[0], center[1], center[2], radius,
                velocity[0], velocity[1], velocity[2]
            ], dtype=torch.float32)
            obs_features.append(obs_vec)
        
        # 填充到固定长度
        while len(obs_features) < 10:
            obs_features.append(torch.zeros(7))
            
        obs_tensor = torch.stack(obs_features).unsqueeze(0)  # [1, 10, 7]
        
        # 编码障碍物特征
        encoded_obs = self.obstacle_encoder(obs_tensor)  # [1, 10, 16]
        
        # 注意力聚合
        attended_obs, _ = self.attention(encoded_obs, encoded_obs, encoded_obs)
        
        # 全局池化
        env_encoding = attended_obs.mean(dim=1)  # [1, 16]
        
        # 扩展到64维
        env_encoding = F.pad(env_encoding, (0, 48))  # [1, 64]
        
        return env_encoding
    
    def physics_loss(self, state: torch.Tensor, action: torch.Tensor, 
                    predicted_traj: torch.Tensor) -> torch.Tensor:
        """
        物理信息损失：确保预测轨迹符合物理规律
        这是PINN的核心 - 将物理约束嵌入到损失函数中
        """
        dt = 0.1
        g = 9.81
        
        # 提取状态
        pos = state[:, :3]  # [x, y, z]
        vel = state[:, 3:6]  # [vx, vy, vz]
        
        # 计算预测的速度变化
        pred_vel_change = action  # 假设action是速度变化
        
        # 物理约束1：重力影响
        gravity_constraint = torch.abs(pred_vel_change[:, 2] + g * dt)
        
        # 物理约束2：最大加速度限制
        max_accel = 15.0
        accel_magnitude = torch.norm(pred_vel_change / dt, dim=1)
        accel_constraint = F.relu(accel_magnitude - max_accel)
        
        # 物理约束3：速度连续性
        new_vel = vel + pred_vel_change
        max_vel = 30.0
        vel_constraint = F.relu(torch.norm(new_vel, dim=1) - max_vel)
        
        # 物理约束4：轨迹连续性（如果有轨迹预测）
        traj_constraint = torch.tensor(0.0)
        if predicted_traj is not None and predicted_traj.shape[1] > 1:
            # 检查轨迹的物理合理性
            traj_diff = predicted_traj[:, 1:] - predicted_traj[:, :-1]
            traj_vel = traj_diff / dt
            traj_accel = (traj_vel[:, 1:] - traj_vel[:, :-1]) / dt
            traj_constraint = F.relu(torch.norm(traj_accel, dim=2) - max_accel).mean()
        
        total_physics_loss = (gravity_constraint.mean() + 
                            accel_constraint.mean() + 
                            vel_constraint.mean() + 
                            traj_constraint)
        
        return total_physics_loss
    
    def forward(self, state: torch.Tensor, action: torch.Tensor, 
                obstacles: List[Dict]) -> Dict[str, torch.Tensor]:
        """
        前向传播：预测动作的约束满足情况
        
        Returns:
            - safety_score: 安全性评分 [0,1]
            - feasibility: 可行性评分 [0,1] 
            - physics_violation: 物理违反程度 [0,1]
            - collision_risk: 碰撞风险 [0,1]
            - predicted_trajectory: 预测轨迹
        """
        batch_size = state.shape[0]
        
        # 编码环境
        env_encoding = self.encode_environment(obstacles)
        env_encoding = env_encoding.expand(batch_size, -1)
        
        # 编码状态
        state_with_env = torch.cat([state, env_encoding], dim=1)
        state_features = self.state_encoder(state_with_env)
        
        # 物理约束嵌入
        physics_features = self.physics_embedding(state_features)
        
        # 约束预测
        constraint_input = torch.cat([physics_features, action], dim=1)
        constraint_scores = self.constraint_predictor(constraint_input)
        
        # 轨迹预测
        traj_input = constraint_input.unsqueeze(1).expand(-1, 20, -1)  # 20步预测
        traj_features, _ = self.trajectory_predictor(traj_input)
        predicted_positions = self.traj_output(traj_features)
        
        # 应用sigmoid确保输出在[0,1]范围内
        safety_score = torch.sigmoid(constraint_scores[:, 0])
        feasibility = torch.sigmoid(constraint_scores[:, 1])
        physics_violation = torch.sigmoid(constraint_scores[:, 2])
        collision_risk = torch.sigmoid(constraint_scores[:, 3])
        
        return {
            'safety_score': safety_score,
            'feasibility': feasibility,
            'physics_violation': physics_violation,
            'collision_risk': collision_risk,
            'predicted_trajectory': predicted_positions,
            'physics_features': physics_features
        }

class NeuralConstraintPredictor:
    """
    神经网络约束预测器 - DWA的智能替代
    核心创新：用学习的方式替代传统的离散化搜索
    """
    
    def __init__(self, device='cpu'):
        self.device = device
        self.net = PhysicsInformedConstraintNet().to(device)
        self.optimizer = torch.optim.Adam(self.net.parameters(), lr=0.001)
        
        # 训练统计
        self.training_history = {
            'constraint_loss': [],
            'physics_loss': [],
            'total_loss': []
        }
    
    def generate_safe_action_set(self, state: np.ndarray, obstacles: List[Dict], 
                               goal: np.ndarray, num_candidates: int = 50) -> List[Dict]:
        """
        生成安全动作集 - 替代传统DWA的核心功能
        
        创新点：
        1. 用神经网络快速评估大量候选动作
        2. 避免了传统DWA的离散化搜索
        3. 能够学习复杂的约束模式
        """
        self.net.eval()
        
        with torch.no_grad():
            # 生成候选动作（比DWA更智能的采样）
            candidate_actions = self._generate_candidate_actions(
                state, goal, num_candidates
            )
            
            # 批量评估所有候选动作
            state_tensor = torch.tensor(state[:6], dtype=torch.float32).unsqueeze(0)
            state_batch = state_tensor.expand(num_candidates, -1)
            action_batch = torch.tensor(candidate_actions, dtype=torch.float32)
            
            # 神经网络预测
            predictions = self.net(state_batch, action_batch, obstacles)
            
            # 筛选安全动作
            safe_actions = []
            for i in range(num_candidates):
                safety = predictions['safety_score'][i].item()
                feasibility = predictions['feasibility'][i].item()
                physics_ok = 1.0 - predictions['physics_violation'][i].item()
                collision_safe = 1.0 - predictions['collision_risk'][i].item()
                
                # 综合评分
                total_score = (safety * 0.3 + feasibility * 0.2 + 
                             physics_ok * 0.3 + collision_safe * 0.2)
                
                if total_score > 0.7:  # 安全阈值
                    safe_actions.append({
                        'action': candidate_actions[i],
                        'safety_score': safety,
                        'feasibility': feasibility,
                        'total_score': total_score,
                        'predicted_trajectory': predictions['predicted_trajectory'][i].cpu().numpy()
                    })
            
            # 按评分排序
            safe_actions.sort(key=lambda x: x['total_score'], reverse=True)
            
            return safe_actions[:20]  # 返回前20个最优动作
    
    def _generate_candidate_actions(self, state: np.ndarray, goal: np.ndarray, 
                                  num_candidates: int) -> np.ndarray:
        """
        智能候选动作生成 - 比DWA的均匀采样更高效
        """
        # 目标导向的采样
        goal_direction = goal - state[:3]
        goal_direction = goal_direction / (np.linalg.norm(goal_direction) + 1e-8)
        
        candidates = []
        
        # 1. 目标导向动作（50%）
        for _ in range(num_candidates // 2):
            # 在目标方向附近采样
            noise = np.random.normal(0, 0.3, 3)
            action = goal_direction * np.random.uniform(0.5, 2.0) + noise
            action = np.clip(action, -3.0, 3.0)
            candidates.append(action)
        
        # 2. 随机探索动作（30%）
        for _ in range(num_candidates * 3 // 10):
            action = np.random.uniform(-2.0, 2.0, 3)
            candidates.append(action)
        
        # 3. 保守安全动作（20%）
        for _ in range(num_candidates - len(candidates)):
            # 小幅度动作
            action = np.random.uniform(-1.0, 1.0, 3)
            candidates.append(action)
        
        return np.array(candidates)
    
    def train_step(self, batch_data: Dict) -> Dict[str, float]:
        """
        训练步骤 - 从经验中学习约束模式
        """
        self.net.train()
        
        states = batch_data['states']
        actions = batch_data['actions']
        obstacles = batch_data['obstacles']
        safety_labels = batch_data['safety_labels']
        
        # 前向传播
        predictions = self.net(states, actions, obstacles)
        
        # 约束预测损失
        constraint_loss = F.mse_loss(predictions['safety_score'], safety_labels)
        
        # 物理信息损失
        physics_loss = self.net.physics_loss(
            states, actions, predictions['predicted_trajectory']
        )
        
        # 总损失
        total_loss = constraint_loss + 0.1 * physics_loss
        
        # 反向传播
        self.optimizer.zero_grad()
        total_loss.backward()
        self.optimizer.step()
        
        # 记录统计
        self.training_history['constraint_loss'].append(constraint_loss.item())
        self.training_history['physics_loss'].append(physics_loss.item())
        self.training_history['total_loss'].append(total_loss.item())
        
        return {
            'constraint_loss': constraint_loss.item(),
            'physics_loss': physics_loss.item(),
            'total_loss': total_loss.item()
        }
