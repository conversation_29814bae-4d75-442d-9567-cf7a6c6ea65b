"""
巡飞弹DWA控制器 - 完整集成版本
基于六自由度运动学模型的动态窗口算法
"""

import numpy as np
import math

class LoiteringMunitionDWA:
    """巡飞弹动态窗口算法控制器 - 改进版本"""

    def __init__(self, dt=0.1):
        self.dt = dt
        self.g = 9.81  # 重力加速度

        # 巡飞弹运动约束（与环境一致）
        self.V_min = 15.0    # 最小速度（失速速度）
        self.V_max = 60.0    # 最大速度
        self.V_cruise = 25.0 # 巡航速度（合理的初始速度）
        self.a_T_max = 8.0   # 最大切向加速度
        self.a_N_max = 39.24 # 最大法向加速度（4g）
        self.gamma_max = np.pi/3  # 最大航迹倾斜角（60°）

        # DWA参数 - 优化计算效率
        self.predict_time = 3.0  # 预测时间窗口
        self.min_safe_distance = 5.0  # 最小安全距离

        # 控制输入离散化参数（优化计算效率）
        self.a_T_resolution = 1.5    # 切向加速度分辨率（更细粒度）
        self.a_N_resolution = 6.0    # 法向加速度分辨率（更细粒度）
        self.mu_resolution = 0.15    # 倾斜角分辨率（更细粒度）

        # 评价函数权重
        self.alpha = 0.4   # 目标方向权重
        self.beta = 0.2    # 速度权重（鼓励巡航速度）
        self.gamma = 0.3   # 距离权重
        self.delta = 0.1   # 障碍物权重
        
    def generate_safe_control_set(self, current_state, obstacles, goal, max_actions=20):
        """
        生成安全控制输入集合 - 改进版本

        Args:
            current_state: [x, y, z, V, γ, ψ] - 当前状态
            obstacles: 障碍物列表
            goal: 目标位置
            max_actions: 最大动作数量

        Returns:
            安全的控制输入列表，按评价分数排序
        """
        x, y, z, V, gamma, psi = current_state

        # 计算动态窗口（基于当前状态的可达加速度范围）
        dw = self._calc_dynamic_window(current_state)

        safe_controls_with_scores = []

        # 遍历控制输入空间（使用优化的分辨率）
        a_T_range = np.arange(dw['a_T_min'], dw['a_T_max'] + self.a_T_resolution,
                             self.a_T_resolution)
        a_N_range = np.arange(dw['a_N_min'], dw['a_N_max'] + self.a_N_resolution,
                             self.a_N_resolution)
        mu_range = np.arange(dw['mu_min'], dw['mu_max'] + self.mu_resolution,
                            self.mu_resolution)

        for a_T in a_T_range:
            for a_N in a_N_range:
                for mu in mu_range:
                    control = np.array([a_T, a_N, mu])

                    # 预测轨迹
                    trajectory = self._predict_trajectory(current_state, control)

                    # 安全性检查
                    if self._is_trajectory_safe(trajectory, obstacles):
                        # 评价控制输入
                        score = self._evaluate_control_input(
                            current_state, control, trajectory, goal, obstacles
                        )

                        safe_controls_with_scores.append({
                            'control': control,
                            'trajectory': trajectory,
                            'score': score
                        })

        # 按评价分数排序，选择最优的动作
        safe_controls_with_scores.sort(key=lambda x: x['score'], reverse=True)

        # 限制返回的动作数量
        safe_controls_with_scores = safe_controls_with_scores[:max_actions]

        # 如果没有安全动作，生成紧急制动动作
        if not safe_controls_with_scores:
            emergency_control = self._generate_emergency_action(current_state)
            safe_controls_with_scores = [emergency_control]

        # 返回控制输入列表（保持向后兼容）
        return [item['control'] for item in safe_controls_with_scores]

    def _calc_dynamic_window(self, state):
        """计算动态窗口（可达加速度范围）"""
        x, y, z, V, gamma, psi = state

        # 切向加速度窗口（考虑速度约束）
        a_T_min = max(-self.a_T_max, (self.V_min - V) / self.dt)
        a_T_max = min(self.a_T_max, (self.V_max - V) / self.dt)

        # 法向加速度窗口
        a_N_min = -self.a_N_max
        a_N_max = self.a_N_max

        # 倾斜角窗口
        mu_min = -np.pi/2
        mu_max = np.pi/2

        return {
            'a_T_min': a_T_min,
            'a_T_max': a_T_max,
            'a_N_min': a_N_min,
            'a_N_max': a_N_max,
            'mu_min': mu_min,
            'mu_max': mu_max
        }

    def _predict_trajectory(self, initial_state, control_input):
        """
        预测轨迹（六自由度运动学模型）

        Args:
            initial_state: [x, y, z, V, γ, ψ]
            control_input: [a_T, a_N, μ]

        Returns:
            trajectory: N×6 数组，每行为 [x, y, z, V, γ, ψ]
        """
        state = initial_state.copy()
        trajectory = [state.copy()]

        a_T, a_N, mu = control_input
        predict_steps = int(self.predict_time / self.dt)

        for _ in range(predict_steps):
            x, y, z, V, gamma, psi = state

            # 六自由度运动学方程积分
            # 位置更新
            x_new = x + V * np.cos(gamma) * np.cos(psi) * self.dt
            y_new = y + V * np.cos(gamma) * np.sin(psi) * self.dt
            z_new = z + V * np.sin(gamma) * self.dt

            # 速度更新（考虑重力影响）
            V_new = V + (a_T - self.g * np.sin(gamma)) * self.dt
            V_new = np.clip(V_new, self.V_min, self.V_max)

            # 角度更新（避免除零）
            if V > 0.1:
                gamma_new = gamma + (a_N * np.cos(mu) - self.g * np.cos(gamma)) / V * self.dt
                psi_new = psi + (a_N * np.sin(mu)) / (V * np.cos(gamma)) * self.dt
            else:
                gamma_new = gamma
                psi_new = psi

            # 角度约束
            gamma_new = np.clip(gamma_new, -self.gamma_max, self.gamma_max)

            # 更新状态
            state = np.array([x_new, y_new, z_new, V_new, gamma_new, psi_new])
            trajectory.append(state.copy())

        return np.array(trajectory)

    def _is_trajectory_safe(self, trajectory, obstacles):
        """检查轨迹是否安全（无碰撞）"""
        for point in trajectory:
            pos = point[:3]
            for obs in obstacles:
                distance = np.linalg.norm(pos - obs['center'])
                if distance <= obs['radius'] + self.min_safe_distance:
                    return False
        return True

    def _evaluate_control_input(self, current_state, control_input, trajectory, goal, obstacles):
        """评价控制输入的质量"""
        final_state = trajectory[-1]
        final_pos = final_state[:3]
        final_V = final_state[3]

        # 1. 目标方向评价
        goal_direction = goal - final_pos
        goal_dist = np.linalg.norm(goal_direction)

        if goal_dist > 0:
            goal_direction_norm = goal_direction / goal_dist
            # 当前速度方向
            current_vel_direction = np.array([
                final_V * np.cos(final_state[4]) * np.cos(final_state[5]),
                final_V * np.cos(final_state[4]) * np.sin(final_state[5]),
                final_V * np.sin(final_state[4])
            ])
            if np.linalg.norm(current_vel_direction) > 0:
                current_vel_direction_norm = current_vel_direction / np.linalg.norm(current_vel_direction)
                heading_score = np.dot(goal_direction_norm, current_vel_direction_norm)
            else:
                heading_score = 0
        else:
            heading_score = 1.0

        # 2. 速度评价（鼓励接近巡航速度）
        speed_score = 1.0 - abs(final_V - self.V_cruise) / (self.V_max - self.V_min)

        # 3. 距离评价（鼓励接近目标）
        distance_score = 1.0 / (1.0 + goal_dist / 100.0)

        # 4. 安全评价（距离障碍物越远越好）
        min_obs_dist = float('inf')
        for obs in obstacles:
            dist = np.linalg.norm(final_pos - obs['center']) - obs['radius']
            min_obs_dist = min(min_obs_dist, dist)

        safety_score = min(min_obs_dist / 50.0, 1.0)

        # 综合评价
        total_score = (self.alpha * heading_score +
                      self.beta * speed_score +
                      self.gamma * distance_score +
                      self.delta * safety_score)

        return total_score

    def _generate_emergency_action(self, current_state):
        """生成紧急制动动作"""
        V = current_state[3]

        # 紧急制动：最大减速，无法向加速度
        emergency_control = np.array([
            -min(self.a_T_max, V / self.dt),  # 紧急减速
            0.0,  # 无法向加速度
            0.0   # 无倾斜角
        ])

        # 预测紧急制动轨迹
        emergency_trajectory = self._predict_trajectory(current_state, emergency_control)

        return {
            'control': emergency_control,
            'trajectory': emergency_trajectory,
            'score': -1000.0  # 低分数表示这是紧急动作
        }

    def get_initial_state(self, start_pos, goal_pos):
        """
        获取合理的初始状态

        Args:
            start_pos: 起始位置 [x, y, z]
            goal_pos: 目标位置 [x, y, z]

        Returns:
            initial_state: [x, y, z, V, γ, ψ]
        """
        # 计算初始朝向（指向目标）
        direction = goal_pos - start_pos
        distance = np.linalg.norm(direction)

        if distance > 0:
            # 计算初始偏航角和倾斜角
            psi_initial = np.arctan2(direction[1], direction[0])
            gamma_initial = np.arcsin(direction[2] / distance)
            # 限制倾斜角范围
            gamma_initial = np.clip(gamma_initial, -self.gamma_max, self.gamma_max)
        else:
            psi_initial = 0.0
            gamma_initial = 0.0

        return np.array([
            start_pos[0], start_pos[1], start_pos[2],
            self.V_cruise,  # 使用巡航速度作为初始速度
            gamma_initial,
            psi_initial
        ])


    
    def evaluate_control(self, control, current_state, goal, obstacles):
        """评价控制输入的优劣"""
        # 预测最终状态
        final_state = self._predict_final_state(current_state, control)
        final_pos = final_state[:3]
        final_V = final_state[3]
        final_gamma = final_state[4]
        final_psi = final_state[5]
        
        # 目标方向评价
        goal_direction = goal - final_pos
        goal_dist = np.linalg.norm(goal_direction)
        if goal_dist > 0:
            goal_direction = goal_direction / goal_dist
        
        # 当前朝向
        current_direction = np.array([
            np.cos(final_gamma) * np.cos(final_psi),
            np.cos(final_gamma) * np.sin(final_psi),
            np.sin(final_gamma)
        ])
        
        heading_score = max(0, np.dot(current_direction, goal_direction))
        
        # 速度评价（鼓励适中速度）
        optimal_speed = (self.V_min + self.V_max) / 2
        speed_score = 1.0 - abs(final_V - optimal_speed) / (self.V_max - self.V_min)
        
        # 距离评价
        distance_score = 1.0 / (1.0 + goal_dist / 200.0)
        
        # 障碍物距离评价
        min_obs_dist = float('inf')
        for obs in obstacles:
            dist = np.linalg.norm(final_pos - obs['center']) - obs['radius']
            min_obs_dist = min(min_obs_dist, dist)
        
        obstacle_score = min(min_obs_dist / 100.0, 1.0)
        
        # 综合评价
        total_score = (self.alpha * heading_score + 
                      self.beta * speed_score + 
                      self.gamma * distance_score + 
                      self.delta * obstacle_score)
        
        return total_score
    
    def _predict_final_state(self, initial_state, control):
        """预测给定控制输入下的最终状态"""
        state = initial_state.copy()
        a_T, a_N, mu = control
        
        # 简化预测：使用预测时间窗口的一半
        predict_steps = int(self.predict_time / (2 * self.dt))
        
        for _ in range(predict_steps):
            x, y, z, V, gamma, psi = state
            
            # 运动学方程积分
            x_new = x + V * np.cos(gamma) * np.cos(psi) * self.dt
            y_new = y + V * np.cos(gamma) * np.sin(psi) * self.dt
            z_new = z + V * np.sin(gamma) * self.dt
            
            V_new = V + (a_T - self.g * np.sin(gamma)) * self.dt
            V_new = np.clip(V_new, self.V_min, self.V_max)
            
            if V > 0.1:
                gamma_new = gamma + (a_N * np.cos(mu) - self.g * np.cos(gamma)) / V * self.dt
                psi_new = psi + (a_N * np.sin(mu)) / (V * np.cos(gamma)) * self.dt
            else:
                gamma_new = gamma
                psi_new = psi
            
            gamma_new = np.clip(gamma_new, -self.gamma_max, self.gamma_max)
            
            state = np.array([x_new, y_new, z_new, V_new, gamma_new, psi_new])
        
        return state
    
    def select_best_control(self, current_state, obstacles, goal):
        """选择最优控制输入"""
        safe_controls = self.generate_safe_control_set(current_state, obstacles, goal)
        
        if not safe_controls:
            # 紧急情况：返回制动控制
            return np.array([-self.a_T_max, 0.0, 0.0])
        
        best_control = None
        best_score = -float('inf')
        
        for control in safe_controls:
            score = self.evaluate_control(control, current_state, goal, obstacles)
            if score > best_score:
                best_score = score
                best_control = control
        
        return best_control if best_control is not None else safe_controls[0]
