"""
测试修复后的轨迹效果
运行简单训练并生成3D轨迹图
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

from loitering_munition_environment import LoiteringMunitionEnvironment
from loitering_munition_dwa import LoiteringMunitionDWA
from environment_config import get_environment_config

def simple_training_test():
    """简单训练测试"""
    print("🚀 测试修复后的训练轨迹")
    print("=" * 50)
    
    # 创建环境
    env_config = get_environment_config('stage1_simple')
    env = LoiteringMunitionEnvironment(
        bounds=[2000, 2000, 2000],
        environment_config=env_config,
        reward_type='simplified'
    )
    
    # 创建DWA
    dwa = LoiteringMunitionDWA()
    
    # 重置环境
    state = env.reset()
    
    print(f"起点: {env.start}")
    print(f"目标: {env.goal}")
    print(f"初始距离: {np.linalg.norm(env.start - env.goal):.2f}m")
    
    # 记录轨迹数据
    trajectory = [env.state[:3].copy()]
    distances = [np.linalg.norm(env.state[:3] - env.goal)]
    rewards = []
    actions_taken = []
    control_scores = []
    
    # 运行50步
    for step in range(50):
        print(f"\n--- 步骤 {step+1} ---")
        
        current_pos = env.state[:3]
        current_dist = np.linalg.norm(current_pos - env.goal)
        print(f"当前位置: [{current_pos[0]:.1f}, {current_pos[1]:.1f}, {current_pos[2]:.1f}]")
        print(f"到目标距离: {current_dist:.2f}m")
        
        # 生成安全控制
        safe_controls = dwa.generate_safe_control_set(
            env.state, env.obstacles + env.dynamic_obstacles, env.goal, max_actions=20
        )
        
        if len(safe_controls) == 0:
            print("❌ 没有安全控制，使用紧急制动")
            action = np.array([-0.5, 0.0, 0.0])
            control_input = np.array([-0.5 * env.a_T_max, 0.0, 0.0])
            score = 0
        else:
            # 选择最佳控制（模拟早期随机 + 后期优化）
            if step < 10:
                # 早期：随机选择前几个好的控制
                control = safe_controls[np.random.randint(min(len(safe_controls), 5))]
            else:
                # 后期：选择最佳控制
                best_control = None
                best_score = -float('inf')
                
                for control in safe_controls:
                    score = dwa.evaluate_control(control, env.state, env.goal, env.obstacles + env.dynamic_obstacles)
                    if score > best_score:
                        best_score = score
                        best_control = control
                
                control = best_control
                score = best_score
            
            # 转换为归一化动作
            action = dwa.get_normalized_action(control)
            
            # 转换为环境控制输入
            control_input = np.array([
                action[0] * env.a_T_max,
                action[1] * env.a_N_max,
                action[2] * (np.pi/2)
            ])
            
            score = dwa.evaluate_control(control, env.state, env.goal, env.obstacles + env.dynamic_obstacles)
        
        print(f"选择控制: a_T={control_input[0]:.2f}, a_N={control_input[1]:.2f}, φ_dot={np.degrees(control_input[2]):.1f}°/s")
        print(f"控制分数: {score:.3f}")
        
        # 执行动作
        next_state, reward, done, info = env.step(control_input)

        # 计算实际移动（使用环境的实际状态，不是归一化的观测）
        new_pos = env.state[:3]  # 修复：使用实际状态而不是归一化观测
        movement = new_pos - current_pos
        movement_dist = np.linalg.norm(movement)
        new_dist = np.linalg.norm(new_pos - env.goal)
        dist_improvement = current_dist - new_dist
        
        print(f"移动距离: {movement_dist:.3f}m")
        print(f"新距离: {new_dist:.2f}m")
        print(f"距离改善: {dist_improvement:.3f}m {'✅' if dist_improvement > 0 else '❌'}")
        print(f"奖励: {reward:.2f}")
        
        # 记录数据
        trajectory.append(new_pos.copy())
        distances.append(new_dist)
        rewards.append(reward)
        actions_taken.append(action.copy())
        control_scores.append(score)

        # 更新状态（使用观测向量）
        state = next_state
        
        if done:
            print(f"Episode结束: {info}")
            break
    
    # 分析结果
    trajectory = np.array(trajectory)
    final_distance = distances[-1]
    initial_distance = distances[0]
    total_improvement = initial_distance - final_distance
    
    print(f"\n" + "=" * 50)
    print(f"📊 训练结果分析:")
    print(f"  总步数: {len(trajectory)-1}")
    print(f"  初始距离: {initial_distance:.2f}m")
    print(f"  最终距离: {final_distance:.2f}m")
    print(f"  总改善: {total_improvement:.2f}m")
    print(f"  改善百分比: {(total_improvement/initial_distance)*100:.1f}%")
    print(f"  平均奖励: {np.mean(rewards):.2f}")
    print(f"  总奖励: {sum(rewards):.2f}")
    
    # 计算移动效率
    total_movement = 0
    for i in range(1, len(trajectory)):
        total_movement += np.linalg.norm(trajectory[i] - trajectory[i-1])
    
    efficiency = total_improvement / total_movement if total_movement > 0 else 0
    print(f"  总移动距离: {total_movement:.2f}m")
    print(f"  移动效率: {efficiency:.3f}")
    
    # 判断成功
    success = total_improvement > 100  # 至少改善100米
    print(f"  训练成功: {'✅' if success else '❌'}")
    
    return trajectory, distances, rewards, actions_taken, control_scores, success

def plot_trajectory_comparison(trajectory, distances, rewards):
    """绘制轨迹对比图"""
    print(f"\n📈 生成轨迹图...")
    
    # 创建环境用于获取障碍物信息
    env_config = get_environment_config('stage1_simple')
    env = LoiteringMunitionEnvironment(
        bounds=[2000, 2000, 2000],
        environment_config=env_config,
        reward_type='simplified'
    )
    env.reset()
    
    # 创建图形
    fig = plt.figure(figsize=(16, 12))
    
    # 3D轨迹图
    ax1 = fig.add_subplot(221, projection='3d')
    
    # 绘制轨迹
    ax1.plot(trajectory[:, 0], trajectory[:, 1], trajectory[:, 2], 
             'b-', linewidth=3, label='修复后轨迹', alpha=0.8)
    
    # 起点和终点
    ax1.scatter(trajectory[0, 0], trajectory[0, 1], trajectory[0, 2], 
                c='green', s=200, marker='o', label='起点', edgecolors='black', linewidth=2)
    ax1.scatter(env.goal[0], env.goal[1], env.goal[2], 
                c='red', s=300, marker='*', label='目标', edgecolors='black', linewidth=2)
    ax1.scatter(trajectory[-1, 0], trajectory[-1, 1], trajectory[-1, 2], 
                c='blue', s=200, marker='s', label='终点', edgecolors='black', linewidth=2)
    
    # 绘制障碍物
    for obs in env.obstacles:
        u = np.linspace(0, 2 * np.pi, 20)
        v = np.linspace(0, np.pi, 20)
        x = obs['center'][0] + obs['radius'] * np.outer(np.cos(u), np.sin(v))
        y = obs['center'][1] + obs['radius'] * np.outer(np.sin(u), np.sin(v))
        z = obs['center'][2] + obs['radius'] * np.outer(np.ones(np.size(u)), np.cos(v))
        ax1.plot_surface(x, y, z, alpha=0.3, color='gray')
    
    # 绘制目标连线
    ax1.plot([trajectory[-1, 0], env.goal[0]], 
             [trajectory[-1, 1], env.goal[1]], 
             [trajectory[-1, 2], env.goal[2]], 
             'r--', linewidth=2, alpha=0.7, label=f'剩余距离: {distances[-1]:.1f}m')
    
    ax1.set_xlabel('X (m)')
    ax1.set_ylabel('Y (m)')
    ax1.set_zlabel('Z (m)')
    ax1.legend()
    ax1.set_title('修复后的3D轨迹 (朝向目标)', fontsize=14, fontweight='bold')
    ax1.grid(True, alpha=0.3)
    
    # 距离变化图
    ax2 = fig.add_subplot(222)
    steps = range(len(distances))
    ax2.plot(steps, distances, 'b-', linewidth=2, marker='o', markersize=4)
    ax2.set_xlabel('步数')
    ax2.set_ylabel('到目标距离 (m)')
    ax2.set_title('距离变化 (应该下降)', fontsize=12, fontweight='bold')
    ax2.grid(True, alpha=0.3)
    
    # 添加改善标注
    improvement = distances[0] - distances[-1]
    ax2.annotate(f'总改善: {improvement:.1f}m\n({(improvement/distances[0]*100):.1f}%)', 
                xy=(len(distances)-1, distances[-1]), 
                xytext=(len(distances)*0.7, distances[0]*0.8),
                arrowprops=dict(arrowstyle='->', color='red', lw=2),
                fontsize=10, fontweight='bold',
                bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
    
    # 奖励变化图
    ax3 = fig.add_subplot(223)
    ax3.plot(range(len(rewards)), rewards, 'g-', linewidth=2, marker='s', markersize=4)
    ax3.set_xlabel('步数')
    ax3.set_ylabel('奖励')
    ax3.set_title('奖励变化', fontsize=12, fontweight='bold')
    ax3.grid(True, alpha=0.3)
    
    # 添加平均奖励线
    avg_reward = np.mean(rewards)
    ax3.axhline(y=avg_reward, color='red', linestyle='--', linewidth=2, 
                label=f'平均奖励: {avg_reward:.2f}')
    ax3.legend()
    
    # XY平面投影
    ax4 = fig.add_subplot(224)
    ax4.plot(trajectory[:, 0], trajectory[:, 1], 'b-', linewidth=3, alpha=0.8)
    ax4.scatter(trajectory[0, 0], trajectory[0, 1], c='green', s=200, marker='o', 
                label='起点', edgecolors='black', linewidth=2)
    ax4.scatter(env.goal[0], env.goal[1], c='red', s=300, marker='*', 
                label='目标', edgecolors='black', linewidth=2)
    ax4.scatter(trajectory[-1, 0], trajectory[-1, 1], c='blue', s=200, marker='s', 
                label='终点', edgecolors='black', linewidth=2)
    
    # 绘制障碍物投影
    for obs in env.obstacles:
        circle = plt.Circle((obs['center'][0], obs['center'][1]), obs['radius'], 
                          color='gray', alpha=0.3)
        ax4.add_patch(circle)
    
    ax4.set_xlabel('X (m)')
    ax4.set_ylabel('Y (m)')
    ax4.set_title('XY平面投影', fontsize=12, fontweight='bold')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    ax4.set_aspect('equal')
    
    # 添加总体标题
    improvement_pct = (distances[0] - distances[-1]) / distances[0] * 100
    success_text = "✅ 成功朝向目标!" if improvement_pct > 5 else "⚠️ 需要进一步优化"
    
    fig.suptitle(f'DWA修复效果验证 - {success_text}\n'
                f'距离改善: {distances[0] - distances[-1]:.1f}m ({improvement_pct:.1f}%)', 
                fontsize=16, fontweight='bold')
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.9)
    
    # 保存图片
    filename = 'dwa_fix_trajectory_test.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"✅ 轨迹图已保存: {filename}")
    
    plt.show()
    
    return improvement_pct > 5

if __name__ == "__main__":
    # 运行简单训练测试
    trajectory, distances, rewards, actions, scores, training_success = simple_training_test()
    
    # 绘制轨迹图
    plot_success = plot_trajectory_comparison(trajectory, distances, rewards)
    
    print(f"\n" + "=" * 50)
    print(f"🎯 最终验证结果:")
    print(f"  训练测试: {'✅ 成功' if training_success else '❌ 需要改进'}")
    print(f"  轨迹分析: {'✅ 朝向目标' if plot_success else '❌ 偏离目标'}")
    
    if training_success and plot_success:
        print(f"\n🎉 DWA修复完全成功！")
        print(f"✅ 巡飞弹现在能够朝向目标移动")
        print(f"✅ 距离持续减少")
        print(f"✅ 可以开始正式训练了")
    else:
        print(f"\n⚠️  修复效果有限，可能需要进一步调整参数")
