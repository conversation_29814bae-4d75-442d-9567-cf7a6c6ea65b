"""
快速运行简化奖励函数训练的便捷脚本
默认训练200次，每10个episode生成一次3D轨迹图
"""

import sys
import os
from train_simplified_reward import SimplifiedRewardTrainer

def main():
    """快速运行增强型简化奖励函数训练"""
    print("🎯 快速启动增强型简化奖励函数训练")
    print("=" * 60)
    print("📋 默认配置:")
    print("  • 随机场景训练: 200 episodes")
    print("  • 固定场景训练: 100 episodes")
    print("  • 总训练: 300 episodes")
    print("  • 随机种子: 42")
    print("  • 3D轨迹图间隔: 每10个episodes")
    print("  • 奖励函数: 简化版本 (论文风格)")
    print("  • 策略连续性: 全程不间断更新")
    print("=" * 60)

    # 询问用户是否要修改配置
    print("\n是否使用默认配置？")
    print("1. 是，使用默认配置 (推荐)")
    print("2. 否，自定义配置")

    try:
        choice = input("\n请选择 (1/2): ").strip()

        if choice == "2":
            # 自定义配置
            random_episodes = int(input("请输入随机场景训练episodes数量 (默认200): ") or "200")
            fixed_episodes = int(input("请输入固定场景训练episodes数量 (默认100): ") or "100")
            seed = int(input("请输入随机种子 (默认42): ") or "42")
            viz_interval = int(input("请输入3D轨迹图生成间隔 (默认10): ") or "10")
        else:
            # 默认配置
            random_episodes = 200
            fixed_episodes = 100
            seed = 42
            viz_interval = 10

    except (ValueError, KeyboardInterrupt):
        print("\n❌ 输入无效或用户取消，使用默认配置")
        random_episodes = 200
        fixed_episodes = 100
        seed = 42
        viz_interval = 10

    total_episodes = random_episodes + fixed_episodes
    print(f"\n🚀 开始训练...")
    print(f"📊 配置: 总{total_episodes} episodes (随机{random_episodes} + 固定{fixed_episodes})")
    print(f"🎲 种子: {seed}, 每{viz_interval}个episodes生成3D图")
    print("=" * 60)

    # 创建训练器并运行
    trainer = SimplifiedRewardTrainer(
        random_episodes=random_episodes,
        fixed_episodes=fixed_episodes,
        seed=seed,
        visualization_interval=viz_interval
    )
    
    try:
        results = trainer.run_training()
        
        # 显示结果摘要
        final_stats = results['final_stats']
        print(f"\n🎉 训练成功完成!")
        print("=" * 60)
        print(f"📊 关键指标:")
        print(f"  • 成功率: {final_stats['success_rate']:.1%}")
        print(f"  • 平均奖励: {final_stats['avg_episode_reward']:.1f}")
        print(f"  • 奖励稳定性: {final_stats['reward_cv']:.3f}")
        print(f"  • 训练时间: {final_stats['total_training_time']:.1f}秒")
        print(f"  • 生成3D图: {final_stats['visualization_count']}个")
        print(f"\n📁 结果保存在: {results['output_dir']}")
        
        # 提示后续操作
        print(f"\n💡 后续操作建议:")
        print(f"  • 查看3D轨迹图了解训练过程")
        print(f"  • 分析training_summary.png了解整体表现")
        print(f"  • 查看JSON报告获取详细数据")
        
        return results
        
    except KeyboardInterrupt:
        print(f"\n⚠️ 训练被用户中断")
        return None
    except Exception as e:
        print(f"\n❌ 训练过程中出现错误: {e}")
        return None

if __name__ == "__main__":
    main()
