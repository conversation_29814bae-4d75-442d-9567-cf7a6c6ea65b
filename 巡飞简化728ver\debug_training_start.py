"""
调试训练启动问题
找出为什么第一个episode卡住了
"""

import time
import numpy as np
from loitering_munition_environment import LoiteringMunitionEnvironment
from loitering_munition_dwa import LoiteringMunitionDWA
from td3_network import StabilizedTD3Controller
from environment_config import get_environment_config, get_td3_config, get_loitering_munition_config

def debug_single_episode():
    """调试单个episode的执行"""
    print('🔍 调试单个episode执行...')
    
    # 创建环境
    print('1. 创建环境...')
    start_time = time.time()
    
    env_config = get_environment_config('stage1_simple')
    env = LoiteringMunitionEnvironment(
        bounds=[2000, 2000, 2000],
        environment_config=env_config,
        reward_type='simplified'
    )
    
    print(f'   环境创建耗时: {time.time() - start_time:.2f}秒')
    
    # 创建DWA
    print('2. 创建DWA控制器...')
    start_time = time.time()
    
    dwa = LoiteringMunitionDWA(dt=0.1)
    
    print(f'   DWA创建耗时: {time.time() - start_time:.2f}秒')
    
    # 创建TD3
    print('3. 创建TD3控制器...')
    start_time = time.time()

    td3_config = get_td3_config()
    td3_config.update({
        'state_dim': 15,
        'action_dim': 3,
        'max_action': 1.0
    })
    controller = StabilizedTD3Controller(td3_config)
    
    print(f'   TD3创建耗时: {time.time() - start_time:.2f}秒')
    
    # 重置环境
    print('4. 重置环境...')
    start_time = time.time()
    
    obs = env.reset()
    
    print(f'   环境重置耗时: {time.time() - start_time:.2f}秒')
    print(f'   起点: {env.start}')
    print(f'   目标: {env.goal}')
    print(f'   障碍物数量: {len(env.obstacles)}')
    
    # 测试第一步
    print('5. 测试第一步动作选择...')
    
    # 测试DWA
    print('   5.1 测试DWA安全控制生成...')
    start_time = time.time()
    
    try:
        safe_controls = dwa.generate_safe_control_set(env.state, env.obstacles, env.goal, max_actions=5)
        print(f'       DWA生成耗时: {time.time() - start_time:.2f}秒')
        print(f'       安全控制数量: {len(safe_controls)}')
        
        if len(safe_controls) > 0:
            print('   5.2 测试DWA控制评价...')
            start_time = time.time()
            
            best_control = None
            best_score = -float('inf')
            
            for i, control in enumerate(safe_controls):
                score = dwa.evaluate_control(control, env.state, env.goal, env.obstacles)
                if score > best_score:
                    best_score = score
                    best_control = control
                
                if i == 0:  # 只测试第一个控制的评价时间
                    print(f'       单个控制评价耗时: {time.time() - start_time:.2f}秒')
            
            print(f'       最佳控制: {best_control}')
            print(f'       最佳分数: {best_score:.3f}')
            
            # 测试环境step
            print('   5.3 测试环境step执行...')
            start_time = time.time()
            
            next_obs, reward, done, info = env.step(best_control)
            
            print(f'       环境step耗时: {time.time() - start_time:.2f}秒')
            print(f'       奖励: {reward:.2f}')
            print(f'       完成: {done}')
            print(f'       新位置: {env.state[:3]}')
            
        else:
            print('       ⚠️ 没有生成安全控制！')
            
    except Exception as e:
        print(f'       ❌ DWA测试失败: {e}')
        import traceback
        traceback.print_exc()
    
    print('\n✅ 单步测试完成')

def debug_episode_loop():
    """调试完整episode循环"""
    print('\n🔄 调试完整episode循环...')
    
    # 创建环境和控制器
    env_config = get_environment_config('stage1_simple')
    env = LoiteringMunitionEnvironment(
        bounds=[2000, 2000, 2000],
        environment_config=env_config,
        reward_type='simplified'
    )
    
    dwa = LoiteringMunitionDWA(dt=0.1)
    
    # 重置环境
    obs = env.reset()
    
    episode_reward = 0
    step = 0
    max_steps = 50  # 限制步数以便调试
    
    print(f'开始episode循环 (最大{max_steps}步)...')
    
    episode_start_time = time.time()
    
    while step < max_steps:
        step_start_time = time.time()
        
        # 选择动作
        safe_controls = dwa.generate_safe_control_set(env.state, env.obstacles, env.goal, max_actions=3)
        
        if len(safe_controls) > 0:
            # 简化控制选择
            action = safe_controls[0]  # 直接使用第一个安全控制
        else:
            action = [0, 0, 0]  # 默认控制
        
        # 执行动作
        next_obs, reward, done, info = env.step(action)
        
        episode_reward += reward
        step += 1
        
        step_time = time.time() - step_start_time
        
        # 每5步输出一次进度
        if step % 5 == 0 or step <= 3:
            current_dist = np.linalg.norm(env.state[:3] - env.goal)
            print(f'   步骤 {step:3d}: 距离 {current_dist:.1f}m, 奖励 {reward:.2f}, 耗时 {step_time:.3f}s')
        
        # 检查终止条件
        if done:
            print(f'   Episode在第{step}步结束: {info.get("termination_reason", "未知原因")}')
            break
        
        # 检查是否卡住
        if step_time > 5.0:
            print(f'   ⚠️ 第{step}步耗时过长: {step_time:.2f}秒')
            break
    
    total_time = time.time() - episode_start_time
    print(f'\nEpisode完成: {step}步, 总耗时 {total_time:.2f}秒, 平均 {total_time/step:.3f}秒/步')
    print(f'总奖励: {episode_reward:.2f}')

def debug_dwa_performance():
    """专门调试DWA性能"""
    print('\n⚡ 调试DWA性能...')
    
    # 创建环境
    env_config = get_environment_config('stage1_simple')
    env = LoiteringMunitionEnvironment(
        bounds=[2000, 2000, 2000],
        environment_config=env_config,
        reward_type='simplified'
    )
    
    dwa = LoiteringMunitionDWA(dt=0.1)
    obs = env.reset()
    
    print(f'障碍物数量: {len(env.obstacles)}')
    
    # 测试不同的max_actions参数
    for max_actions in [3, 5, 10, 20]:
        print(f'\n测试 max_actions={max_actions}:')
        
        start_time = time.time()
        safe_controls = dwa.generate_safe_control_set(env.state, env.obstacles, env.goal, max_actions=max_actions)
        generation_time = time.time() - start_time
        
        print(f'  生成时间: {generation_time:.3f}秒')
        print(f'  安全控制数量: {len(safe_controls)}')
        
        if len(safe_controls) > 0:
            start_time = time.time()
            for control in safe_controls:
                score = dwa.evaluate_control(control, env.state, env.goal, env.obstacles)
            evaluation_time = time.time() - start_time
            
            print(f'  评价时间: {evaluation_time:.3f}秒')
            print(f'  平均单个评价: {evaluation_time/len(safe_controls):.4f}秒')
        
        if generation_time > 1.0:
            print(f'  ⚠️ 生成时间过长！')
            break

if __name__ == "__main__":
    print('🔧 训练启动调试工具')
    print('=' * 50)
    
    try:
        debug_single_episode()
        debug_dwa_performance()
        debug_episode_loop()
        
    except KeyboardInterrupt:
        print('\n⚠️ 调试被用户中断')
    except Exception as e:
        print(f'\n❌ 调试过程中出现错误: {e}')
        import traceback
        traceback.print_exc()
