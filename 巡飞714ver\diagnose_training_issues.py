"""
训练问题诊断脚本
分析训练失败的根本原因
"""

import numpy as np
import sys
import os
import json

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from loitering_munition_env import LoiteringMunitionEnvironment
from loitering_munition_dwa import LoiteringMunitionDWA
from scenario_config import get_scenario_config

def diagnose_environment_termination():
    """诊断环境终止条件"""
    print("🔍 诊断环境终止条件")
    print("=" * 40)
    
    scenario_config = get_scenario_config('stage1')
    env = LoiteringMunitionEnvironment(
        bounds=scenario_config['environment_bounds'],
        fixed_scenario_config=scenario_config
    )
    
    observation = env.reset(verbose=False)
    print(f"初始状态: {env.state}")
    print(f"起点到目标距离: {np.linalg.norm(env.start - env.goal):.1f}m")
    
    # 测试不同的控制输入
    test_controls = [
        [0.0, 0.0, 0.0],      # 无控制
        [1.0, 0.0, 0.0],      # 前进
        [0.0, 5.0, 0.0],      # 转向
        [-1.0, 0.0, 0.0],     # 减速
    ]
    
    for i, control in enumerate(test_controls):
        env_copy = LoiteringMunitionEnvironment(
            bounds=scenario_config['environment_bounds'],
            fixed_scenario_config=scenario_config
        )
        env_copy.reset(verbose=False)
        
        print(f"\n测试控制输入 {i+1}: {control}")
        
        for step in range(25):  # 测试25步
            obs, reward, done, info = env_copy.step(control)
            
            if step < 5 or step % 5 == 0 or done:
                pos = env_copy.state[:3]
                vel = env_copy.state[3]
                goal_dist = np.linalg.norm(pos - env_copy.goal)
                
                print(f"  Step {step+1}: 位置=[{pos[0]:.1f},{pos[1]:.1f},{pos[2]:.1f}], "
                      f"速度={vel:.2f}, 目标距离={goal_dist:.1f}, 奖励={reward:.2f}")
                
                if done:
                    print(f"    终止原因: {info}")
                    break

def diagnose_dwa_actions():
    """诊断DWA动作生成"""
    print("\n🔍 诊断DWA动作生成")
    print("=" * 40)
    
    dwa = LoiteringMunitionDWA()
    scenario_config = get_scenario_config('stage1')
    env = LoiteringMunitionEnvironment(
        bounds=scenario_config['environment_bounds'],
        fixed_scenario_config=scenario_config
    )
    
    env.reset(verbose=False)
    
    # 生成安全动作
    safe_actions = dwa.generate_safe_control_set(
        env.state, env.obstacles + env.dynamic_obstacles, env.goal, max_actions=20
    )
    
    print(f"生成的安全动作数量: {len(safe_actions)}")
    
    if len(safe_actions) > 0:
        print(f"\n前10个安全动作:")
        for i, action in enumerate(safe_actions[:10]):
            print(f"  {i+1}. [{action[0]:.2f}, {action[1]:.2f}, {np.degrees(action[2]):.1f}°]")
    else:
        print("❌ 没有生成任何安全动作！")
        
        # 检查动态窗口
        dw = dwa._calc_dynamic_window(env.state)
        print(f"动态窗口: {dw}")
        
        # 检查障碍物
        print(f"障碍物数量: {len(env.obstacles)}")
        for i, obs in enumerate(env.obstacles):
            dist = np.linalg.norm(env.state[:3] - obs['center'])
            print(f"  障碍物{i+1}: 中心={obs['center']}, 半径={obs['radius']}, 距离={dist:.1f}")

def diagnose_reward_function():
    """诊断奖励函数"""
    print("\n🔍 诊断奖励函数")
    print("=" * 40)
    
    scenario_config = get_scenario_config('stage1')
    env = LoiteringMunitionEnvironment(
        bounds=scenario_config['environment_bounds'],
        fixed_scenario_config=scenario_config
    )
    
    env.reset(verbose=False)
    initial_pos = env.state[:3].copy()
    initial_goal_dist = np.linalg.norm(initial_pos - env.goal)
    
    print(f"初始位置: {initial_pos}")
    print(f"目标位置: {env.goal}")
    print(f"初始距离: {initial_goal_dist:.1f}m")
    
    # 测试不同位置的奖励
    test_positions = [
        initial_pos,  # 初始位置
        initial_pos + np.array([100, 100, 0]),  # 稍微接近
        initial_pos + np.array([500, 500, 0]),  # 更接近
        env.goal - np.array([100, 100, 0]),     # 接近目标
        env.goal,  # 目标位置
    ]
    
    for i, pos in enumerate(test_positions):
        # 设置状态
        env.state[:3] = pos
        env._prev_goal_dist = np.linalg.norm(pos - env.goal)
        
        # 执行一步
        obs, reward, done, info = env.step([0.0, 0.0, 0.0])
        goal_dist = np.linalg.norm(pos - env.goal)
        
        print(f"位置{i+1}: 距离目标={goal_dist:.1f}m, 奖励={reward:.2f}, 完成={done}")

def analyze_training_results():
    """分析训练结果"""
    print("\n🔍 分析训练结果")
    print("=" * 40)
    
    # 查找最新的训练结果
    import glob
    result_dirs = glob.glob("loitering_munition_training_*")
    if not result_dirs:
        print("❌ 没有找到训练结果目录")
        return
    
    latest_dir = max(result_dirs)
    result_file = f"{latest_dir}/training_results.json"
    
    if not os.path.exists(result_file):
        print(f"❌ 没有找到训练结果文件: {result_file}")
        return
    
    with open(result_file, 'r') as f:
        results = json.load(f)
    
    print(f"分析训练结果: {latest_dir}")
    
    for stage_name in ['stage1', 'stage2', 'stage3']:
        if stage_name in results:
            stage_data = results[stage_name]
            rewards = stage_data['episode_rewards']
            steps = stage_data['episode_steps']
            
            print(f"\n{stage_name}:")
            print(f"  总episodes: {len(rewards)}")
            print(f"  奖励范围: [{min(rewards):.2f}, {max(rewards):.2f}]")
            print(f"  平均奖励: {np.mean(rewards):.2f}")
            print(f"  步数范围: [{min(steps)}, {max(steps)}]")
            print(f"  平均步数: {np.mean(steps):.1f}")
            
            # 分析奖励趋势
            if len(rewards) >= 100:
                early_rewards = np.mean(rewards[:100])
                late_rewards = np.mean(rewards[-100:])
                improvement = late_rewards - early_rewards
                print(f"  学习改进: {improvement:.2f} (前100 vs 后100)")
            
            # 分析步数分布
            step_counts = {}
            for s in steps:
                step_counts[s] = step_counts.get(s, 0) + 1
            
            most_common_steps = sorted(step_counts.items(), key=lambda x: x[1], reverse=True)[:3]
            print(f"  最常见步数: {most_common_steps}")

def main():
    """主诊断函数"""
    print("🚨 训练问题诊断")
    print("=" * 50)
    print("基于训练结果分析，发现以下问题:")
    print("1. 零成功率 (0.0%)")
    print("2. 过早终止 (19步)")
    print("3. 奖励停滞")
    print("4. 学习停滞")
    print("=" * 50)
    
    try:
        # 诊断环境终止条件
        diagnose_environment_termination()
        
        # 诊断DWA动作生成
        diagnose_dwa_actions()
        
        # 诊断奖励函数
        diagnose_reward_function()
        
        # 分析训练结果
        analyze_training_results()
        
        print("\n" + "=" * 50)
        print("🎯 诊断完成！")
        print("💡 建议的解决方案:")
        print("1. 检查奖励函数设计")
        print("2. 调整环境终止条件")
        print("3. 优化DWA动作生成")
        print("4. 增加探索策略")
        print("5. 调整网络参数")
        print("=" * 50)
        
    except Exception as e:
        print(f"\n❌ 诊断过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
