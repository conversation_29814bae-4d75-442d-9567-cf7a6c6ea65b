{"experiment_info": {"reward_type": "simplified", "total_episodes": 300, "timestamp": "20250718_012043", "training_time_seconds": 2370.325294971466, "visualization_interval": 10, "visualization_count": 31}, "performance_metrics": {"total_episodes": 300, "random_episodes": 200, "fixed_episodes": 100, "success_rate": 0.9333333333333333, "collision_rate": 0.0, "timeout_rate": 0.0, "avg_episode_reward": -426.8963960222688, "std_episode_reward": 164.25459782443897, "reward_cv": 0.3847645455781049, "avg_episode_steps": 319.52, "std_episode_steps": 32.23553318932386, "total_training_time": 2370.325294971466, "avg_episode_time": 7.901084316571554, "final_50_success_rate": 0.94, "learning_improvement": 0.054174887041356326, "convergence_episode": 45, "best_episode_reward": -309.68084397697885, "worst_episode_reward": -2014.007958624515, "visualization_count": 31, "total_global_steps": 95856, "max_complexity_score": 160, "phase1_success_rate": 0.955, "phase2_success_rate": 0.89, "phase1_avg_reward": -412.17994233194344, "phase2_avg_reward": -456.32930340291955, "phase1_improvement": 0.08501451589787763, "phase2_improvement": 0.1483997303375861, "most_complex_scenario": {"start": [10.0, 10.0, 10.0], "goal": [80.0, 80.0, 80.0], "obstacles": [{"center": [30.0, 30.0, 30.0], "radius": 6.130662766679679}, {"center": [50.0, 20.0, 40.0], "radius": 7.7513375609120505}, {"center": [40.0, 60.0, 50.0], "radius": 6.627740976629537}, {"center": [60.0, 40.0, 30.0], "radius": 7.7954207057011065}, {"center": [70.0, 70.0, 60.0], "radius": 6.998730890368947}], "complexity_score": 160, "bounds": [100, 100, 100]}, "phase_transitions": [{"phase": "random_to_fixed", "episode": 200, "global_step_count": 63368, "selected_scenario": {"episode": 31, "complexity_score": 160, "scenario_data": {"start": [10.0, 10.0, 10.0], "goal": [80.0, 80.0, 80.0], "obstacles": [{"center": [30.0, 30.0, 30.0], "radius": 6.130662766679679}, {"center": [50.0, 20.0, 40.0], "radius": 7.7513375609120505}, {"center": [40.0, 60.0, 50.0], "radius": 6.627740976629537}, {"center": [60.0, 40.0, 30.0], "radius": 7.7954207057011065}, {"center": [70.0, 70.0, 60.0], "radius": 6.998730890368947}], "complexity_score": 160, "bounds": [100, 100, 100]}, "episode_data": {"episode_reward": -733.9599204196618, "episode_steps": 460, "episode_result": "timeout"}}, "phase1_success_rate": 0.955}]}, "episode_rewards": [-512.8122047311988, -428.2713272401162, -399.39509746893356, -421.6575437060808, -337.2485539469136, -395.8470497690835, -893.6535115927408, -398.65165395077014, -384.0685259428688, -351.7564706658361, -403.0378253942447, -339.0802728227119, -610.3245312739584, -618.7487321825855, -402.074614059182, -405.27619764498803, -361.39054782702004, -403.0985370764311, -342.8323058886325, -434.83751972651567, -338.2470464722674, -362.53813907625124, -389.41822250606265, -332.01041326844063, -1987.8516324887087, -400.3696143084956, -338.89492111567307, -371.7411444555508, -350.6868841954989, -360.6078128471671, -377.6803719341704, -733.9599204196618, -354.2574146151914, -621.9432521660847, -354.3901618570644, -402.5288894823644, -453.12240888290376, -378.432935538144, -373.526021554758, -351.1955585406495, -397.8864621193414, -373.4959410923538, -411.2466682984915, -354.61188032521443, -339.491828128189, -376.5781412581536, -376.116088106468, -332.6921983984926, -356.5356755422344, -395.61004438352717, -402.7063472616218, -504.3886572769551, -426.9579633966948, -343.6135361414902, -338.175136813483, -351.42836903886183, -477.2414367429906, -380.0327515216791, -406.5551316978214, -443.70697162974807, -402.4614725409059, -368.11065509059034, -367.249316929892, -331.5596561895743, -402.45593483495907, -402.78145111050884, -433.3375260344508, -484.2993204830126, -361.7883108376092, -356.75446758658774, -332.09571146795156, -365.8142985526806, -363.1848101528961, -404.4748206533762, -424.4881347521499, -376.09200702306134, -376.5617503751175, -622.7447056397261, -454.14662634568833, -336.1118526206492, -380.8155006981266, -399.67423128730377, -336.145541432269, -416.76363838864825, -650.6537471220165, -342.2018851061679, -374.3091137881498, -419.95802656693684, -373.5650830176455, -359.6272774342604, -511.8481092296531, -340.105883213396, -556.6692757048517, -358.4807410070032, -453.1888522119292, -373.40850076721193, -309.68084397697885, -350.44787125939996, -645.0670279838537, -364.77157943286136, -338.88103304992507, -337.9267213003937, -376.69653898469767, -445.6498421794321, -442.31739981737144, -453.7989582050525, -428.97114747645276, -411.5814664050936, -393.1918669179777, -371.3972383752826, -407.5711555180011, -407.8594793883713, -427.3768408305107, -338.75669993740763, -423.91906001904147, -373.82778093631134, -382.46203201127383, -327.0419761343628, -522.1094673460416, -366.7675457739631, -406.657247184886, -434.318580286916, -341.152384826691, -461.1776038873587, -359.10105330268146, -388.4119779490843, -351.9271604736685, -401.1583265682601, -360.24292764041695, -335.9127298840247, -358.0968071503095, -402.1715618298315, -434.05170687808163, -503.0186500919625, -396.193208959478, -358.50837296943786, -375.9901611264758, -368.78023008664394, -361.3226189720214, -421.72612898440104, -455.2706340545765, -402.0402998736038, -352.8958507537276, -390.2095019872852, -479.18418853341245, -371.630214287212, -369.8992332103941, -367.36375485794366, -414.1130759517155, -364.7642463060692, -349.87621989676126, -383.16693455024836, -350.7156600405261, -476.134928600952, -371.0391784738993, -400.15081618569735, -339.82915355005895, -496.0618336696333, -454.3489463368412, -360.90883088380974, -399.1982871708141, -518.7619786018626, -379.5393624514287, -385.11397449974305, -371.94315777560496, -488.19629727367897, -378.0333956585493, -330.0455124782445, -455.9287054577969, -346.966178142707, -327.14783438497244, -421.13489381568206, -437.11015506451906, -386.1841238424785, -454.51525866201473, -324.8950213588812, -345.52493592217945, -362.44427166275307, -471.2458318845563, -461.810235508744, -466.9545289090771, -397.7734583522532, -369.7153893313346, -431.9885964928304, -399.9802198110876, -409.28779056240916, -352.9648802879786, -429.41763044589663, -363.7937402272507, -401.90894731100684, -382.19575772739427, -367.93591170667275, -397.43192429665515, -390.0163165723761, -388.90608050260016, -596.2306430200929, -398.71239996744526, -414.8027893210759, -373.3460659338288, -358.82621566846296, -457.9405938678707, -354.78150386531456, -2014.007958624515, -370.8738533340818, -380.3698843896605, -333.5479032473097, -463.5613522410754, -412.27919471448615, -460.3391933382653, -407.0302361539886, -334.7471168467749, -360.4173177744825, -452.2916810715707, -431.2406907874181, -364.88671573794903, -375.5231011419208, -500.6167822742408, -572.5464898551087, -367.11049986719684, -408.4974410468906, -454.27144792371314, -383.802365851059, -392.0746714311581, -419.25731372732446, -438.9023601368855, -348.2863383687199, -363.3349657880255, -1272.5697163065997, -412.4460382415498, -1245.71217841741, -404.53137505269507, -476.3984805513046, -381.62600587434196, -400.81741711050455, -489.819838914102, -408.457200759456, -591.678457119509, -599.9208664697062, -413.7162282814131, -439.7318994414095, -604.5352416873242, -454.5257730564176, -696.7113506980668, -388.72799673328547, -362.5776231267844, -406.7738762739722, -408.2729443294298, -474.19531097688275, -383.06657402324544, -489.9656306261825, -375.13412959867566, -432.0442383064966, -399.66005412584724, -421.5479428229514, -383.83739612730517, -454.87188902619846, -382.8479525458159, -311.2711211452611, -401.9169256316872, -411.7378966981119, -619.9833660109327, -406.21827703666895, -412.13548235655594, -352.1223670987519, -379.5160336527262, -504.8540066677648, -393.5242760738654, -423.46132714828525, -462.8608494855929, -341.63945615725765, -417.80184628248435, -351.3259664392748, -599.1026885339335, -356.33847886116433, -417.3587541761382, -402.0088651296377, -396.967311734718, -422.35789764758863, -424.6041707948485, -379.6254678388183, -695.7436235182072, -394.0093582917133, -366.21115046828464, -481.71535212320214, -458.9749327159411, -373.87556627738996, -511.4605889898545, -367.1933948934286, -451.46110923663286, -389.2571283071438, -397.4918943031777, -336.93385766420806, -462.5917818229774, -342.5287214598676, -341.17350989997203, -362.13139586594184, -374.8914173463967, -461.98093064137345, -410.16079790517364, -385.15039592709593], "episode_steps": [331, 330, 309, 325, 290, 307, 424, 312, 309, 292, 330, 299, 373, 395, 309, 322, 317, 323, 305, 316, 311, 310, 304, 295, 500, 318, 291, 310, 300, 299, 316, 460, 315, 425, 307, 311, 314, 310, 304, 309, 331, 307, 333, 294, 299, 303, 308, 311, 294, 314, 333, 330, 301, 299, 295, 318, 321, 306, 333, 317, 317, 326, 305, 285, 318, 313, 326, 320, 307, 301, 295, 299, 299, 320, 313, 309, 304, 395, 321, 295, 305, 323, 303, 305, 383, 295, 294, 320, 301, 296, 325, 300, 375, 304, 318, 299, 292, 294, 450, 323, 312, 306, 321, 329, 320, 323, 308, 311, 303, 322, 311, 331, 333, 316, 312, 306, 315, 289, 326, 295, 311, 316, 296, 317, 302, 321, 300, 318, 291, 301, 313, 318, 315, 330, 310, 295, 304, 307, 299, 311, 327, 322, 291, 319, 352, 299, 299, 304, 306, 294, 293, 309, 296, 330, 303, 307, 314, 327, 311, 313, 308, 334, 296, 304, 320, 324, 314, 302, 319, 319, 300, 312, 315, 300, 324, 300, 326, 304, 326, 316, 340, 315, 307, 319, 303, 309, 312, 320, 307, 329, 320, 303, 308, 330, 309, 396, 311, 338, 301, 301, 320, 302, 500, 310, 323, 307, 327, 322, 314, 312, 309, 296, 313, 306, 317, 333, 354, 366, 304, 306, 311, 313, 320, 310, 321, 313, 296, 500, 316, 500, 314, 351, 323, 325, 319, 319, 378, 388, 303, 325, 380, 308, 431, 334, 303, 302, 311, 339, 304, 327, 302, 309, 297, 314, 303, 300, 306, 297, 310, 315, 382, 308, 297, 297, 308, 329, 310, 310, 311, 312, 317, 298, 364, 301, 314, 314, 309, 335, 330, 309, 397, 310, 308, 324, 320, 319, 333, 301, 312, 313, 314, 307, 310, 305, 303, 294, 308, 353, 321, 303], "success_episodes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 25, 26, 27, 28, 29, 30, 32, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 78, 79, 80, 81, 82, 83, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 196, 197, 198, 199, 200, 201, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 218, 219, 220, 221, 222, 223, 224, 225, 226, 228, 230, 231, 232, 233, 234, 235, 238, 239, 241, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 273, 274, 275, 276, 277, 278, 279, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299], "collision_episodes": [], "timeout_episodes": [], "visualization_episodes": [0, 9, 19, 29, 39, 49, 59, 69, 79, 89, 99, 109, 119, 129, 139, 149, 159, 169, 179, 189, 199, 209, 219, 229, 239, 249, 259, 269, 279, 289, 299]}