"""
简化的TD3网络架构 - 巡飞弹专用
适配15维状态空间和3维控制输入
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

class Actor(nn.<PERSON><PERSON>le):
    """Actor网络 - 策略网络"""
    
    def __init__(self, state_dim=15, action_dim=3, max_action=1.0):
        super(Actor, self).__init__()
        self.max_action = max_action
        
        # 状态编码器
        self.state_encoder = nn.Sequential(
            nn.Linear(state_dim, 256),
            nn.LayerNorm(256),
            nn.ReLU(),
            nn.Linear(256, 256),
            nn.<PERSON>er<PERSON>orm(256),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # 策略输出层
        self.policy_head = nn.Sequential(
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, action_dim),
            nn.<PERSON><PERSON>()
        )
    
    def forward(self, state):
        """前向传播"""
        state_features = self.state_encoder(state)
        action = self.policy_head(state_features)
        return self.max_action * action

class Critic(nn.Module):
    """Critic网络 - Q值网络"""
    
    def __init__(self, state_dim=15, action_dim=3):
        super(Critic, self).__init__()
        
        # Q1网络
        self.q1_state_encoder = nn.Sequential(
            nn.Linear(state_dim, 256),
            nn.LayerNorm(256),
            nn.ReLU(),
            nn.Linear(256, 256),
            nn.LayerNorm(256),
            nn.ReLU()
        )
        
        self.q1_action_encoder = nn.Sequential(
            nn.Linear(action_dim, 128),
            nn.LayerNorm(128),
            nn.ReLU(),
            nn.Linear(128, 128),
            nn.LayerNorm(128),
            nn.ReLU()
        )
        
        self.q1_value_head = nn.Sequential(
            nn.Linear(384, 256),  # 256 + 128 = 384
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, 1)
        )
        
        # Q2网络
        self.q2_state_encoder = nn.Sequential(
            nn.Linear(state_dim, 256),
            nn.LayerNorm(256),
            nn.ReLU(),
            nn.Linear(256, 256),
            nn.LayerNorm(256),
            nn.ReLU()
        )
        
        self.q2_action_encoder = nn.Sequential(
            nn.Linear(action_dim, 128),
            nn.LayerNorm(128),
            nn.ReLU(),
            nn.Linear(128, 128),
            nn.LayerNorm(128),
            nn.ReLU()
        )
        
        self.q2_value_head = nn.Sequential(
            nn.Linear(384, 256),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, 1)
        )
    
    def forward(self, state, action):
        """前向传播 - 返回两个Q值"""
        # Q1
        q1_state_features = self.q1_state_encoder(state)
        q1_action_features = self.q1_action_encoder(action)
        q1_combined = torch.cat([q1_state_features, q1_action_features], dim=1)
        q1 = self.q1_value_head(q1_combined)
        
        # Q2
        q2_state_features = self.q2_state_encoder(state)
        q2_action_features = self.q2_action_encoder(action)
        q2_combined = torch.cat([q2_state_features, q2_action_features], dim=1)
        q2 = self.q2_value_head(q2_combined)
        
        return q1, q2
    
    def Q1(self, state, action):
        """只返回Q1值"""
        q1_state_features = self.q1_state_encoder(state)
        q1_action_features = self.q1_action_encoder(action)
        q1_combined = torch.cat([q1_state_features, q1_action_features], dim=1)
        return self.q1_value_head(q1_combined)

class ReplayBuffer:
    """经验回放缓冲区"""
    
    def __init__(self, capacity=100000):
        self.capacity = capacity
        self.buffer = []
        self.position = 0
    
    def add(self, state, action, reward, next_state, done):
        """添加经验"""
        if len(self.buffer) < self.capacity:
            self.buffer.append(None)
        
        self.buffer[self.position] = (state, action, reward, next_state, done)
        self.position = (self.position + 1) % self.capacity
    
    def sample(self, batch_size):
        """采样经验"""
        batch = np.random.choice(len(self.buffer), batch_size, replace=False)
        states, actions, rewards, next_states, dones = zip(*[self.buffer[i] for i in batch])
        
        return (
            torch.FloatTensor(states),
            torch.FloatTensor(actions),
            torch.FloatTensor(rewards).unsqueeze(1),
            torch.FloatTensor(next_states),
            torch.FloatTensor(dones).unsqueeze(1)
        )
    
    def size(self):
        """返回缓冲区大小"""
        return len(self.buffer)

class TD3Agent:
    """TD3智能体"""
    
    def __init__(self, state_dim=15, action_dim=3, max_action=1.0, lr=3e-4):
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.max_action = max_action
        
        # 网络
        self.actor = Actor(state_dim, action_dim, max_action).to(self.device)
        self.actor_target = Actor(state_dim, action_dim, max_action).to(self.device)
        self.actor_target.load_state_dict(self.actor.state_dict())
        
        self.critic = Critic(state_dim, action_dim).to(self.device)
        self.critic_target = Critic(state_dim, action_dim).to(self.device)
        self.critic_target.load_state_dict(self.critic.state_dict())
        
        # 优化器
        self.actor_optimizer = torch.optim.Adam(self.actor.parameters(), lr=lr)
        self.critic_optimizer = torch.optim.Adam(self.critic.parameters(), lr=lr)
        
        # 超参数
        self.discount = 0.99
        self.tau = 0.005
        self.policy_noise = 0.2
        self.noise_clip = 0.5
        self.policy_freq = 2
        
        self.total_it = 0
    
    def select_action(self, state, noise=0.1):
        """选择动作"""
        state = torch.FloatTensor(state.reshape(1, -1)).to(self.device)
        action = self.actor(state).cpu().data.numpy().flatten()
        
        if noise != 0:
            action = action + np.random.normal(0, noise, size=action.shape)
        
        return action.clip(-self.max_action, self.max_action)
    
    def train(self, replay_buffer, batch_size=256):
        """训练网络"""
        self.total_it += 1
        
        # 采样经验
        state, action, reward, next_state, done = replay_buffer.sample(batch_size)
        state = state.to(self.device)
        action = action.to(self.device)
        reward = reward.to(self.device)
        next_state = next_state.to(self.device)
        done = done.to(self.device)
        
        with torch.no_grad():
            # 目标动作添加噪声
            noise = (torch.randn_like(action) * self.policy_noise).clamp(-self.noise_clip, self.noise_clip)
            next_action = (self.actor_target(next_state) + noise).clamp(-self.max_action, self.max_action)
            
            # 计算目标Q值
            target_q1, target_q2 = self.critic_target(next_state, next_action)
            target_q = torch.min(target_q1, target_q2)
            target_q = reward + (1 - done) * self.discount * target_q
        
        # 当前Q值
        current_q1, current_q2 = self.critic(state, action)
        
        # Critic损失
        critic_loss = F.mse_loss(current_q1, target_q) + F.mse_loss(current_q2, target_q)
        
        # 更新Critic
        self.critic_optimizer.zero_grad()
        critic_loss.backward()
        self.critic_optimizer.step()
        
        # 延迟策略更新
        if self.total_it % self.policy_freq == 0:
            # Actor损失
            actor_loss = -self.critic.Q1(state, self.actor(state)).mean()
            
            # 更新Actor
            self.actor_optimizer.zero_grad()
            actor_loss.backward()
            self.actor_optimizer.step()
            
            # 软更新目标网络
            for param, target_param in zip(self.critic.parameters(), self.critic_target.parameters()):
                target_param.data.copy_(self.tau * param.data + (1 - self.tau) * target_param.data)
            
            for param, target_param in zip(self.actor.parameters(), self.actor_target.parameters()):
                target_param.data.copy_(self.tau * param.data + (1 - self.tau) * target_param.data)
    
    def save(self, filename):
        """保存模型"""
        torch.save({
            'actor': self.actor.state_dict(),
            'critic': self.critic.state_dict(),
            'actor_target': self.actor_target.state_dict(),
            'critic_target': self.critic_target.state_dict(),
            'actor_optimizer': self.actor_optimizer.state_dict(),
            'critic_optimizer': self.critic_optimizer.state_dict(),
        }, filename)
    
    def load(self, filename):
        """加载模型"""
        checkpoint = torch.load(filename, map_location=self.device)
        self.actor.load_state_dict(checkpoint['actor'])
        self.critic.load_state_dict(checkpoint['critic'])
        self.actor_target.load_state_dict(checkpoint['actor_target'])
        self.critic_target.load_state_dict(checkpoint['critic_target'])
        self.actor_optimizer.load_state_dict(checkpoint['actor_optimizer'])
        self.critic_optimizer.load_state_dict(checkpoint['critic_optimizer'])

# 控制输入映射函数
def map_action_to_control(action):
    """将网络输出的动作映射到巡飞弹控制输入"""
    # action: [-1, 1] 范围的3维向量
    # 映射到实际控制输入范围
    a_T = action[0] * 8.0      # 切向加速度: [-8, 8] m/s²
    a_N = action[1] * 39.24    # 法向加速度: [-39.24, 39.24] m/s²
    mu = action[2] * np.pi/2   # 倾斜角: [-π/2, π/2] rad

    return np.array([a_T, a_N, mu])
