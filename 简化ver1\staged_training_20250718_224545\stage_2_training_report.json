{"experiment_info": {"stage_number": 2, "environment": "stage2_complex", "total_episodes": 350, "random_episodes": 200, "fixed_episodes": 150, "timestamp": "20250718_224545", "training_time_seconds": 3505.009217977524, "visualization_interval": 20, "visualization_count": 17}, "performance_metrics": {"total_episodes": 350, "random_episodes": 200, "fixed_episodes": 150, "total_training_time": 3505.009217977524, "visualization_count": 17, "phase1_success_rate": 0.97, "phase2_success_rate": 0.9333333333333333, "overall_success_rate": 0.9542857142857143, "avg_reward": -376.4617482570035, "avg_steps": 309.02, "collision_rate": 0.0, "timeout_rate": 0.045714285714285714}, "episode_rewards": [-286.03042930218413, -363.96532897276086, -280.5514184560774, -301.8524942592649, -294.24290275633024, -330.63147219054264, -301.09846220426203, -340.4135921095083, -380.58854225237053, -286.3930657857704, -323.30396590182477, -300.30517469879055, -403.8619339857317, -342.2600833627322, -377.720917588849, -286.4118164894225, -357.7233910830474, -373.0528189304111, -316.08955578583794, -357.234019771142, -374.6797273253385, -474.15905131659144, -386.9815372186163, -312.7994097554716, -387.56295278675134, -298.6935454465054, -386.22051964879677, -297.4804175904203, -632.8103258147336, -362.15766260676907, -379.12274480284407, -376.20830019331635, -299.67434776988193, -361.9411885860198, -406.3632273054368, -341.60241546923885, -315.9966146550137, -397.2881379509001, -328.0292689405662, -388.5473529154409, -298.96060849998946, -297.1422483364405, -291.4326777767937, -362.03767086956543, -314.18572241729544, -335.83413138186023, -388.677049345098, -321.3300774970539, -355.75624949026866, -292.23327721193425, -314.74786217145373, -340.2820616814749, -382.0995068521825, -336.30314528526657, -376.6092424457341, -288.7479483046957, -357.86240420266097, -285.6730071516053, -363.24407584488864, -377.7697079754769, -296.1792058263327, -283.73846872784, -349.90580489446916, -383.8651269383081, -365.23060445768823, -414.3803925402433, -294.9002400397205, -324.56884256857825, -383.8578399467923, -315.7696321227279, -607.8943159970703, -305.19251545500225, -588.3160402563728, -374.6691408001924, -288.1372375791927, -298.58293368283574, -380.11711234647913, -313.10492425091525, -379.4096785911085, -350.74426046711136, -290.35816715137594, -358.37154108550726, -282.7795311804928, -300.80578346553335, -312.4639596519917, -572.8810703070085, -346.8305731991449, -284.3520362654295, -281.93156182772594, -393.3217381217579, -288.34036241994255, -320.1132968054545, -328.2497069976563, -308.20307060621894, -371.78427474453525, -361.6123135515272, -592.297687619538, -723.0545133122117, -288.57228245305555, -304.7672205012076, -501.97731107893105, -289.2646229580636, -348.38119650759353, -401.71383635884916, -311.6329552966234, -375.3851839882048, -356.77936153537803, -1873.3318888756355, -301.26788445281636, -294.5055355436438, -300.6537064504498, -350.813637400543, -300.12370484830166, -301.2707543752026, -339.94803958991395, -296.0935415014849, -342.4114697166504, -405.9299379823699, -387.9860706303446, -382.3108509693675, -409.9118659656904, -286.4830803293242, -286.1379993206867, -362.72845440770936, -417.9177249543509, -290.99839515552577, -329.1298082274588, -307.233035746789, -363.3694658242861, -430.2795394937499, -291.64436003898646, -356.98577532278324, -386.3718644877134, -342.78131308149386, -415.2493078269399, -305.82091289688805, -378.4496927698004, -381.8727838958857, -293.0300273358905, -306.57534462376213, -289.9083734154429, -354.54827414929, -391.10687750822285, -387.1201180992033, -433.68177071919786, -328.4350904069466, -355.40307498706915, -389.4444139063168, -382.957854104487, -317.39198290990646, -415.3046738598305, -301.0879476421005, -302.20680830413517, -291.9035324290753, -321.8049246060487, -283.0453721516617, -386.2958373846525, -391.9613152931867, -288.9864881037259, -298.6100475695095, -329.4928071274942, -392.37037769966906, -347.4501984211638, -305.27430813250794, -354.9948789534367, -294.05816118134476, -317.77561226720997, -299.5234582812372, -367.87371690739036, -608.1940166835568, -297.8110616852443, -357.0147576383232, -360.4137735331337, -281.0516261035141, -346.0342046004387, -300.16069123973654, -287.78183555283493, -361.119262697309, -281.25137147684336, -411.07638859290165, -300.6039190112809, -324.75298895761193, -285.5684363922828, -293.55149352654314, -280.8452781183547, -295.7122642230176, -308.80725112526187, -350.83743954444657, -299.53142998254447, -388.4024384783838, -399.06923692599236, -326.243017624299, -365.809458661465, -373.457795557979, -425.61694642197665, -385.18682574524314, -362.0914716372202, -304.76788814884026, -287.92095195550314, -401.2790090250143, -364.54079485239674, -392.7437554678464, -404.37185323222263, -355.8614056131848, -348.48499062779206, -359.75594162865553, -341.7272091525642, -432.0892232289042, -393.94147453101635, -364.78678800242375, -421.81058892270164, -331.57321751165836, -379.8287116471454, -372.33252073039125, -405.51204480958864, -359.6359479057613, -367.01881512829254, -325.0700530463417, -407.3849859529276, -349.5105530442883, -341.1819136305723, -365.1667785966407, -359.495101230157, -396.3450930189972, -371.1005307695126, -357.83891141903695, -365.45452905065775, -357.78284932253933, -1886.7044875713132, -390.41869964803016, -371.9030957400812, -383.18390882629365, -363.869765530225, -360.64297875722053, -369.9080734287343, -387.67354748664286, -345.7678276092938, -364.9124151317971, -377.97039555874835, -392.97052790700593, -373.07785347914637, -383.3529642583069, -358.2433620148984, -597.6382528184392, -396.4599399623909, -396.38264908659227, -366.9716925983317, -385.60466372883127, -382.47212447959845, -374.8022346408428, -375.4664829121318, -371.089532847809, -379.10435800654045, -371.40309202259556, -371.58869274916844, -333.55569236861265, -393.09418201799247, -358.2175195229982, -381.7322663419031, -361.19247063087175, -420.96102731434405, -398.8725775638256, -372.6842630310382, -386.2561800951473, -328.41336202637694, -367.3062976258173, -341.57541512096276, -404.64069351934216, -406.20242194877864, -405.40458584486197, -334.7845236750341, -355.9059710681666, -331.99488532682267, -405.92868377037894, -356.4189488594793, -321.33043972095913, -344.6866963675288, -382.2903865264984, -340.98025999361465, -588.8413024521097, -365.63415186807384, -374.3874005266039, -340.62470058013275, -414.52438961562507, -390.8317670150684, -384.3411210872583, -661.9320416485077, -371.62791487711803, -430.6977577996686, -364.99434214091235, -397.5623445919437, -363.81305660380934, -358.63355691701446, -369.7734348234483, -350.1298582749066, -370.30957716788765, -591.7966925656758, -385.8654724746518, -680.0005664482329, -369.6141668570096, -334.54645519517766, -397.47164329849505, -340.004819898962, -417.8588390973073, -354.4335081777327, -378.4249180307055, -568.4288189328896, -353.2235609204594, -371.65019899278946, -348.4522918533987, -330.3239497223379, -397.38051292877896, -365.23297547201827, -377.1551585227956, -384.1972655510369, -383.35261321150193, -390.08183189249826, -321.28227429482257, -387.9981775063399, -578.6253020654838, -337.2451903168066, -541.1384585420444, -445.7709253396263, -391.31952275810966, -538.6092623274685, -361.1806115626069, -383.15029114226877, -370.14669136091214, -345.9227295210024, -406.28210807391224, -431.2697910015164, -414.38012821545135, -359.4671050813638, -376.1598420245619, -363.2464261022192, -365.82200319589913, -431.902343708985, -325.0687504686959, -385.0757164828551, -397.24616487241383, -721.79254400262, -351.0523176682, -514.5549781797579, -537.2919719061455, -341.6820126375957, -400.1498335900416, -347.42862685140335, -409.83471094576237, -607.3336646738617, -368.2975977011496], "episode_steps": [281, 303, 279, 292, 291, 299, 287, 314, 308, 279, 307, 288, 303, 297, 322, 281, 302, 302, 302, 306, 299, 335, 308, 298, 312, 286, 330, 287, 386, 305, 315, 322, 289, 304, 305, 302, 303, 307, 309, 311, 289, 281, 286, 299, 305, 304, 308, 287, 315, 291, 293, 307, 303, 299, 299, 286, 294, 282, 317, 315, 290, 281, 320, 313, 304, 329, 285, 306, 299, 296, 372, 290, 377, 317, 281, 288, 306, 296, 305, 325, 287, 295, 283, 299, 298, 364, 300, 282, 280, 306, 282, 304, 303, 298, 303, 302, 374, 390, 284, 283, 343, 281, 302, 306, 292, 314, 311, 500, 293, 283, 291, 297, 293, 289, 315, 287, 306, 313, 312, 305, 321, 287, 281, 308, 332, 282, 308, 294, 306, 334, 284, 300, 312, 331, 308, 302, 307, 303, 286, 302, 283, 306, 316, 316, 309, 306, 318, 303, 299, 299, 332, 293, 291, 282, 303, 280, 316, 303, 289, 287, 304, 314, 302, 290, 300, 283, 316, 292, 317, 416, 281, 309, 298, 279, 292, 291, 287, 306, 277, 315, 287, 290, 284, 295, 279, 290, 297, 297, 290, 305, 328, 302, 300, 315, 347, 308, 308, 291, 282, 305, 299, 320, 312, 304, 305, 299, 292, 320, 308, 318, 320, 299, 305, 299, 316, 311, 302, 303, 314, 316, 297, 296, 311, 314, 306, 308, 299, 307, 500, 323, 298, 308, 303, 315, 312, 303, 309, 293, 315, 315, 310, 312, 304, 375, 313, 325, 304, 321, 305, 308, 310, 304, 310, 303, 299, 295, 314, 307, 304, 314, 332, 312, 303, 307, 316, 307, 301, 321, 314, 333, 300, 298, 300, 311, 297, 306, 305, 314, 298, 365, 304, 310, 290, 321, 319, 329, 376, 307, 345, 302, 303, 302, 316, 297, 323, 300, 400, 320, 397, 301, 308, 307, 300, 315, 302, 312, 366, 316, 307, 301, 301, 303, 305, 304, 302, 306, 319, 303, 312, 369, 292, 353, 338, 319, 363, 304, 311, 298, 303, 307, 320, 320, 302, 305, 299, 309, 332, 304, 318, 301, 427, 305, 351, 347, 295, 306, 307, 320, 369, 307], "success_episodes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 71, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 297, 299, 300, 301, 302, 303, 304, 305, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 322, 323, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 341, 342, 343, 344, 345, 346, 347, 349], "collision_episodes": [], "timeout_episodes": [28, 70, 72, 96, 107, 169, 228, 243, 279, 296, 298, 306, 321, 324, 340, 348], "visualization_episodes": [19, 39, 59, 79, 99, 119, 139, 159, 179, 199, 219, 239, 259, 279, 299, 319, 339], "phase_transitions": [{"phase": "random_to_fixed", "episode": 200, "selected_scenario": {"scenario": {"obstacles": [{"center": [70.0, 50.0, 30.0], "radius": 3}, {"center": [55.0, 35.0, 35.0], "radius": 4}, {"center": [20.0, 50.0, 50.0], "radius": 3}, {"center": [35.0, 35.0, 35.0], "radius": 6}, {"center": [60.0, 50.0, 20.0], "radius": 3}, {"center": [15.0, 15.0, 50.0], "radius": 4}, {"center": [15.0, 85.0, 50.0], "radius": 4}, {"center": [85.0, 85.0, 50.0], "radius": 4}, {"center": [45.0, 45.0, 25.0], "radius": 4}], "start": [10.0, 10.0, 10.0], "goal": [80.0, 80.0, 80.0]}, "complexity_score": 94.13261182504834, "episode_reward": -723.0545133122117, "episode_success": true, "episode_num": 97}, "phase1_success_rate": 0.97}], "most_complex_scenario": {"scenario": {"obstacles": [{"center": [70.0, 50.0, 30.0], "radius": 3}, {"center": [55.0, 35.0, 35.0], "radius": 4}, {"center": [20.0, 50.0, 50.0], "radius": 3}, {"center": [35.0, 35.0, 35.0], "radius": 6}, {"center": [60.0, 50.0, 20.0], "radius": 3}, {"center": [15.0, 15.0, 50.0], "radius": 4}, {"center": [15.0, 85.0, 50.0], "radius": 4}, {"center": [85.0, 85.0, 50.0], "radius": 4}, {"center": [45.0, 45.0, 25.0], "radius": 4}], "start": [10.0, 10.0, 10.0], "goal": [80.0, 80.0, 80.0]}, "complexity_score": 94.13261182504834, "episode_reward": -723.0545133122117, "episode_success": true, "episode_num": 97}}