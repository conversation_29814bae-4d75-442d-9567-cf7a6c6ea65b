# 巡飞弹六自由度分阶段训练系统

## 📋 系统概述

本系统实现了论文《基于安全强化学习的巡飞弹约束动态规划方法：DWA-TD3融合架构》中描述的完整技术方案，集成了：

- **巡飞弹六自由度运动学环境**：基于真实巡飞弹物理模型
- **三项奖励函数**：距离奖励 + 效率奖励 + 安全约束奖励
- **分阶段训练策略**：从简单到复杂的渐进式学习
- **TD3深度强化学习**：双重Q网络架构
- **DWA安全约束**：动态窗口算法确保飞行安全

## 🚁 巡飞弹运动学模型

### 状态空间（6维）
```
状态向量: [x, y, z, V, γ, ψ]
- (x, y, z): 位置坐标 [m]
- V: 速度大小 [m/s]
- γ: 航迹倾斜角 [rad]
- ψ: 航迹偏航角 [rad]
```

### 控制输入（3维）
```
控制向量: [a_T, a_N, μ]
- a_T: 切向加速度（推力方向）[m/s²]
- a_N: 法向加速度（升力方向）[m/s²]
- μ: 倾斜角（机动方向）[rad]
```

### 运动学方程
```
ẋ = V cos γ cos ψ
ẏ = V cos γ sin ψ
ż = V sin γ
V̇ = a_T - g sin γ
γ̇ = (a_N cos μ - g cos γ)/V
ψ̇ = (a_N sin μ)/(V cos γ)
```

## 🎯 三项奖励函数

### 1. 距离奖励（目标导向驱动）
```
R_distance = -|p - g|/50.0
```
提供连续的目标导向信号，基于最优控制理论的代价函数设计。

### 2. 效率奖励（时间最优化）
```
R_efficiency = -0.1
```
每步固定时间惩罚，鼓励寻找最短路径，体现时间最优原理。

### 3. 安全约束奖励（危险规避）
```
R_danger = -(3.0 - d_min) × 2.0  当 d_min < 3.0
         = 0                    其他情况
```
仅在接近危险区域时激活，提供必要的安全边界信号。

### 终端奖励
```
R_success = 100.0   （到达目标）
R_collision = -100.0 （碰撞或越界）
```

## 🏗️ 分阶段训练策略

### 阶段1：基础作战场景（500 episodes）
- **静态威胁**：5个大型固定障碍物
- **环境类型**：建筑、山峰、通信塔、工业设施、桥梁
- **学习目标**：掌握基本避障和目标导向策略

### 阶段2：复杂静态场景（1000 episodes）
- **静态威胁**：15个固定障碍物（继承阶段1 + 新增10个）
- **环境类型**：城市建筑群、地形障碍、军事设施
- **学习目标**：提升复杂环境下的路径规划能力

### 阶段3：动态威胁场景（500 episodes）
- **静态威胁**：15个（继承阶段2）
- **动态威胁**：3个固定模式动态障碍物
  - 敌方巡逻飞机（线性运动）
  - 防空导弹系统（圆周扫描）
  - 移动雷达车（振荡巡逻）
- **学习目标**：适应动态威胁的预测和避让

## 🔧 运动约束参数

| 约束类型 | 参数值 | 说明 |
|----------|--------|------|
| 最小速度 | 15 m/s | 失速速度限制 |
| 最大速度 | 60 m/s | 最大飞行速度 |
| 最大切向加速度 | 8.0 m/s² | 推力限制 |
| 最大法向加速度 | 39.24 m/s² | 升力限制（4g） |
| 最大航迹倾斜角 | ±60° | 俯仰角限制 |
| 最小安全距离 | 2.0 m | 障碍物安全裕度 |
| 作战空间 | 2km×2km×200m | 三维飞行区域 |

## 📁 文件结构

```
loitering_munition_complete_system/
├── loitering_munition_env.py      # 巡飞弹六自由度环境
├── loitering_munition_dwa.py      # DWA安全控制器
├── scenario_config.py             # 分阶段场景配置
├── td3_network.py                 # TD3网络架构
├── train_loitering_munition.py    # 主训练脚本
├── test_system.py                 # 系统测试脚本
├── DWA_RL_Framework_Paper.tex     # 完整论文（需复制）
└── README.md                      # 本文档
```

## 🚀 快速开始

### 1. 系统测试
```bash
# 快速功能测试
python test_system.py --mode quick

# 预期输出：
# ⚡ 快速系统测试
# 1. 测试环境创建... ✅
# 2. 测试网络创建... ✅  
# 3. 测试环境步进... ✅
# 🎉 快速测试通过！
```

### 2. 开始训练
```bash
# 执行完整的三阶段训练
python train_loitering_munition.py

# 训练过程：
# 🚁 巡飞弹分阶段训练系统
# 🚀 开始 stage1: 基础作战场景
# 🚀 开始 stage2: 复杂静态场景  
# 🚀 开始 stage3: 动态威胁场景
# 🎉 训练完成!
```

### 3. 测试训练结果
```bash
# 测试单个模型
python test_system.py --mode single --model loitering_munition_training_XXXXXX/stage3_model.pth --stage stage3

# 测试所有阶段模型
python test_system.py --mode all --model-dir loitering_munition_training_XXXXXX

# 生成详细可视化分析
python visualize_results.py --results-dir loitering_munition_training_XXXXXX --mode comprehensive
```

## 📊 训练输出详解

### 🖥️ 实时训练输出
```
🚁 巡飞弹分阶段训练系统
============================================================
📅 开始时间: 2024-01-15 14:30:25
📁 输出目录: loitering_munition_training_20240115_143025
🎲 随机种子: 42

🚀 开始 stage1: 基础作战场景
--------------------------------------------------
🎯 阶段1：巡飞弹基础作战场景 - 5个固定静态威胁
📊 静态威胁: 5个, 动态威胁: 0个
🚀 起点: [100, 100, 50]
🎯 目标: [1800, 1800, 120]

  Episode    0: 平均奖励= -45.23, 成功率=0.0%, 步数=245
  Episode  100: 平均奖励= -38.67, 成功率=15.8%, 步数=198
  Episode  200: 平均奖励= -31.45, 成功率=32.5%, 步数=167
  Episode  300: 平均奖励= -25.89, 成功率=48.3%, 步数=145
  Episode  400: 平均奖励= -22.34, 成功率=62.8%, 步数=128
  Episode  499: 平均奖励= -19.67, 成功率=78.4%, 步数=115

✅ stage1 完成:
   成功率: 78.4%
   平均奖励: -19.67
   平均步数: 115.2
   训练时间: 1847.3秒
💾 stage1 模型已保存

🎉 训练完成!
📊 总体成功率: 72.1%
⏱️ 总训练时间: 8234.7秒
📈 可视化图表已保存到: loitering_munition_training_XXXXXX/visualizations/
```

### 💾 保存的文件结构
```
loitering_munition_training_20240115_143025/
├── 🤖 训练模型
│   ├── stage1_model.pth              # 阶段1训练模型
│   ├── stage2_model.pth              # 阶段2训练模型
│   └── stage3_model.pth              # 阶段3训练模型
├── 📊 训练数据
│   ├── training_results.json         # 详细训练数据（JSON格式）
│   └── training_report.txt           # 训练总结报告（文本格式）
├── 📈 可视化图表
│   ├── training_curves.png           # 训练曲线图
│   ├── success_rates.png             # 成功率分析图
│   ├── stage_comparison.png          # 阶段对比雷达图
│   └── reward_distribution.png       # 奖励分布分析图
└── 🎬 综合分析（可选生成）
    ├── scenario_layout_stage1.png    # 阶段1场景布局图
    ├── scenario_layout_stage2.png    # 阶段2场景布局图
    ├── scenario_layout_stage3.png    # 阶段3场景布局图
    ├── 3d_trajectory_stage1.png      # 阶段1 3D飞行轨迹
    ├── 3d_trajectory_stage2.png      # 阶段2 3D飞行轨迹
    └── 3d_trajectory_stage3.png      # 阶段3 3D飞行轨迹
```

### 📈 可视化内容详解

#### 1. **训练曲线图** (`training_curves.png`)
- **Episode奖励变化**：显示每个阶段的奖励学习曲线
- **Episode步数变化**：显示路径规划效率的改善
- **累积成功率变化**：显示学习进度和收敛性
- **各阶段训练时间对比**：显示计算效率

#### 2. **成功率分析图** (`success_rates.png`)
- **结果分布柱状图**：成功/碰撞/超时的百分比分布
- **成功率趋势图**：各阶段成功率的变化趋势

#### 3. **阶段对比雷达图** (`stage_comparison.png`)
- **多维性能对比**：成功率、奖励、步数、训练效率的综合评估
- **雷达图可视化**：直观显示各阶段的优势和劣势

#### 4. **奖励分布分析图** (`reward_distribution.png`)
- **奖励直方图**：显示奖励值的分布特征
- **奖励箱线图**：显示奖励的统计特性
- **学习改进程度**：前后期奖励的改善幅度
- **训练稳定性**：奖励方差的变化趋势

#### 5. **3D飞行轨迹图** (`3d_trajectory_stageX.png`)
- **完整3D轨迹**：巡飞弹的实际飞行路径
- **静态威胁可视化**：建筑、地形等障碍物的3D展示
- **动态威胁轨迹**：敌方单位的运动轨迹
- **起点终点标记**：任务的起始和目标位置

#### 6. **场景布局图** (`scenario_layout_stageX.png`)
- **俯视图**：作战区域的平面布局
- **侧视图**：高度信息和地形特征
- **威胁分布**：静态和动态威胁的位置关系

### 📋 预期训练结果

| 阶段 | 训练时间 | 成功率 | 平均奖励 | 平均步数 | 特点 |
|------|----------|--------|----------|----------|------|
| Stage1 | 30-60分钟 | >80% | >-50 | <800 | 基础避障能力 |
| Stage2 | 60-120分钟 | >70% | >-80 | <1200 | 复杂路径规划 |
| Stage3 | 30-60分钟 | >60% | >-120 | <1500 | 动态威胁应对 |

## 🔬 技术特点

### 1. 学术严谨性
- 采用航空工程标准的六自由度模型
- 基于实际巡飞弹性能参数设计
- 完整的运动学约束体系

### 2. 工程实用性
- 真实作战环境建模
- 实际威胁类型模拟
- 可扩展的场景配置

### 3. 训练有效性
- 渐进式复杂度设计
- 固定场景确保可重复性
- 知识积累式学习策略

## 📝 论文对应关系

| 论文章节 | 代码实现 | 说明 |
|----------|----------|------|
| 2.1 巡飞弹运动学模型 | `loitering_munition_env.py` | 六自由度运动学方程 |
| 2.2 运动约束 | `loitering_munition_dwa.py` | DWA约束验证 |
| 3.1 奖励函数设计 | `_calculate_simplified_reward()` | 三项奖励函数 |
| 3.2 分阶段训练 | `scenario_config.py` | 三阶段场景配置 |
| 4.1 网络架构 | `td3_network.py` | TD3双重Q网络 |

## ⚠️ 注意事项

1. **计算资源**：训练需要较长时间（预计2-4小时）
2. **内存需求**：建议至少8GB RAM
3. **依赖库**：需要PyTorch、NumPy、Matplotlib
4. **随机种子**：已固定为42，确保结果可重复

## 🎉 系统优势

1. **完整性**：涵盖论文中的所有技术要点
2. **可运行性**：开箱即用，无需额外配置
3. **可扩展性**：模块化设计，易于修改和扩展
4. **可重现性**：固定随机种子，结果可重复验证

这个集成系统完全实现了论文中描述的巡飞弹智能控制技术，为学术研究和工程应用提供了完整的技术方案。
