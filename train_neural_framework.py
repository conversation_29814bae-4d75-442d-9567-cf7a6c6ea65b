"""
训练神经网络增强的分层安全强化学习框架
Train Neural-Enhanced Hierarchical Safe Reinforcement Learning Framework
"""

import sys
import os
import numpy as np
import torch
import time
from collections import deque
import matplotlib.pyplot as plt

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from neural_enhanced_rl_framework import NeuralEnhancedSafeRL
from test_neural_enhanced_framework import MockEnvironment

class SimpleReplayBuffer:
    """简单的经验回放缓冲区"""
    
    def __init__(self, capacity=10000):
        self.capacity = capacity
        self.buffer = deque(maxlen=capacity)
    
    def push(self, state, action, reward, next_state, done, obstacles):
        """添加经验"""
        self.buffer.append({
            'state': state,
            'action': action,
            'reward': reward,
            'next_state': next_state,
            'done': done,
            'obstacles': obstacles
        })
    
    def sample(self, batch_size):
        """采样经验"""
        indices = np.random.choice(len(self.buffer), batch_size, replace=False)
        batch = [self.buffer[i] for i in indices]
        
        return {
            'states': np.array([exp['state'] for exp in batch]),
            'actions': np.array([exp['action'] for exp in batch]),
            'rewards': np.array([exp['reward'] for exp in batch]),
            'next_states': np.array([exp['next_state'] for exp in batch]),
            'dones': np.array([exp['done'] for exp in batch]),
            'obstacles': [exp['obstacles'] for exp in batch]
        }
    
    def __len__(self):
        return len(self.buffer)

def generate_training_data(num_episodes=50):
    """生成训练数据 - 使用简单的启发式策略"""
    print("🎯 生成训练数据...")
    
    env = MockEnvironment()
    replay_buffer = SimpleReplayBuffer()
    
    for episode in range(num_episodes):
        state = env.reset()
        episode_reward = 0
        steps = 0
        max_steps = 100
        
        while steps < max_steps:
            # 使用简单的启发式策略生成"好"的训练数据
            goal_direction = env.goal - state[:3]
            goal_distance = np.linalg.norm(goal_direction)
            
            if goal_distance > 1e-6:
                # 朝向目标的动作
                action = goal_direction / goal_distance * 2.0  # 适度的速度
                
                # 添加一些随机噪声
                action += np.random.normal(0, 0.5, 3)
                action = np.clip(action, -3.0, 3.0)
            else:
                action = np.array([0.0, 0.0, 0.0])
            
            # 执行动作
            next_state, reward, done, info = env.step(action)
            
            # 存储经验
            replay_buffer.push(
                state.copy(), action.copy(), reward, 
                next_state.copy(), done, env.obstacles.copy()
            )
            
            episode_reward += reward
            steps += 1
            state = next_state
            
            if done:
                break
        
        if episode % 10 == 0:
            success = info.get('success', False)
            print(f"  Episode {episode}: 奖励 {episode_reward:.1f}, 步骤 {steps}, 成功 {'✅' if success else '❌'}")
    
    print(f"✅ 生成了 {len(replay_buffer)} 条训练数据")
    return replay_buffer

def train_constraint_predictor(rl_framework, replay_buffer, num_epochs=100):
    """训练约束预测网络"""
    print("\n🧠 训练约束预测网络...")
    
    constraint_losses = []
    physics_losses = []
    
    for epoch in range(num_epochs):
        if len(replay_buffer) < 32:
            continue
            
        # 训练约束预测器
        loss_info = rl_framework.train_constraint_predictor(replay_buffer, batch_size=32)
        
        if loss_info:
            constraint_losses.append(loss_info['constraint_loss'])
            physics_losses.append(loss_info['physics_loss'])
            
            if epoch % 20 == 0:
                print(f"  Epoch {epoch}: 约束损失 {loss_info['constraint_loss']:.4f}, "
                      f"物理损失 {loss_info['physics_loss']:.4f}")
    
    return constraint_losses, physics_losses

def test_trained_framework(rl_framework, num_test_episodes=5):
    """测试训练后的框架"""
    print("\n🧪 测试训练后的框架...")
    
    env = MockEnvironment()
    success_count = 0
    total_rewards = []
    total_steps = []
    
    for episode in range(num_test_episodes):
        state = env.reset()
        episode_reward = 0
        steps = 0
        max_steps = 200
        
        print(f"\n测试Episode {episode + 1}:")
        
        while steps < max_steps:
            # 使用训练后的框架选择动作
            action, info = rl_framework.get_action(state, env.obstacles, env.goal, training=False)
            
            # 执行动作
            next_state, reward, done, env_info = env.step(action)
            
            episode_reward += reward
            steps += 1
            state = next_state
            
            # 每20步打印一次进度
            if steps % 20 == 0:
                goal_dist = np.linalg.norm(state[:3] - env.goal)
                print(f"  步骤 {steps}: 距离 {goal_dist:.1f}m, 奖励 {reward:.2f}, 方法 {info['selection_method']}")
            
            if done:
                if env_info.get('success', False):
                    print(f"  ✅ 成功到达目标! 步骤: {steps}, 总奖励: {episode_reward:.1f}")
                    success_count += 1
                elif env_info.get('collision', False):
                    print(f"  ❌ 发生碰撞! 步骤: {steps}")
                elif env_info.get('out_of_bounds', False):
                    print(f"  ❌ 越界! 步骤: {steps}")
                break
        
        if not done:
            final_dist = np.linalg.norm(state[:3] - env.goal)
            print(f"  ⏰ 超时结束，最终距离: {final_dist:.1f}m")
        
        total_rewards.append(episode_reward)
        total_steps.append(steps)
    
    # 统计结果
    success_rate = success_count / num_test_episodes
    avg_reward = np.mean(total_rewards)
    avg_steps = np.mean(total_steps)
    
    print(f"\n📊 测试结果统计:")
    print(f"  成功率: {success_rate:.1%} ({success_count}/{num_test_episodes})")
    print(f"  平均奖励: {avg_reward:.1f}")
    print(f"  平均步骤: {avg_steps:.1f}")
    
    return success_rate, avg_reward, avg_steps

def compare_with_random_policy(num_test_episodes=5):
    """与随机策略对比"""
    print("\n🎲 随机策略对比测试...")
    
    env = MockEnvironment()
    success_count = 0
    total_rewards = []
    
    for episode in range(num_test_episodes):
        state = env.reset()
        episode_reward = 0
        steps = 0
        max_steps = 200
        
        while steps < max_steps:
            # 随机动作
            action = np.random.uniform(-2.0, 2.0, 3)
            
            # 执行动作
            next_state, reward, done, env_info = env.step(action)
            
            episode_reward += reward
            steps += 1
            state = next_state
            
            if done:
                if env_info.get('success', False):
                    success_count += 1
                break
        
        total_rewards.append(episode_reward)
    
    success_rate = success_count / num_test_episodes
    avg_reward = np.mean(total_rewards)
    
    print(f"  随机策略成功率: {success_rate:.1%}")
    print(f"  随机策略平均奖励: {avg_reward:.1f}")
    
    return success_rate, avg_reward

def main():
    """主训练函数"""
    print("🚀 神经网络增强的分层安全强化学习框架训练")
    print("=" * 60)
    
    # 设置设备
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")
    
    # 创建框架
    rl_framework = NeuralEnhancedSafeRL(state_dim=6, action_dim=3, device=device)
    
    try:
        # 1. 生成训练数据
        replay_buffer = generate_training_data(num_episodes=100)
        
        # 2. 训练约束预测网络
        constraint_losses, physics_losses = train_constraint_predictor(
            rl_framework, replay_buffer, num_epochs=200
        )
        
        # 3. 测试训练前的性能（作为基线）
        print("\n📊 训练前性能测试:")
        rl_framework.current_episode = 0  # 重置为约束引导模式
        baseline_success, baseline_reward, baseline_steps = test_trained_framework(rl_framework, 3)
        
        # 4. 测试训练后的性能
        print("\n📊 训练后性能测试:")
        rl_framework.current_episode = 150  # 切换到策略引导模式
        trained_success, trained_reward, trained_steps = test_trained_framework(rl_framework, 5)
        
        # 5. 随机策略对比
        random_success, random_reward = compare_with_random_policy(5)
        
        # 6. 结果总结
        print("\n" + "=" * 60)
        print("📈 训练结果总结:")
        print(f"  随机策略:   成功率 {random_success:.1%}, 平均奖励 {random_reward:.1f}")
        print(f"  训练前框架: 成功率 {baseline_success:.1%}, 平均奖励 {baseline_reward:.1f}")
        print(f"  训练后框架: 成功率 {trained_success:.1%}, 平均奖励 {trained_reward:.1f}")
        
        improvement = trained_success - baseline_success
        print(f"  性能提升:   成功率 +{improvement:.1%}")
        
        if trained_success > random_success:
            print("✅ 训练成功！框架性能超过随机策略")
        else:
            print("⚠️ 训练效果有限，需要进一步优化")
        
        # 7. 保存模型
        model_path = "neural_enhanced_rl_model.pth"
        rl_framework.save_models(model_path)
        print(f"💾 模型已保存到: {model_path}")
        
        # 8. 绘制训练曲线
        if constraint_losses:
            plt.figure(figsize=(12, 4))
            
            plt.subplot(1, 2, 1)
            plt.plot(constraint_losses)
            plt.title('约束预测损失')
            plt.xlabel('Epoch')
            plt.ylabel('Loss')
            
            plt.subplot(1, 2, 2)
            plt.plot(physics_losses)
            plt.title('物理信息损失')
            plt.xlabel('Epoch')
            plt.ylabel('Loss')
            
            plt.tight_layout()
            plt.savefig('training_curves.png')
            print("📊 训练曲线已保存到: training_curves.png")
        
    except Exception as e:
        print(f"❌ 训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
