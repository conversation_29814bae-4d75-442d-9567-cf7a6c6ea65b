"""
完整回合测试 - 包含障碍物可视化
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from loitering_munition_environment import LoiteringMunitionEnvironment
from loitering_munition_dwa import LoiteringMunitionDWA
from environment_config import get_environment_config

def run_complete_episode():
    """运行完整回合并生成可视化"""
    print('🚀 开始完整训练回合测试...')

    # 创建环境
    env_config = get_environment_config('stage1_simple')
    env = LoiteringMunitionEnvironment(
        bounds=[2000, 2000, 2000],
        environment_config=env_config,
        reward_type='simplified'
    )

    # 创建DWA控制器
    dwa = LoiteringMunitionDWA(dt=0.1)

    # 重置环境
    obs = env.reset()
    print(f'起点: {env.start}')
    print(f'目标: {env.goal}')
    print(f'初始距离: {np.linalg.norm(env.start - env.goal):.2f}m')

    # 检查障碍物
    print(f'静态障碍物数量: {len(env.obstacles)}')
    print(f'动态障碍物数量: {len(env.dynamic_obstacles)}')

    # 打印障碍物信息
    if len(env.obstacles) > 0:
        print('静态障碍物:')
        for i, obs_info in enumerate(env.obstacles):
            center = obs_info['center']
            radius = obs_info['radius']
            print(f'  障碍物 {i+1}: 中心 [{center[0]:.1f}, {center[1]:.1f}, {center[2]:.1f}], 半径 {radius:.1f}m')

    # 记录轨迹数据
    trajectory = [env.state[:3].copy()]
    distances = []
    rewards = []
    actions = []
    dwa_scores = []

    episode_reward = 0
    step = 0
    max_steps = 500  # 增加步数以便更好地观察避障

    print(f'\n开始训练回合 (最大步数: {max_steps})...')

    while step < max_steps:
        # 获取当前距离
        current_dist = np.linalg.norm(env.state[:3] - env.goal)
        distances.append(current_dist)
        
        # 使用DWA选择动作
        safe_controls = dwa.generate_safe_control_set(env.state, env.obstacles, env.goal, max_actions=5)
        if len(safe_controls) > 0:
            best_control = None
            best_score = -float('inf')
            
            for control in safe_controls:
                score = dwa.evaluate_control(control, env.state, env.goal, env.obstacles)
                if score > best_score:
                    best_score = score
                    best_control = control
            
            action = best_control if best_control is not None else safe_controls[0]
            dwa_scores.append(best_score)
        else:
            action = [0, 0, 0]
            dwa_scores.append(0.0)
        
        actions.append(action.copy())
        
        # 执行动作
        next_obs, reward, done, info = env.step(action)
        
        # 记录数据
        trajectory.append(env.state[:3].copy())
        rewards.append(reward)
        episode_reward += reward
        
        # 打印进度
        if step % 10 == 0 or step < 5:
            print(f'步骤 {step+1:3d}: 位置 [{env.state[0]:.1f}, {env.state[1]:.1f}, {env.state[2]:.1f}], 距离 {current_dist:.2f}m, 奖励 {reward:.2f}, DWA分数 {dwa_scores[-1]:.3f}')
        
        obs = next_obs
        step += 1
        
        if done:
            print(f'回合结束于步骤 {step}')
            break

    # 最终距离
    final_dist = np.linalg.norm(env.state[:3] - env.goal)
    distances.append(final_dist)

    print(f'\n📊 回合统计:')
    print(f'总步数: {step}')
    print(f'初始距离: {distances[0]:.2f}m')
    print(f'最终距离: {final_dist:.2f}m')
    print(f'距离改善: {distances[0] - final_dist:.2f}m')
    print(f'改善百分比: {(distances[0] - final_dist)/distances[0]*100:.1f}%')
    print(f'总奖励: {episode_reward:.2f}')
    print(f'平均奖励: {episode_reward/step:.2f}')
    print(f'平均DWA分数: {np.mean(dwa_scores):.3f}')

    # 检查避障效果
    print(f'\n🛡️ 避障分析:')
    min_distances = []
    for i, pos in enumerate(trajectory):
        min_dist_to_obs = float('inf')
        closest_obs = -1
        for j, obstacle in enumerate(env.obstacles):
            dist_to_center = np.linalg.norm(pos - obstacle['center'])
            dist_to_surface = dist_to_center - obstacle['radius']
            if dist_to_surface < min_dist_to_obs:
                min_dist_to_obs = dist_to_surface
                closest_obs = j
        min_distances.append(min_dist_to_obs)

        # 检查是否有碰撞
        if min_dist_to_obs < 5.0:  # 5米安全距离
            print(f'⚠️  步骤 {i}: 接近障碍物 {closest_obs+1}, 距离 {min_dist_to_obs:.1f}m')

    min_distances = np.array(min_distances)
    print(f'最小障碍物距离: {np.min(min_distances):.2f}m')
    print(f'平均障碍物距离: {np.mean(min_distances):.2f}m')
    print(f'安全飞行: {"是" if np.min(min_distances) >= 5.0 else "否"}')

    # 转换为numpy数组
    trajectory = np.array(trajectory)
    distances = np.array(distances)
    rewards = np.array(rewards)
    actions = np.array(actions)

    print(f'\n🎨 生成轨迹图...')
    
    # 生成可视化
    generate_trajectory_visualization(env, trajectory, distances, rewards, actions, dwa_scores)
    
    return {
        'trajectory': trajectory,
        'distances': distances,
        'rewards': rewards,
        'actions': actions,
        'dwa_scores': dwa_scores,
        'episode_reward': episode_reward,
        'step_count': step
    }

def generate_trajectory_visualization(env, trajectory, distances, rewards, actions, dwa_scores):
    """生成完整的轨迹可视化"""
    
    # 创建大图
    fig = plt.figure(figsize=(20, 15))
    
    # 3D轨迹图 (主图)
    ax1 = fig.add_subplot(231, projection='3d')
    
    # 绘制轨迹
    ax1.plot(trajectory[:, 0], trajectory[:, 1], trajectory[:, 2], 'b-', linewidth=3, alpha=0.8, label='Trajectory')
    ax1.scatter(trajectory[::5, 0], trajectory[::5, 1], trajectory[::5, 2], c='blue', s=30, alpha=0.6)
    
    # 绘制起点和目标
    ax1.scatter(*env.start, color='green', s=200, marker='o', label='Start', edgecolors='black', linewidth=2)
    ax1.scatter(*env.goal, color='red', s=200, marker='*', label='Target', edgecolors='black', linewidth=2)
    
    # 绘制障碍物 - 使用更清晰的可视化
    for i, obstacle in enumerate(env.obstacles):
        center = obstacle['center']
        radius = obstacle['radius']

        # 创建球体 - 增加分辨率
        u = np.linspace(0, 2 * np.pi, 30)
        v = np.linspace(0, np.pi, 30)
        x_sphere = center[0] + radius * np.outer(np.cos(u), np.sin(v))
        y_sphere = center[1] + radius * np.outer(np.sin(u), np.sin(v))
        z_sphere = center[2] + radius * np.outer(np.ones(np.size(u)), np.cos(v))

        # 使用不同颜色和更高透明度
        colors = ['orange', 'yellow', 'purple', 'brown', 'pink']
        color = colors[i % len(colors)]
        ax1.plot_surface(x_sphere, y_sphere, z_sphere, alpha=0.4, color=color, edgecolor='black', linewidth=0.1)

        # 添加障碍物标签和信息
        ax1.text(center[0], center[1], center[2] + radius + 20,
                f'Obs{i+1}\\nr={radius:.0f}m', fontsize=10, ha='center',
                bbox=dict(boxstyle="round,pad=0.3", facecolor=color, alpha=0.7))
    
    ax1.set_xlabel('X (m)')
    ax1.set_ylabel('Y (m)')
    ax1.set_zlabel('Z (m)')
    ax1.set_title('3D Trajectory with Obstacles')
    ax1.legend()
    
    # XY平面投影
    ax2 = fig.add_subplot(232)
    ax2.plot(trajectory[:, 0], trajectory[:, 1], 'b-', linewidth=2, alpha=0.8, label='Trajectory')
    ax2.scatter(trajectory[::5, 0], trajectory[::5, 1], c='blue', s=20, alpha=0.6)
    ax2.scatter(*env.start[:2], color='green', s=100, marker='o', label='Start')
    ax2.scatter(*env.goal[:2], color='red', s=100, marker='*', label='Target')
    
    # 绘制障碍物投影 - 使用填充圆形和清晰标签
    for i, obstacle in enumerate(env.obstacles):
        center = obstacle['center']
        radius = obstacle['radius']
        colors = ['orange', 'yellow', 'purple', 'brown', 'pink']
        color = colors[i % len(colors)]

        # 填充圆形表示障碍物
        circle_fill = plt.Circle(center[:2], radius, fill=True, color=color, alpha=0.3)
        ax2.add_patch(circle_fill)

        # 边界圆形
        circle_edge = plt.Circle(center[:2], radius, fill=False, color=color, linewidth=3, alpha=0.8)
        ax2.add_patch(circle_edge)

        # 清晰的标签
        ax2.text(center[0], center[1], f'Obs{i+1}\\nr={radius:.0f}m',
                fontsize=10, ha='center', va='center', weight='bold',
                bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8, edgecolor=color))
    
    ax2.set_xlabel('X (m)')
    ax2.set_ylabel('Y (m)')
    ax2.set_title('XY Plane View')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_aspect('equal')
    
    # 距离变化
    ax3 = fig.add_subplot(233)
    ax3.plot(range(len(distances)), distances, 'r-', linewidth=2, marker='o', markersize=3)
    ax3.set_xlabel('Step')
    ax3.set_ylabel('Distance to Target (m)')
    ax3.set_title('Distance vs Steps')
    ax3.grid(True, alpha=0.3)
    
    # 奖励变化
    ax4 = fig.add_subplot(234)
    ax4.plot(range(len(rewards)), rewards, 'g-', linewidth=2, marker='o', markersize=3)
    ax4.set_xlabel('Step')
    ax4.set_ylabel('Reward')
    ax4.set_title('Reward vs Steps')
    ax4.grid(True, alpha=0.3)
    
    # DWA分数
    ax5 = fig.add_subplot(235)
    ax5.plot(range(len(dwa_scores)), dwa_scores, 'm-', linewidth=2, marker='o', markersize=3)
    ax5.set_xlabel('Step')
    ax5.set_ylabel('DWA Score')
    ax5.set_title('DWA Control Score vs Steps')
    ax5.grid(True, alpha=0.3)
    
    # 控制输入
    ax6 = fig.add_subplot(236)
    ax6.plot(range(len(actions)), actions[:, 0], 'r-', linewidth=2, label='a_T (m/s²)', alpha=0.8)
    ax6.plot(range(len(actions)), actions[:, 1], 'g-', linewidth=2, label='a_N (m/s²)', alpha=0.8)
    ax6.plot(range(len(actions)), np.degrees(actions[:, 2]), 'b-', linewidth=2, label='φ_dot (deg/s)', alpha=0.8)
    ax6.set_xlabel('Step')
    ax6.set_ylabel('Control Input')
    ax6.set_title('Control Inputs vs Steps')
    ax6.legend()
    ax6.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('complete_episode_trajectory_detailed.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f'✅ 详细轨迹图已保存: complete_episode_trajectory_detailed.png')

if __name__ == "__main__":
    results = run_complete_episode()
