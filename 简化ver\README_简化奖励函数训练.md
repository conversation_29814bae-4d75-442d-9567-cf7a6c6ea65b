# 简化奖励函数专用训练系统

## 🎯 系统概述

本系统从对比训练代码中抽离出简化奖励函数，创建了专门的训练脚本，用于验证简化奖励函数的训练效果。系统支持训练200次（可自定义），并在训练过程中定期生成三维轨迹图。

## 📁 文件结构

```
简化ver/
├── train_simplified_reward.py          # 主训练脚本
├── run_simplified_training.py          # 快速运行脚本
├── 简化奖励函数训练说明.md              # 详细使用说明
├── README_简化奖励函数训练.md           # 本文件
└── simplified_reward_training_*/        # 训练输出目录
    ├── simplified_reward_model.pth      # 训练好的模型
    ├── episode_*_3d_trajectory.png      # 3D轨迹图
    ├── training_summary.png             # 训练总结图
    ├── simplified_reward_training_report.json  # 详细报告
    └── simplified_reward_training_rewards.csv  # 奖励数据
```

## 🚀 快速开始

### 方法1: 使用快速运行脚本（推荐）
```bash
cd 简化ver
python run_simplified_training.py
```
按提示选择配置即可开始训练。

### 方法2: 直接使用主脚本
```bash
cd 简化ver
python train_simplified_reward.py --episodes 200 --viz-interval 10
```

## 🎨 主要特性

### 1. 专用简化奖励函数
- **明确终止信号**: 成功+100，碰撞/越界-100
- **核心距离奖励**: -distance/50.0，强烈鼓励接近目标
- **效率激励**: 每步-0.1，鼓励快速完成
- **最小安全约束**: 仅在真正危险时惩罚

### 2. 定期3D可视化
- 默认每10个episodes生成一次3D轨迹图
- 不同结果用不同颜色和线型表示：
  - 蓝色实线：成功到达
  - 红色虚线：发生碰撞
  - 橙色点线：超时

### 3. 完整数据记录
- 模型文件：训练好的TD3模型
- 训练数据：完整的训练过程数据
- 可视化：3D轨迹图和训练总结图
- 报告：JSON和CSV格式的详细报告

## 📊 输出说明

### 训练过程输出
```
Episode  15: Reward=  -45.2, Success=0.133, Steps=156, Result=timeout
Episode  20: Reward=  -38.7, Success=0.150, Steps=142, Result=success
```

### 最终统计
```
📊 最终统计:
  • 总Episodes: 200
  • 成功率: 0.650 (130/200)
  • 平均Episode奖励: -35.42
  • 奖励变异系数: 0.245
  • 训练时间: 180.5秒
  • 生成3D图数量: 20
```

## 🔧 参数配置

### 命令行参数
- `--episodes`: 训练episodes数量 (默认: 200)
- `--seed`: 随机种子 (默认: 42)
- `--viz-interval`: 3D轨迹图生成间隔 (默认: 10)

### 示例用法
```bash
# 训练300个episodes，每5个episodes生成一次3D图
python train_simplified_reward.py --episodes 300 --viz-interval 5

# 使用不同随机种子
python train_simplified_reward.py --seed 123

# 快速测试（少量episodes）
python train_simplified_reward.py --episodes 20 --viz-interval 5
```

## 📈 与原始train_dwa_rl.py的对比

| 特性 | 原始脚本 | 简化奖励脚本 |
|------|----------|-------------|
| 奖励函数 | 可选择复杂/简化 | 专用简化版本 |
| 3D可视化 | 实时显示 | 定期保存图片 |
| 输出组织 | 单一目录 | 时间戳目录 |
| 数据记录 | 基础统计 | 详细分析数据 |
| 使用便利性 | 需要参数 | 一键运行 |

## 🎯 使用场景

1. **验证简化奖励函数效果**: 专门测试简化奖励函数的训练性能
2. **生成可视化材料**: 为论文或报告准备3D轨迹图
3. **对比分析**: 与复杂奖励函数的结果进行对比
4. **参数调优**: 测试不同参数设置的影响

## 📝 注意事项

1. **字体显示**: 系统会自动检测和配置中文字体，如有问题请参考`字体显示修复指南.md`
2. **内存使用**: 大量3D图生成会消耗内存，建议合理设置间隔
3. **训练时间**: 200个episodes约需10-20分钟（取决于硬件）
4. **磁盘空间**: 完整训练数据可能较大，注意磁盘空间

## 🔧 字体显示问题解决

如果图表中文字显示为方框，请运行：
```bash
python fix_font_display.py
```
系统会自动检测和修复字体问题。详细说明请查看`字体显示修复指南.md`。

## 🔄 后续分析建议

训练完成后，建议进行以下分析：

1. **查看3D轨迹图**: 了解训练过程中轨迹质量的变化
2. **分析训练总结图**: 观察奖励趋势、成功率进展等
3. **对比不同参数**: 使用不同种子或参数重复训练
4. **性能评估**: 使用生成的模型进行性能测试

## 🆚 简化 vs 复杂奖励函数

### 简化奖励函数优势
- **学习目标明确**: 主要优化距离目标，避免多目标冲突
- **收敛更快**: 减少了复杂的权重平衡问题
- **稳定性更好**: 减少了奖励波动

### 适用场景
- **快速原型验证**: 验证基本的路径规划能力
- **基准测试**: 作为对比其他方法的基准
- **教学演示**: 清晰展示强化学习的基本原理

---

**开始您的简化奖励函数训练之旅！** 🚀
