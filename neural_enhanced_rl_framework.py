"""
神经网络增强的分层安全强化学习框架
Neural-Enhanced Hierarchical Safe Reinforcement Learning Framework

核心创新：用神经网络替代传统DWA，构建真正的深度强化学习分层架构
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional
from neural_constraint_predictor import NeuralConstraintPredictor
import copy

class NeuralEnhancedSafeRL:
    """
    神经网络增强的安全强化学习框架
    
    创新架构：
    1. 约束预测层（Neural Constraint Predictor）- 替代传统DWA
    2. 策略学习层（TD3 Actor-Critic）- 全局优化
    3. 分层协同机制 - 两个神经网络的智能协作
    """
    
    def __init__(self, state_dim=6, action_dim=3, device='cpu'):
        self.device = device
        self.state_dim = state_dim
        self.action_dim = action_dim
        
        # 约束预测网络 - 替代DWA的核心创新
        self.constraint_predictor = NeuralConstraintPredictor(device)
        
        # TD3策略网络
        self.actor = Actor(state_dim, action_dim).to(device)
        self.critic1 = Critic(state_dim, action_dim).to(device)
        self.critic2 = Critic(state_dim, action_dim).to(device)
        
        # 目标网络
        self.target_actor = copy.deepcopy(self.actor)
        self.target_critic1 = copy.deepcopy(self.critic1)
        self.target_critic2 = copy.deepcopy(self.critic2)
        
        # 优化器
        self.actor_optimizer = torch.optim.Adam(self.actor.parameters(), lr=3e-4)
        self.critic_optimizer = torch.optim.Adam(
            list(self.critic1.parameters()) + list(self.critic2.parameters()), lr=3e-4
        )
        
        # 训练参数
        self.gamma = 0.99
        self.tau = 0.005
        self.policy_noise = 0.2
        self.noise_clip = 0.5
        self.policy_freq = 2
        
        # 分层协同参数
        self.constraint_guidance_episodes = 100  # 约束引导阶段的episode数
        self.current_episode = 0
        
        # 协同学习统计
        self.collaboration_stats = {
            'constraint_accuracy': [],
            'policy_performance': [],
            'safety_violations': []
        }
    
    def get_action(self, state: np.ndarray, obstacles: List[Dict], 
                   goal: np.ndarray, training: bool = True) -> Tuple[np.ndarray, Dict]:
        """
        分层动作选择 - 核心创新的协同机制
        
        创新点：
        1. 约束预测网络生成安全动作候选集
        2. TD3网络从候选集中选择最优动作
        3. 两个网络协同学习和优化
        """
        
        # 第一层：约束预测网络生成安全候选动作
        safe_candidates = self.constraint_predictor.generate_safe_action_set(
            state, obstacles, goal, num_candidates=100
        )
        
        if not safe_candidates:
            # 紧急情况：使用保守动作
            emergency_action = np.array([0.0, 0.0, 0.0])
            return emergency_action, {'num_safe_candidates': 0, 'selection_method': 'emergency'}
        
        # 第二层：TD3网络选择最优动作
        if self.current_episode < self.constraint_guidance_episodes:
            # 早期阶段：主要依赖约束预测网络（类似DWA引导）
            selected_action = self._constraint_guided_selection(safe_candidates, state, goal)
            selection_method = 'constraint_guided'
        else:
            # 后期阶段：TD3主导选择
            selected_action = self._policy_guided_selection(safe_candidates, state, training)
            selection_method = 'policy_guided'
        
        info = {
            'num_safe_candidates': len(safe_candidates),
            'selection_method': selection_method,
            'top_candidate_score': safe_candidates[0]['total_score'] if safe_candidates else 0
        }
        
        return selected_action, info
    
    def _constraint_guided_selection(self, candidates: List[Dict], 
                                   state: np.ndarray, goal: np.ndarray) -> np.ndarray:
        """
        约束引导的动作选择 - 早期训练阶段
        """
        if not candidates:
            return np.array([0.0, 0.0, 0.0])
        
        # 在前几个最优候选中随机选择（保持探索性）
        top_k = min(5, len(candidates))
        selected_idx = np.random.randint(0, top_k)
        
        return candidates[selected_idx]['action']
    
    def _policy_guided_selection(self, candidates: List[Dict], 
                               state: np.ndarray, training: bool) -> np.ndarray:
        """
        策略引导的动作选择 - 后期训练阶段
        
        创新点：TD3网络学习从安全候选集中选择最优动作
        """
        if not candidates:
            return np.array([0.0, 0.0, 0.0])
        
        # TD3生成期望动作
        state_tensor = torch.FloatTensor(state[:self.state_dim]).unsqueeze(0).to(self.device)
        
        with torch.no_grad():
            td3_action = self.actor(state_tensor).cpu().numpy()[0]
            
            if training:
                # 添加探索噪声
                noise = np.random.normal(0, self.policy_noise, size=self.action_dim)
                noise = np.clip(noise, -self.noise_clip, self.noise_clip)
                td3_action = td3_action + noise
        
        # 从候选集中找到最接近TD3期望的安全动作
        candidate_actions = np.array([c['action'] for c in candidates])
        distances = np.linalg.norm(candidate_actions - td3_action, axis=1)
        
        # 结合距离和安全评分选择
        scores = np.array([c['total_score'] for c in candidates])
        normalized_distances = 1.0 / (1.0 + distances)  # 距离越近分数越高
        
        # 综合评分：70%安全性 + 30%与TD3期望的接近度
        combined_scores = 0.7 * scores + 0.3 * normalized_distances
        
        best_idx = np.argmax(combined_scores)
        return candidates[best_idx]['action']
    
    def train_constraint_predictor(self, replay_buffer, batch_size: int = 64):
        """
        训练约束预测网络 - 从经验中学习安全模式
        
        创新点：约束预测网络与强化学习环境协同进化
        """
        if len(replay_buffer) < batch_size:
            return {}
        
        # 采样训练数据
        batch = replay_buffer.sample(batch_size)
        
        # 构造训练数据
        states = torch.FloatTensor(batch['states']).to(self.device)
        actions = torch.FloatTensor(batch['actions']).to(self.device)
        rewards = batch['rewards']
        dones = batch['dones']
        
        # 根据奖励和终止状态生成安全标签
        safety_labels = torch.FloatTensor([
            1.0 if r > -50 and not done else 0.0  # 简单的安全标签生成
            for r, done in zip(rewards, dones)
        ]).to(self.device)
        
        # 训练约束预测网络
        train_data = {
            'states': states,
            'actions': actions,
            'obstacles': batch.get('obstacles', [[] for _ in range(batch_size)]),
            'safety_labels': safety_labels
        }
        
        constraint_loss = self.constraint_predictor.train_step(train_data)
        
        return constraint_loss
    
    def train_policy(self, replay_buffer, batch_size: int = 256):
        """
        训练TD3策略网络 - 标准TD3训练过程
        """
        if len(replay_buffer) < batch_size:
            return {}
        
        batch = replay_buffer.sample(batch_size)
        
        state = torch.FloatTensor(batch['states']).to(self.device)
        action = torch.FloatTensor(batch['actions']).to(self.device)
        next_state = torch.FloatTensor(batch['next_states']).to(self.device)
        reward = torch.FloatTensor(batch['rewards']).unsqueeze(1).to(self.device)
        done = torch.FloatTensor(batch['dones']).unsqueeze(1).to(self.device)
        
        # 训练Critic
        with torch.no_grad():
            noise = (torch.randn_like(action) * self.policy_noise).clamp(
                -self.noise_clip, self.noise_clip
            )
            next_action = (self.target_actor(next_state) + noise).clamp(-1, 1)
            
            target_q1 = self.target_critic1(next_state, next_action)
            target_q2 = self.target_critic2(next_state, next_action)
            target_q = torch.min(target_q1, target_q2)
            target_q = reward + (1 - done) * self.gamma * target_q
        
        current_q1 = self.critic1(state, action)
        current_q2 = self.critic2(state, action)
        
        critic_loss = nn.MSELoss()(current_q1, target_q) + nn.MSELoss()(current_q2, target_q)
        
        self.critic_optimizer.zero_grad()
        critic_loss.backward()
        self.critic_optimizer.step()
        
        # 延迟更新Actor
        actor_loss = torch.tensor(0.0)
        if self.current_episode % self.policy_freq == 0:
            actor_loss = -self.critic1(state, self.actor(state)).mean()
            
            self.actor_optimizer.zero_grad()
            actor_loss.backward()
            self.actor_optimizer.step()
            
            # 软更新目标网络
            self._soft_update(self.target_actor, self.actor)
            self._soft_update(self.target_critic1, self.critic1)
            self._soft_update(self.target_critic2, self.critic2)
        
        return {
            'critic_loss': critic_loss.item(),
            'actor_loss': actor_loss.item() if isinstance(actor_loss, torch.Tensor) else 0.0
        }
    
    def _soft_update(self, target, source):
        """软更新目标网络"""
        for target_param, param in zip(target.parameters(), source.parameters()):
            target_param.data.copy_(target_param.data * (1.0 - self.tau) + param.data * self.tau)
    
    def update_episode_count(self):
        """更新episode计数"""
        self.current_episode += 1
    
    def save_models(self, filepath: str):
        """保存所有模型"""
        torch.save({
            'constraint_predictor': self.constraint_predictor.net.state_dict(),
            'actor': self.actor.state_dict(),
            'critic1': self.critic1.state_dict(),
            'critic2': self.critic2.state_dict(),
            'episode': self.current_episode
        }, filepath)
    
    def load_models(self, filepath: str):
        """加载所有模型"""
        checkpoint = torch.load(filepath, map_location=self.device)
        self.constraint_predictor.net.load_state_dict(checkpoint['constraint_predictor'])
        self.actor.load_state_dict(checkpoint['actor'])
        self.critic1.load_state_dict(checkpoint['critic1'])
        self.critic2.load_state_dict(checkpoint['critic2'])
        self.current_episode = checkpoint.get('episode', 0)

# 简单的Actor-Critic网络定义
class Actor(nn.Module):
    def __init__(self, state_dim, action_dim, max_action=1.0):
        super().__init__()
        self.net = nn.Sequential(
            nn.Linear(state_dim, 256),
            nn.ReLU(),
            nn.Linear(256, 256),
            nn.ReLU(),
            nn.Linear(256, action_dim),
            nn.Tanh()
        )
        self.max_action = max_action
    
    def forward(self, state):
        return self.max_action * self.net(state)

class Critic(nn.Module):
    def __init__(self, state_dim, action_dim):
        super().__init__()
        self.net = nn.Sequential(
            nn.Linear(state_dim + action_dim, 256),
            nn.ReLU(),
            nn.Linear(256, 256),
            nn.ReLU(),
            nn.Linear(256, 1)
        )
    
    def forward(self, state, action):
        return self.net(torch.cat([state, action], 1))
