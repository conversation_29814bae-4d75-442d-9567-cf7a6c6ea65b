"""
GPU优化的快速测试脚本
验证GPU使用和训练速度优化
"""

import torch
import numpy as np
import time
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from staged_training_framework import LoiteringMunitionStagedTrainer
from environment_config import TRAINING_STAGES

def check_gpu_availability():
    """检查GPU可用性"""
    print("🔍 GPU环境检查")
    print("=" * 40)
    
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {'✅' if torch.cuda.is_available() else '❌'}")
    
    if torch.cuda.is_available():
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
            print(f"  显存: {torch.cuda.get_device_properties(i).total_memory / 1024**3:.1f} GB")
        
        # 测试GPU计算
        device = torch.device("cuda")
        x = torch.randn(1000, 1000).to(device)
        start_time = time.time()
        y = torch.mm(x, x)
        torch.cuda.synchronize()
        gpu_time = time.time() - start_time
        print(f"GPU计算测试: {gpu_time:.4f}秒 ✅")
        
        return True
    else:
        print("⚠️  CUDA不可用，将使用CPU训练（速度较慢）")
        return False

def test_optimized_training():
    """测试优化后的训练速度"""
    print(f"\n🚀 优化训练速度测试")
    print("=" * 40)
    
    # 检查GPU
    gpu_available = check_gpu_availability()
    
    try:
        # 创建训练器
        trainer = LoiteringMunitionStagedTrainer(
            start_stage=1,
            end_stage=1,
            seed=42,
            visualization_interval=5  # 减少可视化频率
        )
        
        # 设置超快速测试配置
        original_random = TRAINING_STAGES["stage1_simple"]["random_episodes"]
        original_fixed = TRAINING_STAGES["stage1_simple"]["fixed_episodes"]
        
        TRAINING_STAGES["stage1_simple"]["random_episodes"] = 3  # 极少的episodes
        TRAINING_STAGES["stage1_simple"]["fixed_episodes"] = 2
        TRAINING_STAGES["stage1_simple"]["total_episodes"] = 5
        
        print(f"超快速配置: 3个随机 + 2个固定 = 5个episodes")
        print(f"GPU状态: {'启用' if gpu_available else '禁用'}")
        
        # 记录开始时间
        start_time = time.time()
        
        print(f"\n开始训练...")
        results, controller = trainer.run_staged_training()
        
        # 记录结束时间
        end_time = time.time()
        total_time = end_time - start_time
        
        # 恢复原始配置
        TRAINING_STAGES["stage1_simple"]["random_episodes"] = original_random
        TRAINING_STAGES["stage1_simple"]["fixed_episodes"] = original_fixed
        TRAINING_STAGES["stage1_simple"]["total_episodes"] = original_random + original_fixed
        
        print(f"\n📊 性能统计:")
        print(f"  总训练时间: {total_time:.2f} 秒")
        print(f"  平均每episode: {total_time/5:.2f} 秒")
        print(f"  GPU使用: {'✅' if gpu_available else '❌'}")
        
        if total_time < 60:  # 1分钟内完成
            print(f"✅ 训练速度优化成功！")
        else:
            print(f"⚠️  训练速度仍需优化")
        
        return True, total_time
        
    except Exception as e:
        print(f"❌ 优化训练测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, 0

def test_dwa_performance():
    """测试DWA控制器性能"""
    print(f"\n⚡ DWA控制器性能测试")
    print("=" * 40)
    
    try:
        from loitering_munition_environment import LoiteringMunitionEnvironment
        from loitering_munition_dwa import LoiteringMunitionDWA
        from environment_config import get_environment_config, get_loitering_munition_config
        
        # 创建环境和DWA
        env_config = get_environment_config('test_simple')
        lm_config = get_loitering_munition_config()
        
        env = LoiteringMunitionEnvironment(
            bounds=[1000, 1000, 100],
            environment_config=env_config
        )
        
        dwa = LoiteringMunitionDWA(dt=lm_config['dt'])
        
        # 设置测试场景
        env.start = np.array([100, 100, 50])
        env.goal = np.array([800, 800, 50])
        env.obstacles = []  # 简化场景
        env.dynamic_obstacles = []
        
        state = env.reset()
        
        print(f"DWA参数:")
        print(f"  预测时间: {dwa.predict_time}s")
        print(f"  分辨率: aT={dwa.a_T_resolution}, aN={dwa.a_N_resolution}, μ={dwa.mu_resolution}")
        
        # 测试DWA控制生成速度
        num_tests = 10
        total_time = 0
        
        for i in range(num_tests):
            start_time = time.time()
            
            safe_controls = dwa.generate_safe_control_set(
                env.state, env.obstacles + env.dynamic_obstacles, env.goal, max_actions=5
            )
            
            end_time = time.time()
            total_time += (end_time - start_time)
            
            if i == 0:
                print(f"  生成安全控制数量: {len(safe_controls)}")
        
        avg_time = total_time / num_tests
        print(f"  平均生成时间: {avg_time:.4f}秒")
        
        if avg_time < 0.1:  # 100ms内
            print(f"✅ DWA性能优化成功！")
        else:
            print(f"⚠️  DWA性能需要进一步优化")
        
        return True, avg_time
        
    except Exception as e:
        print(f"❌ DWA性能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, 0

def test_memory_usage():
    """测试内存使用情况"""
    print(f"\n💾 内存使用测试")
    print("=" * 40)
    
    try:
        import psutil
        
        # 获取当前进程
        process = psutil.Process()
        
        # 初始内存
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        print(f"初始内存使用: {initial_memory:.1f} MB")
        
        # 创建TD3网络测试内存
        from td3_network import TD3Agent
        from environment_config import get_td3_config
        
        td3_config = get_td3_config()
        agent = TD3Agent(
            state_dim=td3_config['state_dim'],
            action_dim=td3_config['action_dim'],
            max_action=td3_config['max_action']
        )
        
        # 网络创建后内存
        network_memory = process.memory_info().rss / 1024 / 1024  # MB
        print(f"网络创建后: {network_memory:.1f} MB (+{network_memory-initial_memory:.1f} MB)")
        
        # 添加一些经验数据
        for _ in range(1000):
            state = np.random.randn(td3_config['state_dim'])
            action = np.random.randn(td3_config['action_dim'])
            reward = np.random.randn()
            next_state = np.random.randn(td3_config['state_dim'])
            done = False
            agent.replay_buffer.add(state, action, reward, next_state, done)
        
        # 数据添加后内存
        data_memory = process.memory_info().rss / 1024 / 1024  # MB
        print(f"数据添加后: {data_memory:.1f} MB (+{data_memory-network_memory:.1f} MB)")
        
        # GPU内存使用（如果可用）
        if torch.cuda.is_available():
            gpu_memory = torch.cuda.memory_allocated() / 1024 / 1024  # MB
            print(f"GPU内存使用: {gpu_memory:.1f} MB")
        
        print(f"✅ 内存使用正常")
        return True
        
    except ImportError:
        print(f"⚠️  psutil未安装，跳过内存测试")
        return True
    except Exception as e:
        print(f"❌ 内存测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 GPU优化和性能测试")
    print("=" * 60)
    
    tests = [
        ("GPU环境检查", check_gpu_availability),
        ("DWA性能测试", test_dwa_performance),
        ("内存使用测试", test_memory_usage),
        ("优化训练测试", test_optimized_training)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*20} {test_name} {'='*20}")
            if test_name == "GPU环境检查":
                result = test_func()
            else:
                result, *_ = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果摘要
    print(f"\n{'='*20} 测试结果摘要 {'='*20}")
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 测试统计: {passed}/{len(results)} 通过")
    
    if passed == len(results):
        print("🎉 所有优化测试通过！")
        print("💡 优化建议:")
        print("  ✅ GPU已启用（如果可用）")
        print("  ✅ DWA参数已优化")
        print("  ✅ 训练配置已调整")
        print("  ✅ 内存使用正常")
        return True
    else:
        print("⚠️  部分优化需要进一步调整")
        return False

if __name__ == "__main__":
    main()
