"""
训练结果GIF生成器 - 巡飞简化ver
基于训练结果生成导航动画，与简化ver1的staged_gif_generator.py功能相同
"""

import numpy as np
import torch
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.animation as animation
import os
import glob
import json
from datetime import datetime

# 设置matplotlib使用英文字体，避免中文字体问题
plt.rcParams['font.family'] = ['DejaVu Sans', 'Arial', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

from loitering_munition_environment import LoiteringMunitionEnvironment
from td3_network import StabilizedTD3Controller
from environment_config import get_environment_config, get_td3_config

def find_latest_training_directory():
    """查找最新的训练结果目录"""
    print("🔍 查找训练结果目录...")
    
    # 查找所有训练目录，排除非目录文件
    pattern = "loitering_munition_staged_training_*"
    all_matches = glob.glob(pattern)
    
    # 只保留目录，排除文件
    training_dirs = []
    for d in all_matches:
        if os.path.isdir(d):
            # 确保是纯粹的训练目录，不包含其他后缀
            import re
            if re.match(r'^loitering_munition_staged_training_\d{8}_\d{6}$', d):
                training_dirs.append(d)
    
    if not training_dirs:
        print("❌ 未找到训练结果目录")
        return None
    
    # 按时间戳排序，获取最新的
    training_dirs.sort(reverse=True)
    latest_dir = training_dirs[0]
    
    print(f"✅ 找到最新训练目录: {latest_dir}")
    return latest_dir

def load_trained_model(training_dir):
    """加载训练好的模型"""
    model_files = glob.glob(os.path.join(training_dir, '*_model.pth'))
    if not model_files:
        print("❌ 未找到模型文件")
        return None
    
    model_file = model_files[0]  # 使用第一个模型文件
    print(f"📦 加载模型: {os.path.basename(model_file)}")
    
    # 创建控制器并加载模型
    td3_config = get_td3_config()
    td3_config.update({
        'state_dim': 15,
        'action_dim': 3,
        'max_action': 1.0
    })
    controller = StabilizedTD3Controller(td3_config)
    controller.load(model_file)

    # 设置为评估模式（测试模式，不学习）
    controller.actor.eval()
    controller.critic.eval()

    print(f"✅ 模型已设置为评估模式（测试模式）")

    return controller

def generate_navigation_trajectory(controller, max_steps=2000):
    """生成导航轨迹 - 使用与训练时相同的DWA+TD3融合逻辑"""
    print(f"🚀 生成导航轨迹 (最大{max_steps}步)...")
    print("🔧 使用DWA+TD3融合逻辑 (与训练时相同)")
    print("📋 测试模式: 只推理，不学习，不更新模型")

    # 创建环境
    env_config = get_environment_config('stage1_simple')
    env = LoiteringMunitionEnvironment(
        bounds=[2000, 2000, 2000],
        environment_config=env_config,
        reward_type='simplified'
    )

    # 创建DWA控制器
    from loitering_munition_dwa import LoiteringMunitionDWA
    dwa = LoiteringMunitionDWA(dt=0.1)

    # 重置环境
    obs = env.reset()

    # 确保环境的max_steps不会限制我们的测试
    env.max_steps = max_steps
    env.step_count = 0

    # 记录轨迹数据
    trajectory = [env.state[:3].copy()]
    rewards = []

    # 记录约束相关数据
    velocities = [env.state[3]]  # 速度
    gammas = [env.state[4]]      # 航迹倾斜角
    psis = [env.state[5]]        # 偏航角
    accelerations_T = []         # 切向加速度
    accelerations_N = []         # 法向加速度
    control_inputs = []          # 控制输入

    print(f"起点: [{env.start[0]:.1f}, {env.start[1]:.1f}, {env.start[2]:.1f}]")
    print(f"目标: [{env.goal[0]:.1f}, {env.goal[1]:.1f}, {env.goal[2]:.1f}]")
    print(f"障碍物数量: {len(env.obstacles)}")

    step = 0
    episode_reward = 0

    # 使用torch.no_grad()确保测试时不计算梯度，不更新模型
    with torch.no_grad():
        while step < max_steps:
            # 获取观测值（15维）
            state = env._get_observation()

            # 完全复制训练时的动作生成逻辑
            # 1. DWA生成安全动作集
            safe_controls = dwa.generate_safe_control_set(
                env.state, env.obstacles + env.dynamic_obstacles, env.goal, max_actions=20
            )

            if safe_controls:
                # 测试时使用后期阶段的逻辑（TD3从安全动作集中选择最优的）
                # 2. 将安全控制转换为归一化动作集
                safe_actions = [dwa.get_normalized_action(control) for control in safe_controls]

                # 3. TD3选择动作（测试时不添加噪声）
                td3_action = controller.select_action(state, noise=0)

                # 4. 从安全动作集中找到最接近TD3选择的动作
                best_idx = 0
                min_distance = float('inf')
                for i, safe_action in enumerate(safe_actions):
                    distance = np.linalg.norm(td3_action - safe_action)
                    if distance < min_distance:
                        min_distance = distance
                        best_idx = i

                action = safe_actions[best_idx]
            else:
                # 如果没有安全动作，使用紧急制动（与训练时相同）
                action = np.array([-0.5, 0.0, 0.0])
                print(f"⚠️ 步骤 {step}: 没有安全动作，使用紧急制动")

            # 5. 将归一化动作转换为实际控制输入（与训练时完全相同）
            control_input = np.array([
                action[0] * env.a_T_max,
                action[1] * env.a_N_max,
                action[2] * (np.pi/2)
            ])

            # 6. 执行控制输入
            next_obs, reward, done, info = env.step(control_input)

            # 记录数据
            trajectory.append(env.state[:3].copy())
            rewards.append(reward)
            episode_reward += reward

            # 记录约束相关数据
            velocities.append(env.state[3])
            gammas.append(env.state[4])
            psis.append(env.state[5])
            accelerations_T.append(control_input[0])
            accelerations_N.append(control_input[1])
            control_inputs.append(control_input.copy())

            step += 1

            # 每50步输出一次进度
            if step % 50 == 0:
                current_dist = np.linalg.norm(env.state[:3] - env.goal)
                print(f"步骤 {step}: 距离目标 {current_dist:.1f}m, 安全动作数 {len(safe_controls)}")

            # 检查是否完成
            if done:
                final_distance = np.linalg.norm(env.state[:3] - env.goal)
                success = final_distance <= 50.0
                print(f"✅ 任务完成于第{step}步")
                print(f"最终距离: {final_distance:.1f}m")
                print(f"任务状态: {'成功' if success else '失败'}")
                break

    print(f"总奖励: {episode_reward:.1f}")
    print(f"轨迹点数: {len(trajectory)}")

    return {
        'trajectory': np.array(trajectory),
        'rewards': rewards,
        'environment': env,
        'total_steps': step,
        'total_reward': episode_reward,
        'velocities': np.array(velocities),
        'gammas': np.array(gammas),
        'psis': np.array(psis),
        'accelerations_T': np.array(accelerations_T),
        'accelerations_N': np.array(accelerations_N),
        'control_inputs': np.array(control_inputs)
    }

def create_gif_animation(trajectory_data, fps=12):
    """创建GIF动画"""
    print(f"🎬 创建GIF动画 (FPS: {fps})...")
    
    trajectory = trajectory_data['trajectory']
    env = trajectory_data['environment']
    
    # 创建图形
    fig = plt.figure(figsize=(12, 10))
    ax = fig.add_subplot(111, projection='3d')
    
    # 设置坐标轴范围
    ax.set_xlim(0, env.bounds[0])
    ax.set_ylim(0, env.bounds[1])
    ax.set_zlim(0, env.bounds[2])
    ax.set_xlabel('X (m)', fontsize=12)
    ax.set_ylabel('Y (m)', fontsize=12)
    ax.set_zlabel('Z (m)', fontsize=12)
    
    # 绘制静态元素
    # 起点和目标
    ax.scatter(*env.start, color='green', s=200, marker='o', label='Start', alpha=0.9)
    ax.scatter(*env.goal, color='red', s=200, marker='*', label='Target', alpha=0.9)
    
    # 绘制障碍物
    obstacle_colors = ['orange', 'yellow', 'purple', 'brown', 'pink']
    for i, obstacle in enumerate(env.obstacles):
        # 高分辨率球体
        u = np.linspace(0, 2 * np.pi, 20)
        v = np.linspace(0, np.pi, 20)
        
        center = obstacle['center']
        radius = obstacle['radius']
        x = center[0] + radius * np.outer(np.cos(u), np.sin(v))
        y = center[1] + radius * np.outer(np.sin(u), np.sin(v))
        z = center[2] + radius * np.outer(np.ones(np.size(u)), np.cos(v))
        
        color = obstacle_colors[i % len(obstacle_colors)]
        ax.plot_surface(x, y, z, alpha=0.4, color=color, edgecolor='black', linewidth=0.1)
    
    # 初始化动画元素
    line, = ax.plot([], [], [], 'b-', linewidth=3, alpha=0.8, label='Trajectory')
    point, = ax.plot([], [], [], 'bo', markersize=8, alpha=0.9)
    
    ax.legend()
    ax.set_title('Loitering Munition Navigation Animation', fontsize=14, fontweight='bold')
    
    def animate(frame):
        """动画更新函数"""
        if frame < len(trajectory):
            # 更新轨迹线
            line.set_data(trajectory[:frame+1, 0], trajectory[:frame+1, 1])
            line.set_3d_properties(trajectory[:frame+1, 2])
            
            # 更新当前位置点
            current_pos = trajectory[frame]
            point.set_data([current_pos[0]], [current_pos[1]])
            point.set_3d_properties([current_pos[2]])
            
            # 更新标题显示当前步数
            ax.set_title(f'Loitering Munition Navigation - Step {frame+1}/{len(trajectory)}', 
                        fontsize=14, fontweight='bold')
        
        return line, point
    
    # 创建动画
    anim = animation.FuncAnimation(
        fig, animate, frames=len(trajectory), 
        interval=1000//fps, blit=False, repeat=True
    )
    
    # 生成文件名
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    gif_filename = f'loitering_munition_navigation_{timestamp}.gif'
    
    print(f"💾 保存GIF动画: {gif_filename}")
    anim.save(gif_filename, writer='pillow', fps=fps, dpi=100)
    plt.close()
    
    return gif_filename

def generate_static_plots(trajectory_data):
    """生成静态分析图"""
    print("📊 生成静态分析图...")
    
    trajectory = trajectory_data['trajectory']
    rewards = trajectory_data['rewards']
    env = trajectory_data['environment']
    
    # 创建多子图
    fig = plt.figure(figsize=(16, 12))
    
    # 3D轨迹图
    ax1 = fig.add_subplot(221, projection='3d')
    ax1.plot(trajectory[:, 0], trajectory[:, 1], trajectory[:, 2], 'b-', linewidth=3, alpha=0.8)
    ax1.scatter(*env.start, color='green', s=100, marker='o', label='Start')
    ax1.scatter(*env.goal, color='red', s=100, marker='*', label='Target')
    ax1.scatter(*trajectory[-1], color='blue', s=100, marker='X', label='End')
    
    # 绘制障碍物
    for i, obstacle in enumerate(env.obstacles):
        center = obstacle['center']
        ax1.scatter(*center, color='orange', s=50, marker='s', alpha=0.7)
    
    ax1.set_xlabel('X (m)')
    ax1.set_ylabel('Y (m)')
    ax1.set_zlabel('Z (m)')
    ax1.set_title('3D Trajectory')
    ax1.legend()
    
    # XY平面投影
    ax2 = fig.add_subplot(222)
    ax2.plot(trajectory[:, 0], trajectory[:, 1], 'b-', linewidth=2, alpha=0.8)
    ax2.scatter(*env.start[:2], color='green', s=100, marker='o', label='Start')
    ax2.scatter(*env.goal[:2], color='red', s=100, marker='*', label='Target')
    
    # 绘制障碍物投影
    for obstacle in env.obstacles:
        center = obstacle['center']
        radius = obstacle['radius']
        circle = plt.Circle(center[:2], radius, fill=False, color='orange', alpha=0.7)
        ax2.add_patch(circle)
    
    ax2.set_xlabel('X (m)')
    ax2.set_ylabel('Y (m)')
    ax2.set_title('XY Plane Projection')
    ax2.legend()
    ax2.set_aspect('equal')
    ax2.grid(True, alpha=0.3)
    
    # 奖励曲线
    ax3 = fig.add_subplot(223)
    ax3.plot(range(len(rewards)), rewards, 'g-', linewidth=2)
    ax3.set_xlabel('Step')
    ax3.set_ylabel('Reward')
    ax3.set_title('Reward Curve')
    ax3.grid(True, alpha=0.3)
    
    # 距离变化
    ax4 = fig.add_subplot(224)
    distances = [np.linalg.norm(pos - env.goal) for pos in trajectory]
    ax4.plot(range(len(distances)), distances, 'r-', linewidth=2)
    ax4.set_xlabel('Step')
    ax4.set_ylabel('Distance to Target (m)')
    ax4.set_title('Distance to Target')
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存静态图
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    static_filename = f'loitering_munition_analysis_{timestamp}.png'
    plt.savefig(static_filename, dpi=300, bbox_inches='tight')
    plt.close()
    
    return static_filename

def generate_constraint_analysis(trajectory_data):
    """生成详细的约束分析报告"""
    print("📊 生成约束分析报告...")

    env = trajectory_data['environment']
    velocities = trajectory_data['velocities']
    gammas = trajectory_data['gammas']
    psis = trajectory_data['psis']
    accelerations_T = trajectory_data['accelerations_T']
    accelerations_N = trajectory_data['accelerations_N']

    # 从环境中读取约束参数
    V_min = env.V_min
    V_max = env.V_max
    a_T_max = env.a_T_max
    a_N_max = env.a_N_max
    gamma_max = env.gamma_max

    # 创建约束分析图
    fig = plt.figure(figsize=(20, 15))

    # 时间轴
    time_steps = np.arange(len(velocities))

    # 1. 速度约束分析
    ax1 = fig.add_subplot(231)
    ax1.plot(time_steps, velocities, 'b-', linewidth=2, label='Actual Velocity')
    ax1.axhline(y=V_min, color='red', linestyle='--', linewidth=2, alpha=0.8, label=f'V_min = {V_min:.1f} m/s')
    ax1.axhline(y=V_max, color='red', linestyle='--', linewidth=2, alpha=0.8, label=f'V_max = {V_max:.1f} m/s')
    ax1.fill_between(time_steps, V_min, V_max, alpha=0.2, color='green', label='Safe Zone')
    ax1.set_xlabel('Time Steps')
    ax1.set_ylabel('Velocity (m/s)')
    ax1.set_title('Velocity Constraints', fontsize=14, fontweight='bold')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 检查违反情况
    v_violations = np.sum((velocities < V_min) | (velocities > V_max))
    ax1.text(0.02, 0.98, f'Violations: {v_violations}/{len(velocities)}',
             transform=ax1.transAxes, fontsize=12, verticalalignment='top',
             bbox=dict(boxstyle="round,pad=0.3", facecolor='yellow' if v_violations > 0 else 'lightgreen'))

    # 2. 切向加速度约束分析
    ax2 = fig.add_subplot(232)
    if len(accelerations_T) > 0:
        ax2.plot(time_steps[1:], accelerations_T, 'g-', linewidth=2, label='Actual a_T')
        ax2.axhline(y=a_T_max, color='red', linestyle='--', linewidth=2, alpha=0.8, label=f'a_T_max = {a_T_max:.1f} m/s²')
        ax2.axhline(y=-a_T_max, color='red', linestyle='--', linewidth=2, alpha=0.8, label=f'a_T_min = {-a_T_max:.1f} m/s²')
        ax2.fill_between(time_steps[1:], -a_T_max, a_T_max, alpha=0.2, color='green', label='Safe Zone')

        # 检查违反情况
        aT_violations = np.sum((accelerations_T < -a_T_max) | (accelerations_T > a_T_max))
        ax2.text(0.02, 0.98, f'Violations: {aT_violations}/{len(accelerations_T)}',
                 transform=ax2.transAxes, fontsize=12, verticalalignment='top',
                 bbox=dict(boxstyle="round,pad=0.3", facecolor='yellow' if aT_violations > 0 else 'lightgreen'))

    ax2.set_xlabel('Time Steps')
    ax2.set_ylabel('Tangential Acceleration (m/s²)')
    ax2.set_title('Tangential Acceleration Constraints', fontsize=14, fontweight='bold')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # 3. 法向加速度约束分析
    ax3 = fig.add_subplot(233)
    if len(accelerations_N) > 0:
        ax3.plot(time_steps[1:], accelerations_N, 'orange', linewidth=2, label='Actual a_N')
        ax3.axhline(y=a_N_max, color='red', linestyle='--', linewidth=2, alpha=0.8, label=f'a_N_max = {a_N_max:.1f} m/s²')
        ax3.axhline(y=-a_N_max, color='red', linestyle='--', linewidth=2, alpha=0.8, label=f'a_N_min = {-a_N_max:.1f} m/s²')
        ax3.fill_between(time_steps[1:], -a_N_max, a_N_max, alpha=0.2, color='green', label='Safe Zone')

        # 检查违反情况
        aN_violations = np.sum((accelerations_N < -a_N_max) | (accelerations_N > a_N_max))
        ax3.text(0.02, 0.98, f'Violations: {aN_violations}/{len(accelerations_N)}',
                 transform=ax3.transAxes, fontsize=12, verticalalignment='top',
                 bbox=dict(boxstyle="round,pad=0.3", facecolor='yellow' if aN_violations > 0 else 'lightgreen'))

    ax3.set_xlabel('Time Steps')
    ax3.set_ylabel('Normal Acceleration (m/s²)')
    ax3.set_title('Normal Acceleration Constraints', fontsize=14, fontweight='bold')
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # 4. 航迹倾斜角约束分析
    ax4 = fig.add_subplot(234)
    gammas_deg = np.degrees(gammas)
    gamma_max_deg = np.degrees(gamma_max)
    ax4.plot(time_steps, gammas_deg, 'purple', linewidth=2, label='Actual γ')
    ax4.axhline(y=gamma_max_deg, color='red', linestyle='--', linewidth=2, alpha=0.8, label=f'γ_max = {gamma_max_deg:.1f}°')
    ax4.axhline(y=-gamma_max_deg, color='red', linestyle='--', linewidth=2, alpha=0.8, label=f'γ_min = {-gamma_max_deg:.1f}°')
    ax4.fill_between(time_steps, -gamma_max_deg, gamma_max_deg, alpha=0.2, color='green', label='Safe Zone')
    ax4.set_xlabel('Time Steps')
    ax4.set_ylabel('Flight Path Angle (degrees)')
    ax4.set_title('Flight Path Angle Constraints', fontsize=14, fontweight='bold')
    ax4.legend()
    ax4.grid(True, alpha=0.3)

    # 检查违反情况
    gamma_violations = np.sum((gammas < -gamma_max) | (gammas > gamma_max))
    ax4.text(0.02, 0.98, f'Violations: {gamma_violations}/{len(gammas)}',
             transform=ax4.transAxes, fontsize=12, verticalalignment='top',
             bbox=dict(boxstyle="round,pad=0.3", facecolor='yellow' if gamma_violations > 0 else 'lightgreen'))

    # 5. 约束违反统计
    ax5 = fig.add_subplot(235)
    constraint_names = ['Velocity', 'Tangential Acc', 'Normal Acc', 'Flight Path Angle']
    violation_counts = [v_violations, aT_violations if len(accelerations_T) > 0 else 0,
                       aN_violations if len(accelerations_N) > 0 else 0, gamma_violations]
    total_steps = [len(velocities), len(accelerations_T), len(accelerations_N), len(gammas)]

    colors = ['red' if count > 0 else 'green' for count in violation_counts]
    bars = ax5.bar(constraint_names, violation_counts, color=colors, alpha=0.7, edgecolor='black')

    # 添加百分比标签
    for i, (bar, count, total) in enumerate(zip(bars, violation_counts, total_steps)):
        if total > 0:
            percentage = (count / total) * 100
            ax5.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                    f'{count}\n({percentage:.1f}%)', ha='center', va='bottom', fontsize=10, fontweight='bold')

    ax5.set_ylabel('Number of Violations')
    ax5.set_title('Constraint Violations Summary', fontsize=14, fontweight='bold')
    ax5.tick_params(axis='x', rotation=45)

    # 6. 约束遵守率饼图
    ax6 = fig.add_subplot(236)
    total_violations = sum(violation_counts)
    total_checks = sum(total_steps)
    compliance_rate = ((total_checks - total_violations) / total_checks) * 100 if total_checks > 0 else 100

    sizes = [compliance_rate, 100 - compliance_rate]
    labels = [f'Compliant\n({compliance_rate:.1f}%)', f'Violations\n({100-compliance_rate:.1f}%)']
    colors_pie = ['lightgreen', 'lightcoral']

    wedges, texts, autotexts = ax6.pie(sizes, labels=labels, colors=colors_pie, autopct='',
                                       startangle=90, textprops={'fontsize': 12, 'fontweight': 'bold'})
    ax6.set_title('Overall Constraint Compliance', fontsize=14, fontweight='bold')

    plt.tight_layout()

    # 保存约束分析图
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    constraint_filename = f'loitering_munition_constraints_{timestamp}.png'
    plt.savefig(constraint_filename, dpi=300, bbox_inches='tight')
    plt.close()

    # 生成约束分析报告
    report = {
        'constraint_parameters': {
            'V_min': V_min,
            'V_max': V_max,
            'a_T_max': a_T_max,
            'a_N_max': a_N_max,
            'gamma_max_deg': gamma_max_deg
        },
        'violations': {
            'velocity': v_violations,
            'tangential_acceleration': aT_violations if len(accelerations_T) > 0 else 0,
            'normal_acceleration': aN_violations if len(accelerations_N) > 0 else 0,
            'flight_path_angle': gamma_violations
        },
        'compliance_rate': compliance_rate,
        'total_steps': len(velocities)
    }

    return constraint_filename, report

def print_training_summary(training_dir):
    """打印训练摘要"""
    try:
        results_file = os.path.join(training_dir, 'staged_training_results.json')
        if os.path.exists(results_file):
            with open(results_file, 'r', encoding='utf-8') as f:
                results = json.load(f)

            print(f"\n📋 训练摘要 from {training_dir}:")
            print("=" * 50)

            if 'stages' in results:
                for stage_key, stage_data in results['stages'].items():
                    if 'error' not in stage_data:
                        print(f"{stage_key}:")
                        print(f"  • 总episodes: {stage_data.get('total_episodes', 'N/A')}")
                        print(f"  • 成功率: {stage_data.get('success_rate', 0):.2%}")
                        print(f"  • 平均奖励: {stage_data.get('final_avg_reward', 0):.1f}")
                        print(f"  • 随机阶段成功率: {stage_data.get('random_phase_success_rate', 0):.2%}")
                        print(f"  • 固定阶段成功率: {stage_data.get('fixed_phase_success_rate', 0):.2%}")

            print("=" * 50)

    except Exception as e:
        print(f"⚠️ 无法读取训练摘要: {e}")

def main(max_steps=2000, fps=12):
    """主函数"""
    print("🎬 训练结果GIF生成器 - 巡飞简化ver")
    print("=" * 60)
    print(f"📅 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # 1. 查找最新训练目录
    training_dir = find_latest_training_directory()
    if not training_dir:
        print("❌ 未找到训练结果，请先运行训练")
        return False

    # 2. 打印训练摘要
    print_training_summary(training_dir)

    # 3. 加载训练好的模型
    controller = load_trained_model(training_dir)
    if not controller:
        print("❌ 模型加载失败")
        return False

    # 4. 生成导航轨迹
    trajectory_data = generate_navigation_trajectory(controller, max_steps=max_steps)

    # 5. 创建GIF动画
    gif_filename = create_gif_animation(trajectory_data, fps=fps)

    # 6. 生成静态分析图
    static_filename = generate_static_plots(trajectory_data)

    # 7. 生成约束分析报告
    constraint_filename, constraint_report = generate_constraint_analysis(trajectory_data)

    # 8. 总结结果
    print(f"\n" + "=" * 60)
    print(f"🎉 GIF生成完成!")
    print(f"=" * 60)
    print(f"📁 GIF动画: {gif_filename}")
    print(f"📊 分析图表: {static_filename}")
    print(f"🔒 约束分析: {constraint_filename}")
    print(f"🎯 轨迹步数: {trajectory_data['total_steps']}")
    print(f"🏆 总奖励: {trajectory_data['total_reward']:.1f}")

    # 检查任务完成情况
    final_pos = trajectory_data['trajectory'][-1]
    goal_pos = trajectory_data['environment'].goal
    final_distance = np.linalg.norm(final_pos - goal_pos)
    success = final_distance <= 50.0

    print(f"📍 最终距离: {final_distance:.1f}m")
    print(f"✅ 任务状态: {'成功' if success else '失败'}")

    # 打印约束分析摘要
    print(f"\n🔒 约束遵守情况:")
    print(f"  总体遵守率: {constraint_report['compliance_rate']:.1f}%")
    print(f"  速度约束违反: {constraint_report['violations']['velocity']} 次")
    print(f"  切向加速度违反: {constraint_report['violations']['tangential_acceleration']} 次")
    print(f"  法向加速度违反: {constraint_report['violations']['normal_acceleration']} 次")
    print(f"  航迹倾斜角违反: {constraint_report['violations']['flight_path_angle']} 次")

    print(f"\n💡 完整的测试分析包含:")
    print(f"  ✅ 3D导航动画")
    print(f"  ✅ 障碍物可视化")
    print(f"  ✅ 轨迹分析图表")
    print(f"  ✅ 详细约束分析 (红色虚线标注约束界限)")
    print(f"  ✅ 训练结果验证")

    return True

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='训练结果GIF生成器 - 巡飞简化ver')
    parser.add_argument('--steps', type=int, default=2000, help='最大步数 (默认 2000)')
    parser.add_argument('--fps', type=int, default=12, help='帧率 (默认 12)')

    args = parser.parse_args()

    try:
        success = main(max_steps=args.steps, fps=args.fps)
        if success:
            print(f"\n🎬 GIF生成成功！可以查看生成的动画文件")
        else:
            print(f"\n❌ GIF生成失败")
    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 生成过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
