"""
测试修正后的训练顺序
验证现在是：先随机探索，再固定强化
"""

from environment_config import print_all_configs, get_training_stage_config

print("🔍 验证训练顺序修正")
print("=" * 50)

print("📋 当前训练阶段配置:")
print_all_configs()

print("\n✅ 验证结果:")
print("现在的训练顺序是：")
print("1️⃣ 随机场景探索 - 探索各种场景，评估复杂度")
print("2️⃣ 固定场景强化 - 选择最复杂场景进行强化训练")
print()
print("这与您原始设计一致：先随机200步，再从中选最复杂的进行固定训练！")

# 验证配置
stage1_config = get_training_stage_config("stage1_simple")
print(f"\n📊 阶段1配置验证:")
print(f"  • 随机探索: {stage1_config['random_episodes']} episodes")
print(f"  • 固定强化: {stage1_config['fixed_episodes']} episodes")
print(f"  • 总计: {stage1_config['total_episodes']} episodes")

print("\n🎯 顺序已修正完成！")
