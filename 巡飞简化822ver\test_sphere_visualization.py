"""
球体可视化测试脚本
验证障碍物球体的正确显示
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from loitering_munition_environment import LoiteringMunitionEnvironment
from environment_config import get_environment_config, get_loitering_munition_config

def test_sphere_rendering():
    """测试球体渲染效果"""
    print("🎨 球体可视化测试")
    print("=" * 40)
    
    # 创建测试环境
    env_config = get_environment_config('test_simple')
    lm_config = get_loitering_munition_config()
    
    env = LoiteringMunitionEnvironment(
        bounds=[1000, 1000, 200],
        environment_config=env_config,
        reward_type='simplified'
    )
    
    # 手动创建一些测试球体
    test_spheres = [
        {'center': np.array([200, 200, 100]), 'radius': 30, 'color': 'red', 'name': '测试球体1'},
        {'center': np.array([500, 300, 80]), 'radius': 25, 'color': 'blue', 'name': '测试球体2'},
        {'center': np.array([800, 600, 120]), 'radius': 35, 'color': 'green', 'name': '测试球体3'},
        {'center': np.array([300, 700, 60]), 'radius': 20, 'color': 'purple', 'name': '测试球体4'}
    ]
    
    # 创建对比图
    fig = plt.figure(figsize=(16, 8))
    
    # 子图1：原始方法（可能有问题的）
    ax1 = fig.add_subplot(1, 2, 1, projection='3d')
    ax1.set_title('原始球体渲染方法', fontsize=14, fontweight='bold')
    
    for sphere in test_spheres:
        # 原始方法（可能导致扁平）
        u = np.linspace(0, 2 * np.pi, 10)
        v = np.linspace(0, np.pi, 10)
        x = sphere['center'][0] + sphere['radius'] * np.outer(np.cos(u), np.sin(v))
        y = sphere['center'][1] + sphere['radius'] * np.outer(np.sin(u), np.sin(v))
        z = sphere['center'][2] + sphere['radius'] * np.outer(np.ones(np.size(u)), np.cos(v))
        ax1.plot_surface(x, y, z, alpha=0.6, color=sphere['color'])
    
    ax1.set_xlim(0, 1000)
    ax1.set_ylim(0, 1000)
    ax1.set_zlim(0, 200)
    ax1.set_xlabel('X (m)')
    ax1.set_ylabel('Y (m)')
    ax1.set_zlabel('Z (m)')
    
    # 子图2：修复后的方法
    ax2 = fig.add_subplot(1, 2, 2, projection='3d')
    ax2.set_title('修复后球体渲染方法', fontsize=14, fontweight='bold')
    
    for sphere in test_spheres:
        # 修复后的方法
        u = np.linspace(0, 2 * np.pi, 20)  # 增加分辨率
        v = np.linspace(0, np.pi, 20)      # 增加分辨率
        
        # 正确的球体参数化
        x = sphere['center'][0] + sphere['radius'] * np.outer(np.cos(u), np.sin(v))
        y = sphere['center'][1] + sphere['radius'] * np.outer(np.sin(u), np.sin(v))
        z = sphere['center'][2] + sphere['radius'] * np.outer(np.ones(np.size(u)), np.cos(v))
        
        ax2.plot_surface(x, y, z, alpha=0.6, color=sphere['color'])
    
    ax2.set_xlim(0, 1000)
    ax2.set_ylim(0, 1000)
    ax2.set_zlim(0, 200)
    ax2.set_xlabel('X (m)')
    ax2.set_ylabel('Y (m)')
    ax2.set_zlabel('Z (m)')
    
    # 设置等比例坐标轴
    try:
        ax2.set_box_aspect([1000, 1000, 200])
        print("✅ 使用set_box_aspect设置等比例坐标轴")
    except:
        try:
            ax2.set_aspect('equal')
            print("✅ 使用set_aspect设置等比例坐标轴")
        except:
            print("⚠️  无法设置等比例坐标轴，可能影响球体显示")
    
    plt.tight_layout()
    plt.savefig('sphere_visualization_test.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("📊 球体可视化对比图已保存: sphere_visualization_test.png")

def test_environment_spheres():
    """测试环境中的球体显示"""
    print(f"\n🌍 环境球体显示测试")
    print("=" * 40)
    
    # 创建环境
    env_config = get_environment_config('stage1_simple')
    lm_config = get_loitering_munition_config()
    
    env = LoiteringMunitionEnvironment(
        bounds=[1000, 1000, 200],
        environment_config=env_config,
        reward_type='simplified'
    )
    
    state = env.reset()
    
    print(f"环境信息:")
    print(f"  边界: {env.bounds}")
    print(f"  静态障碍物数量: {len(env.obstacles)}")
    print(f"  动态障碍物数量: {len(env.dynamic_obstacles)}")
    
    # 显示障碍物信息
    for i, obs in enumerate(env.obstacles):
        print(f"  静态障碍物 {i+1}: 中心{obs['center']}, 半径{obs['radius']:.1f}")
    
    for i, obs in enumerate(env.dynamic_obstacles):
        print(f"  动态障碍物 {i+1}: 中心{obs['center']}, 半径{obs['radius']:.1f}")
    
    # 创建3D可视化
    fig = plt.figure(figsize=(12, 10))
    ax = fig.add_subplot(111, projection='3d')
    
    # 绘制起点和终点
    ax.scatter(*env.start, color='green', s=200, marker='o', label='起点', alpha=0.8)
    ax.scatter(*env.goal, color='red', s=200, marker='*', label='目标', alpha=0.8)
    
    # 绘制静态障碍物（修复后的方法）
    for i, obs in enumerate(env.obstacles):
        u = np.linspace(0, 2 * np.pi, 25)  # 高分辨率
        v = np.linspace(0, np.pi, 25)
        
        x = obs['center'][0] + obs['radius'] * np.outer(np.cos(u), np.sin(v))
        y = obs['center'][1] + obs['radius'] * np.outer(np.sin(u), np.sin(v))
        z = obs['center'][2] + obs['radius'] * np.outer(np.ones(np.size(u)), np.cos(v))
        
        ax.plot_surface(x, y, z, alpha=0.5, color='gray',
                       label='静态障碍物' if i == 0 else "")
    
    # 绘制动态障碍物（修复后的方法）
    for i, obs in enumerate(env.dynamic_obstacles):
        u = np.linspace(0, 2 * np.pi, 25)  # 高分辨率
        v = np.linspace(0, np.pi, 25)
        
        x = obs['center'][0] + obs['radius'] * np.outer(np.cos(u), np.sin(v))
        y = obs['center'][1] + obs['radius'] * np.outer(np.sin(u), np.sin(v))
        z = obs['center'][2] + obs['radius'] * np.outer(np.ones(np.size(u)), np.cos(v))
        
        ax.plot_surface(x, y, z, alpha=0.6, color='orange',
                       label='动态障碍物' if i == 0 else "")
    
    # 设置坐标轴
    ax.set_xlim(0, env.bounds[0])
    ax.set_ylim(0, env.bounds[1])
    ax.set_zlim(0, env.bounds[2])
    ax.set_xlabel('X (m)', fontsize=12)
    ax.set_ylabel('Y (m)', fontsize=12)
    ax.set_zlabel('Z (m)', fontsize=12)
    ax.grid(True, alpha=0.3)
    
    # 设置等比例坐标轴
    try:
        ax.set_box_aspect([env.bounds[0], env.bounds[1], env.bounds[2]])
        print("✅ 等比例坐标轴设置成功")
    except:
        print("⚠️  等比例坐标轴设置失败")
    
    ax.set_title('修复后的环境球体显示', fontsize=14, fontweight='bold')
    ax.legend()
    
    plt.tight_layout()
    plt.savefig('environment_spheres_fixed.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("📊 环境球体显示图已保存: environment_spheres_fixed.png")

def analyze_sphere_formula():
    """分析球体公式的正确性"""
    print(f"\n🔍 球体公式分析")
    print("=" * 40)
    
    print("球体参数化公式:")
    print("  x = x₀ + r * cos(u) * sin(v)")
    print("  y = y₀ + r * sin(u) * sin(v)")
    print("  z = z₀ + r * cos(v)")
    print("  其中: u ∈ [0, 2π] (方位角), v ∈ [0, π] (极角)")
    print()
    
    # 测试不同半径的球体
    center = np.array([0, 0, 0])
    radii = [10, 20, 30]
    
    fig = plt.figure(figsize=(15, 5))
    
    for i, radius in enumerate(radii):
        ax = fig.add_subplot(1, 3, i+1, projection='3d')
        
        # 生成球体
        u = np.linspace(0, 2 * np.pi, 30)
        v = np.linspace(0, np.pi, 30)
        
        x = center[0] + radius * np.outer(np.cos(u), np.sin(v))
        y = center[1] + radius * np.outer(np.sin(u), np.sin(v))
        z = center[2] + radius * np.outer(np.ones(np.size(u)), np.cos(v))
        
        ax.plot_surface(x, y, z, alpha=0.7, color='lightblue')
        
        # 设置相同的坐标轴范围
        max_range = 35
        ax.set_xlim(-max_range, max_range)
        ax.set_ylim(-max_range, max_range)
        ax.set_zlim(-max_range, max_range)
        
        # 设置等比例
        try:
            ax.set_box_aspect([1, 1, 1])
        except:
            pass
        
        ax.set_title(f'半径 {radius}m 的球体')
        ax.set_xlabel('X (m)')
        ax.set_ylabel('Y (m)')
        ax.set_zlabel('Z (m)')
    
    plt.tight_layout()
    plt.savefig('sphere_formula_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("📊 球体公式分析图已保存: sphere_formula_analysis.png")
    print("✅ 球体公式验证完成")

def main():
    """主函数"""
    print("🎨 球体可视化修复验证")
    print("=" * 60)
    
    try:
        # 1. 球体渲染对比测试
        test_sphere_rendering()
        
        # 2. 环境球体显示测试
        test_environment_spheres()
        
        # 3. 球体公式分析
        analyze_sphere_formula()
        
        print(f"\n🎉 球体可视化测试完成!")
        print(f"📁 生成的文件:")
        print(f"  - sphere_visualization_test.png: 渲染方法对比")
        print(f"  - environment_spheres_fixed.png: 修复后的环境显示")
        print(f"  - sphere_formula_analysis.png: 球体公式验证")
        
        print(f"\n🔧 修复内容:")
        print(f"  ✅ 增加球体分辨率: 10x10 -> 20x20 或 25x25")
        print(f"  ✅ 设置等比例坐标轴: set_box_aspect()")
        print(f"  ✅ 优化球体参数化公式")
        print(f"  ✅ 提高图片质量: 300 DPI")
        
    except Exception as e:
        print(f"❌ 球体可视化测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
