"""
预训练动作生成器
Pretrained Action Generator

核心创新：神经网络学习生成比DWA更好的候选动作
- 预训练阶段：从专家数据学习动作生成模式
- 推理阶段：直接生成高质量候选动作，比DWA更快更好
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Dict, Tuple
import pickle
import os

class ActionGeneratorNet(nn.Module):
    """
    动作生成神经网络
    
    输入：状态 + 目标 + 障碍物编码
    输出：多个候选动作的参数分布
    """
    
    def __init__(self, state_dim=6, action_dim=3, num_actions=20, hidden_dim=256):
        super().__init__()
        
        self.num_actions = num_actions
        self.action_dim = action_dim
        
        # 状态编码器
        self.state_encoder = nn.Sequential(
            nn.Linear(state_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim // 2)
        )
        
        # 目标编码器
        self.goal_encoder = nn.Sequential(
            nn.Linear(3, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, hidden_dim // 4)
        )
        
        # 障碍物编码器（处理可变数量障碍物）
        self.obstacle_encoder = nn.Sequential(
            nn.Linear(7, 32),  # [x,y,z,r,vx,vy,vz]
            nn.ReLU(),
            nn.Linear(32, 16)
        )
        
        # 注意力机制聚合障碍物
        self.attention = nn.MultiheadAttention(16, num_heads=4, batch_first=True)
        
        # 融合层
        fusion_dim = hidden_dim // 2 + hidden_dim // 4 + 16
        self.fusion = nn.Sequential(
            nn.Linear(fusion_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim)
        )
        
        # 动作生成头：生成多个动作的均值和方差
        self.action_means = nn.Linear(hidden_dim, num_actions * action_dim)
        self.action_stds = nn.Linear(hidden_dim, num_actions * action_dim)
        
        # 动作质量评估头
        self.quality_head = nn.Linear(hidden_dim, num_actions)
        
    def encode_obstacles(self, obstacles: List[Dict]) -> torch.Tensor:
        """编码障碍物信息"""
        device = next(self.parameters()).device
        
        if not obstacles:
            return torch.zeros(1, 16, device=device)
        
        # 编码每个障碍物
        obs_features = []
        for obs in obstacles[:10]:  # 最多10个障碍物
            if isinstance(obs, dict):
                center = obs.get('center', [0, 0, 0])
                radius = obs.get('radius', 1.0)
                velocity = obs.get('velocity', [0, 0, 0])
            else:
                center, radius, velocity = [0, 0, 0], 1.0, [0, 0, 0]
            
            obs_vec = torch.tensor([
                center[0], center[1], center[2], radius,
                velocity[0], velocity[1], velocity[2]
            ], dtype=torch.float32, device=device)
            obs_features.append(obs_vec)
        
        # 填充到固定长度
        while len(obs_features) < 10:
            obs_features.append(torch.zeros(7, device=device))
        
        obs_tensor = torch.stack(obs_features).unsqueeze(0)  # [1, 10, 7]
        
        # 编码
        encoded_obs = self.obstacle_encoder(obs_tensor)  # [1, 10, 16]
        
        # 注意力聚合
        attended_obs, _ = self.attention(encoded_obs, encoded_obs, encoded_obs)
        
        # 全局池化
        obs_encoding = attended_obs.mean(dim=1)  # [1, 16]
        
        return obs_encoding
    
    def forward(self, state: torch.Tensor, goal: torch.Tensor, 
                obstacles: List[Dict]) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Returns:
            action_means: [batch, num_actions, action_dim]
            action_stds: [batch, num_actions, action_dim]
            quality_scores: [batch, num_actions]
        """
        batch_size = state.shape[0]
        
        # 编码各个组件
        state_feat = self.state_encoder(state)  # [batch, hidden//2]
        goal_feat = self.goal_encoder(goal)     # [batch, hidden//4]
        
        # 处理障碍物（假设batch中所有样本使用相同障碍物）
        obs_feat = self.encode_obstacles(obstacles)  # [1, 16]
        obs_feat = obs_feat.expand(batch_size, -1)   # [batch, 16]
        
        # 融合特征
        combined_feat = torch.cat([state_feat, goal_feat, obs_feat], dim=1)
        fused_feat = self.fusion(combined_feat)  # [batch, hidden]
        
        # 生成动作参数
        action_means = self.action_means(fused_feat)  # [batch, num_actions * action_dim]
        action_stds = self.action_stds(fused_feat)    # [batch, num_actions * action_dim]
        
        # 重塑为正确形状
        action_means = action_means.view(batch_size, self.num_actions, self.action_dim)
        action_stds = F.softplus(action_stds.view(batch_size, self.num_actions, self.action_dim)) + 1e-6
        
        # 质量评分
        quality_scores = torch.sigmoid(self.quality_head(fused_feat))  # [batch, num_actions]
        
        return {
            'action_means': action_means,
            'action_stds': action_stds,
            'quality_scores': quality_scores
        }
    
    def sample_actions(self, state: torch.Tensor, goal: torch.Tensor, 
                      obstacles: List[Dict], deterministic: bool = False) -> torch.Tensor:
        """采样动作"""
        with torch.no_grad():
            outputs = self.forward(state, goal, obstacles)
            
            if deterministic:
                # 确定性：返回均值
                actions = outputs['action_means']
            else:
                # 随机采样
                means = outputs['action_means']
                stds = outputs['action_stds']
                
                # 从正态分布采样
                noise = torch.randn_like(means)
                actions = means + noise * stds
            
            # 限制动作范围
            actions = torch.clamp(actions, -3.0, 3.0)
            
            return actions, outputs['quality_scores']

class PretrainedActionGenerator:
    """
    预训练动作生成器
    
    核心创新：
    1. 预训练阶段：从专家数据学习比DWA更好的动作生成策略
    2. 推理阶段：直接生成高质量候选动作，无需暴力搜索
    """
    
    def __init__(self, device='cpu'):
        self.device = device
        self.net = ActionGeneratorNet().to(device)
        self.optimizer = torch.optim.Adam(self.net.parameters(), lr=0.001)
        
        # 训练状态
        self.is_pretrained = False
        self.training_history = []
        
    def generate_expert_data(self, num_episodes=200) -> List[Dict]:
        """
        生成专家数据用于预训练
        使用改进的启发式策略作为"专家"
        """
        print("🎯 生成专家数据...")
        
        expert_data = []
        
        for episode in range(num_episodes):
            # 随机初始化环境
            state = np.random.uniform([50, 50, 20, -5, -5, -2], [950, 950, 80, 5, 5, 2])
            goal = np.random.uniform([100, 100, 20], [900, 900, 80])
            
            # 随机生成障碍物
            num_obs = np.random.randint(2, 6)
            obstacles = []
            for _ in range(num_obs):
                obs_center = np.random.uniform([100, 100, 20], [900, 900, 80])
                obs_radius = np.random.uniform(15, 35)
                obs_velocity = np.random.uniform([-2, -2, -1], [2, 2, 1])
                obstacles.append({
                    'center': obs_center,
                    'radius': obs_radius,
                    'velocity': obs_velocity
                })
            
            # 生成专家动作（改进的启发式）
            expert_actions = self._generate_expert_actions(state, goal, obstacles)
            
            expert_data.append({
                'state': state[:6],  # 只取前6维
                'goal': goal,
                'obstacles': obstacles,
                'expert_actions': expert_actions
            })
            
            if episode % 50 == 0:
                print(f"  生成进度: {episode}/{num_episodes}")
        
        print(f"✅ 生成了 {len(expert_data)} 条专家数据")
        return expert_data
    
    def _generate_expert_actions(self, state: np.ndarray, goal: np.ndarray, 
                                obstacles: List[Dict], num_actions: int = 20) -> List[np.ndarray]:
        """
        生成专家动作 - 使用改进的启发式策略
        这些动作质量应该比简单的DWA更好
        """
        expert_actions = []
        
        pos = state[:3]
        vel = state[3:6]
        
        # 计算目标方向
        to_goal = goal - pos
        goal_dist = np.linalg.norm(to_goal)
        goal_direction = to_goal / (goal_dist + 1e-6)
        
        # 计算障碍物排斥力
        total_repulsion = np.zeros(3)
        for obs in obstacles:
            to_obs = obs['center'] - pos
            obs_dist = np.linalg.norm(to_obs)
            
            if obs_dist < obs['radius'] + 100:  # 在影响范围内
                repulsion_strength = (obs['radius'] + 50) / (obs_dist + 1e-6)
                repulsion_direction = -to_obs / (obs_dist + 1e-6)
                total_repulsion += repulsion_direction * repulsion_strength
        
        # 生成多样化的专家动作
        for i in range(num_actions):
            if i < num_actions // 2:
                # 前一半：主要朝向目标
                base_action = goal_direction * np.random.uniform(1.5, 3.0)
                
                # 加入避障成分
                if np.linalg.norm(total_repulsion) > 0.1:
                    repulsion_weight = min(0.5, np.linalg.norm(total_repulsion) / 2.0)
                    base_action = (1 - repulsion_weight) * base_action + repulsion_weight * total_repulsion
                
            else:
                # 后一半：更多探索性动作
                if np.linalg.norm(total_repulsion) > 0.1:
                    # 主要避障
                    base_action = total_repulsion * np.random.uniform(0.8, 2.0)
                else:
                    # 随机探索
                    base_action = np.random.uniform(-2, 2, 3)
            
            # 添加噪声增加多样性
            noise = np.random.normal(0, 0.4, 3)
            action = base_action + noise
            
            # 限制动作范围
            action = np.clip(action, -3.0, 3.0)
            expert_actions.append(action)
        
        return expert_actions
    
    def pretrain(self, expert_data: List[Dict], num_epochs: int = 100) -> List[float]:
        """
        预训练网络
        学习从状态+目标+障碍物生成高质量动作
        """
        print(f"🧠 开始预训练，数据量: {len(expert_data)}, epochs: {num_epochs}")
        
        losses = []
        
        for epoch in range(num_epochs):
            epoch_losses = []
            
            # 随机打乱数据
            np.random.shuffle(expert_data)
            
            batch_size = 32
            for i in range(0, len(expert_data), batch_size):
                batch = expert_data[i:i+batch_size]
                
                # 准备批次数据
                states = torch.tensor([d['state'] for d in batch], dtype=torch.float32, device=self.device)
                goals = torch.tensor([d['goal'] for d in batch], dtype=torch.float32, device=self.device)
                
                # 处理专家动作
                expert_actions_batch = []
                for d in batch:
                    # 取前20个专家动作
                    actions = d['expert_actions'][:20]
                    while len(actions) < 20:
                        actions.append(np.zeros(3))  # 填充
                    expert_actions_batch.append(actions)
                
                expert_actions = torch.tensor(expert_actions_batch, dtype=torch.float32, device=self.device)
                
                # 前向传播
                obstacles = batch[0]['obstacles']  # 简化：使用第一个样本的障碍物
                outputs = self.net(states, goals, obstacles)
                
                # 计算损失
                # 1. 动作预测损失
                predicted_means = outputs['action_means']
                action_loss = F.mse_loss(predicted_means, expert_actions)
                
                # 2. 质量评分损失（专家动作应该有高质量评分）
                target_quality = torch.ones(len(batch), 20, device=self.device) * 0.8
                quality_loss = F.mse_loss(outputs['quality_scores'], target_quality)
                
                # 总损失
                total_loss = action_loss + 0.1 * quality_loss
                
                # 反向传播
                self.optimizer.zero_grad()
                total_loss.backward()
                self.optimizer.step()
                
                epoch_losses.append(total_loss.item())
            
            avg_loss = np.mean(epoch_losses)
            losses.append(avg_loss)
            
            if epoch % 20 == 0:
                print(f"  Epoch {epoch}: Loss = {avg_loss:.4f}")
        
        self.is_pretrained = True
        self.training_history = losses
        
        print("✅ 预训练完成!")
        return losses
    
    def generate_candidate_actions(self, state: np.ndarray, goal: np.ndarray, 
                                 obstacles: List[Dict], num_candidates: int = 50) -> List[np.ndarray]:
        """
        生成候选动作 - 预训练后的智能生成
        这是替代DWA暴力搜索的核心功能
        """
        if not self.is_pretrained:
            print("警告：网络未预训练，使用随机动作")
            return [np.random.uniform(-2, 2, 3) for _ in range(num_candidates)]
        
        self.net.eval()
        
        with torch.no_grad():
            # 准备输入
            state_tensor = torch.tensor(state[:6], dtype=torch.float32, device=self.device).unsqueeze(0)
            goal_tensor = torch.tensor(goal, dtype=torch.float32, device=self.device).unsqueeze(0)
            
            # 生成动作
            actions, quality_scores = self.net.sample_actions(
                state_tensor, goal_tensor, obstacles, deterministic=False
            )
            
            # 转换为numpy并按质量排序
            actions_np = actions[0].cpu().numpy()  # [num_actions, action_dim]
            quality_np = quality_scores[0].cpu().numpy()  # [num_actions]
            
            # 按质量评分排序
            sorted_indices = np.argsort(quality_np)[::-1]
            
            # 返回前num_candidates个动作
            candidate_actions = []
            for i in sorted_indices[:num_candidates]:
                candidate_actions.append(actions_np[i])
            
            # 如果需要更多候选，添加一些随机扰动
            while len(candidate_actions) < num_candidates:
                # 在最好的动作基础上添加噪声
                best_action = actions_np[sorted_indices[0]]
                noise = np.random.normal(0, 0.5, 3)
                noisy_action = best_action + noise
                noisy_action = np.clip(noisy_action, -3.0, 3.0)
                candidate_actions.append(noisy_action)
        
        return candidate_actions
    
    def save_model(self, filepath: str):
        """保存预训练模型"""
        torch.save({
            'model_state_dict': self.net.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'is_pretrained': self.is_pretrained,
            'training_history': self.training_history
        }, filepath)
        print(f"💾 模型已保存到: {filepath}")
    
    def load_model(self, filepath: str):
        """加载预训练模型"""
        if os.path.exists(filepath):
            try:
                checkpoint = torch.load(filepath, map_location=self.device, weights_only=False)
                self.net.load_state_dict(checkpoint['model_state_dict'])
                self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
                self.is_pretrained = checkpoint.get('is_pretrained', False)
                self.training_history = checkpoint.get('training_history', [])
                print(f"📂 模型已从 {filepath} 加载")
                return True
            except Exception as e:
                print(f"❌ 加载模型失败: {e}")
                return False
        else:
            print(f"❌ 模型文件 {filepath} 不存在")
            return False
