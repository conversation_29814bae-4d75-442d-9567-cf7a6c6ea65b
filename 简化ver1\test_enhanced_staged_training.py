"""
测试增强的分阶段训练系统
验证是否生成了与train_simplified_reward.py相同的输出
"""

import os
import sys
from datetime import datetime

def test_enhanced_staged_training():
    """测试增强的分阶段训练系统"""
    print("🧪 测试增强的分阶段训练系统")
    print("=" * 60)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        from staged_training import StagedTrainer
        from environment_config import TRAINING_STAGES
        
        # 创建训练器（只训练第一阶段，少量episodes用于测试）
        trainer = StagedTrainer(
            start_stage=1,
            end_stage=1,  # 只训练第一阶段
            seed=42,
            visualization_interval=3  # 每3个episodes生成一次图
        )
        
        # 修改训练配置为更少的episodes
        original_config = TRAINING_STAGES["stage1_simple"].copy()
        TRAINING_STAGES["stage1_simple"]["random_episodes"] = 6  # 随机探索
        TRAINING_STAGES["stage1_simple"]["fixed_episodes"] = 4   # 固定强化
        TRAINING_STAGES["stage1_simple"]["total_episodes"] = 10
        
        print("📋 测试配置:")
        print(f"  • 训练阶段: 1 (简单环境)")
        print(f"  • 随机场景探索: 6 episodes")
        print(f"  • 固定场景强化: 4 episodes")
        print(f"  • 总计: 10 episodes")
        print(f"  • 3D轨迹图间隔: 每3个episodes")
        print()
        
        # 运行训练
        print("🚀 开始测试训练...")
        results = trainer.run_staged_training()
        
        # 恢复原始配置
        TRAINING_STAGES["stage1_simple"] = original_config
        
        if results:
            print("\n✅ 增强分阶段训练测试成功!")
            
            # 检查生成的文件
            output_dir = trainer.full_output_dir
            print(f"\n📁 检查输出目录: {output_dir}")
            
            expected_files = [
                'stage_1_model.pth',
                'stage_1_training_data.pkl',
                'stage_1_training_report.json',
                'stage_1_training_rewards.csv',
                'stage_1_phase_1_summary.png',
                'stage_1_phase_2_summary.png',
                'stage_1_comparison.png'
            ]
            
            missing_files = []
            existing_files = []
            
            for filename in expected_files:
                filepath = os.path.join(output_dir, filename)
                if os.path.exists(filepath):
                    existing_files.append(filename)
                    file_size = os.path.getsize(filepath)
                    print(f"  ✅ {filename} ({file_size} bytes)")
                else:
                    missing_files.append(filename)
                    print(f"  ❌ {filename} (缺失)")
            
            # 检查3D轨迹图
            trajectory_files = [f for f in os.listdir(output_dir) if f.endswith('_3d_trajectory.png')]
            print(f"  📊 3D轨迹图: {len(trajectory_files)} 个")
            for traj_file in trajectory_files:
                print(f"    • {traj_file}")
            
            print(f"\n📊 文件生成统计:")
            print(f"  • 预期文件: {len(expected_files)}")
            print(f"  • 成功生成: {len(existing_files)}")
            print(f"  • 缺失文件: {len(missing_files)}")
            print(f"  • 3D轨迹图: {len(trajectory_files)}")
            
            if len(missing_files) == 0:
                print("\n🎉 所有预期文件都已生成！")
                print("✅ 增强分阶段训练系统与原始train_simplified_reward.py输出格式一致")
                return True
            else:
                print(f"\n⚠️ 有 {len(missing_files)} 个文件缺失")
                return False
        else:
            print("\n❌ 增强分阶段训练测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = test_enhanced_staged_training()
    
    if success:
        print("\n🎉 测试完成！增强分阶段训练系统工作正常")
        print("\n💡 现在可以使用以下命令进行完整训练:")
        print("   python staged_training.py --start-stage 1 --end-stage 3")
        print("   或者")
        print("   python run_staged_training.py")
    else:
        print("\n🔧 请检查并修复测试中发现的问题")
    
    return success

if __name__ == "__main__":
    main()
