"""
简单测试脚本 - 验证基本功能
"""

import numpy as np
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from loitering_munition_environment import LoiteringMunitionEnvironment
from loitering_munition_dwa import LoiteringMunitionDWA
from environment_config import get_environment_config, get_loitering_munition_config

def test_basic_functionality():
    """测试基本功能"""
    print("🧪 基本功能测试")
    print("=" * 30)
    
    try:
        # 创建环境
        env_config = get_environment_config("stage1_simple")
        lm_config = get_loitering_munition_config()
        
        env = LoiteringMunitionEnvironment(
            bounds=lm_config['bounds'],
            environment_config=env_config,
            reward_type='simplified'
        )
        
        # 重置环境
        state = env.reset()
        print(f"✅ 环境创建成功")
        print(f"   状态维度: {state.shape}")
        print(f"   起点: {env.start}")
        print(f"   目标: {env.goal}")
        print(f"   障碍物数量: {len(env.obstacles)}")
        
        # 创建DWA控制器
        dwa = LoiteringMunitionDWA(dt=lm_config['dt'])
        print(f"✅ DWA控制器创建成功")
        
        # 测试一步控制
        print(f"\n🎯 测试DWA控制...")
        print(f"   当前位置: {env.state[:3]}")
        print(f"   目标位置: {env.goal}")
        print(f"   距离: {np.linalg.norm(env.state[:3] - env.goal):.2f}m")
        
        # 生成安全控制
        safe_controls = dwa.generate_safe_control_set(
            env.state, env.obstacles + env.dynamic_obstacles, env.goal, max_actions=5
        )
        
        if safe_controls:
            print(f"✅ 生成了 {len(safe_controls)} 个安全控制")
            
            # 选择最优控制
            best_control = dwa.select_best_control(
                env.state, env.obstacles + env.dynamic_obstacles, env.goal
            )
            print(f"✅ 最优控制: {best_control}")
            
            # 执行控制
            next_state, reward, done, info = env.step(best_control)
            print(f"✅ 执行控制成功")
            print(f"   新位置: {env.state[:3]}")
            print(f"   奖励: {reward:.3f}")
            print(f"   新距离: {np.linalg.norm(env.state[:3] - env.goal):.2f}m")
            
            if done:
                if info.get('success', False):
                    print(f"🏆 任务成功!")
                elif info.get('collision', False):
                    print(f"💥 发生碰撞")
                else:
                    print(f"🚫 其他终止原因")
        else:
            print(f"⚠️  没有找到安全控制")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_steps():
    """测试多步控制"""
    print(f"\n🧪 多步控制测试")
    print("=" * 30)
    
    try:
        # 创建环境
        env_config = get_environment_config("test_simple")
        lm_config = get_loitering_munition_config()
        
        env = LoiteringMunitionEnvironment(
            bounds=[1000, 1000, 100],  # 较小的环境
            environment_config=env_config,
            reward_type='simplified'
        )
        
        # 设置简单场景
        env.start = np.array([100, 100, 50])
        env.goal = np.array([800, 800, 50])
        env.obstacles = []  # 无障碍物
        env.dynamic_obstacles = []
        
        state = env.reset()
        dwa = LoiteringMunitionDWA(dt=lm_config['dt'])
        
        print(f"起点: {env.start}")
        print(f"目标: {env.goal}")
        print(f"初始距离: {np.linalg.norm(env.start - env.goal):.2f}m")
        
        # 运行多步
        total_reward = 0
        for step in range(20):
            # 选择控制
            best_control = dwa.select_best_control(
                env.state, env.obstacles + env.dynamic_obstacles, env.goal
            )
            
            # 执行控制
            next_state, reward, done, info = env.step(best_control)
            total_reward += reward
            
            current_distance = np.linalg.norm(env.state[:3] - env.goal)
            
            if step % 5 == 0:
                print(f"步骤 {step:2d}: 距离 {current_distance:6.2f}m, 奖励 {reward:6.2f}")
            
            if done:
                print(f"\n在第 {step+1} 步完成")
                if info.get('success', False):
                    print(f"🏆 成功到达目标!")
                break
        
        print(f"总奖励: {total_reward:.2f}")
        print(f"最终距离: {np.linalg.norm(env.state[:3] - env.goal):.2f}m")
        
        return True
        
    except Exception as e:
        print(f"❌ 多步测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 简单测试开始")
    print("=" * 50)
    
    tests = [
        ("基本功能", test_basic_functionality),
        ("多步控制", test_multiple_steps)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示结果
    print(f"\n{'='*20} 测试结果 {'='*20}")
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 测试统计: {passed}/{len(results)} 通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！")
        return True
    else:
        print("⚠️  部分测试失败")
        return False

if __name__ == "__main__":
    main()
