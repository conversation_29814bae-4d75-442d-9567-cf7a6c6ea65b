"""
快速测试脚本
"""

try:
    print("Testing imports...")
    from dwa_rl_core import StabilizedRewardEnvironment
    from environment_config import get_environment_config
    print("✅ Imports successful")
    
    print("Testing environment config...")
    env_config = get_environment_config('stage1_simple')
    print("✅ Environment config loaded")
    
    print("Testing environment creation...")
    env = StabilizedRewardEnvironment(environment_config=env_config)
    print("✅ Environment created")
    
    print("Testing reset...")
    state = env.reset()
    print(f"✅ Reset successful, state shape: {state.shape}")
    
    print("Testing scenario save...")
    scenario = env.save_scenario()
    print(f"✅ Scenario saved, obstacles: {len(scenario['obstacles'])}")
    
    print("Testing fixed scenario reset...")
    state2 = env.reset(scenario)
    print("✅ Fixed scenario reset successful")
    
    print("All tests passed!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
