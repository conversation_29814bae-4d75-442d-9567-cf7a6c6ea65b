"""
巡飞弹固定场景配置 - 完整集成版本
三阶段分阶段训练场景配置
"""

import numpy as np

class LoiteringMunitionScenarioConfig:
    """巡飞弹固定场景配置管理器"""
    
    def __init__(self, seed=42):
        self.seed = seed
        np.random.seed(seed)
        
        # 巡飞弹任务场景：2km x 2km x 200m 作战空间
        self.start = np.array([100.0, 100.0, 50.0], dtype=np.float64)  # 发射位置
        self.goal = np.array([1800.0, 1800.0, 120.0], dtype=np.float64)  # 目标位置
        
        # 巡飞弹初始状态
        self.initial_velocity = 25.0      # 初始速度 25 m/s
        self.initial_gamma = 0.0          # 初始航迹倾斜角 0°
        self.initial_psi = np.pi/4        # 初始偏航角 45°（东北方向）
        
        # 环境边界
        self.environment_bounds = [2000, 2000, 200]
        
        # 生成各阶段场景
        self.stage1_obstacles = self._generate_stage1_obstacles()
        self.stage2_obstacles = self._generate_stage2_obstacles()
        self.stage3_static_obstacles = self.stage2_obstacles  # 阶段3继承阶段2的静态障碍物
        self.stage3_dynamic_obstacles = self._generate_stage3_dynamic_obstacles()
    
    def _generate_stage1_obstacles(self):
        """阶段1：5个固定静态威胁"""
        obstacles = [
            # 高层建筑群
            {'center': np.array([600.0, 400.0, 80.0], dtype=np.float64), 'radius': 50.0, 'type': 'building'},
            # 山峰/丘陵
            {'center': np.array([900.0, 600.0, 150.0], dtype=np.float64), 'radius': 80.0, 'type': 'terrain'},
            # 通信塔
            {'center': np.array([1200.0, 800.0, 120.0], dtype=np.float64), 'radius': 30.0, 'type': 'tower'},
            # 工业设施
            {'center': np.array([1400.0, 1200.0, 90.0], dtype=np.float64), 'radius': 60.0, 'type': 'facility'},
            # 桥梁结构
            {'center': np.array([1600.0, 1400.0, 100.0], dtype=np.float64), 'radius': 40.0, 'type': 'bridge'}
        ]
        return obstacles
    
    def _generate_stage2_obstacles(self):
        """阶段2：继承阶段1 + 新增10个威胁"""
        obstacles = self.stage1_obstacles.copy()
        
        # 新增10个威胁
        additional_obstacles = [
            # 城市建筑群
            {'center': np.array([500.0, 900.0, 70.0], dtype=np.float64), 'radius': 35.0, 'type': 'building'},
            {'center': np.array([800.0, 1100.0, 110.0], dtype=np.float64), 'radius': 45.0, 'type': 'building'},
            {'center': np.array([1100.0, 500.0, 85.0], dtype=np.float64), 'radius': 40.0, 'type': 'building'},
            
            # 地形障碍
            {'center': np.array([300.0, 1200.0, 130.0], dtype=np.float64), 'radius': 70.0, 'type': 'terrain'},
            {'center': np.array([1700.0, 600.0, 140.0], dtype=np.float64), 'radius': 60.0, 'type': 'terrain'},
            {'center': np.array([1000.0, 300.0, 95.0], dtype=np.float64), 'radius': 50.0, 'type': 'terrain'},
            {'center': np.array([1500.0, 1700.0, 160.0], dtype=np.float64), 'radius': 80.0, 'type': 'terrain'},
            
            # 军事设施
            {'center': np.array([700.0, 1500.0, 75.0], dtype=np.float64), 'radius': 55.0, 'type': 'military'},
            {'center': np.array([1300.0, 900.0, 105.0], dtype=np.float64), 'radius': 45.0, 'type': 'facility'},
            {'center': np.array([400.0, 700.0, 90.0], dtype=np.float64), 'radius': 35.0, 'type': 'infrastructure'}
        ]
        
        obstacles.extend(additional_obstacles)
        return obstacles
    
    def _generate_stage3_dynamic_obstacles(self):
        """阶段3：3个固定动态威胁"""
        dynamic_obstacles = [
            # 动态威胁1：敌方巡逻飞机（线性巡逻）
            {
                'center': np.array([800.0, 1000.0, 100.0], dtype=np.float64),
                'radius': 25.0,
                'motion_type': 'linear',
                'motion_params': {
                    'velocity': np.array([15.0, 0.0, 2.0], dtype=np.float64),  # 15 m/s 巡逻速度
                    'bounds': {'x': [600, 1400], 'y': [800, 1200], 'z': [80, 120]}
                },
                'time': 0.0,
                'type': 'aircraft'
            },
            
            # 动态威胁2：防空导弹系统（圆周扫描）
            {
                'center': np.array([1200.0, 1200.0, 80.0], dtype=np.float64),
                'radius': 20.0,
                'motion_type': 'circular',
                'motion_params': {
                    'center_orbit': np.array([1200.0, 1200.0, 80.0], dtype=np.float64),
                    'radius_orbit': 100.0,  # 100m 扫描半径
                    'angular_speed': 0.02,   # 较慢的扫描速度
                    'phase': 0.0
                },
                'time': 0.0,
                'type': 'sam_system'
            },
            
            # 动态威胁3：移动雷达车（振荡巡逻）
            {
                'center': np.array([1500.0, 800.0, 60.0], dtype=np.float64),
                'radius': 30.0,
                'motion_type': 'oscillating',
                'motion_params': {
                    'center_base': np.array([1500.0, 800.0, 60.0], dtype=np.float64),
                    'amplitude': np.array([200.0, 150.0, 20.0], dtype=np.float64),  # 更大的巡逻范围
                    'frequency': np.array([0.01, 0.008, 0.015], dtype=np.float64),  # 较慢的移动频率
                    'phase': np.array([0.0, np.pi/3, np.pi/2], dtype=np.float64)
                },
                'time': 0.0,
                'type': 'radar_vehicle'
            }
        ]
        return dynamic_obstacles
    
    def get_scenario_config(self, stage):
        """获取指定阶段的场景配置"""
        base_config = {
            'start': self.start,
            'goal': self.goal,
            'initial_velocity': self.initial_velocity,
            'initial_gamma': self.initial_gamma,
            'initial_psi': self.initial_psi,
            'environment_bounds': self.environment_bounds
        }
        
        if stage == 'stage1':
            return {
                **base_config,
                'static_obstacles': self.stage1_obstacles,
                'dynamic_obstacles': [],
                'description': f"阶段1：巡飞弹基础作战场景 - {len(self.stage1_obstacles)}个固定静态威胁"
            }
        elif stage == 'stage2':
            return {
                **base_config,
                'static_obstacles': self.stage2_obstacles,
                'dynamic_obstacles': [],
                'description': f"阶段2：复杂巡飞弹作战场景 - {len(self.stage2_obstacles)}个固定静态威胁"
            }
        elif stage == 'stage3':
            return {
                **base_config,
                'static_obstacles': self.stage3_static_obstacles,
                'dynamic_obstacles': self.stage3_dynamic_obstacles,
                'description': f"阶段3：动态威胁巡飞弹作战场景 - {len(self.stage3_static_obstacles)}个静态 + {len(self.stage3_dynamic_obstacles)}个动态威胁"
            }
        else:
            raise ValueError(f"未知的阶段: {stage}")

# 全局配置实例
SCENARIO_CONFIG = LoiteringMunitionScenarioConfig()

def get_scenario_config(stage):
    """获取场景配置的便捷函数"""
    return SCENARIO_CONFIG.get_scenario_config(stage)
