#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成剩余的训练总结图表
"""

import pickle
import json
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 设置为非交互模式
import matplotlib.pyplot as plt
from datetime import datetime
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_training_data():
    """加载训练数据"""
    data_dir = "enhanced_training_20250718_022622"
    
    # 加载训练数据
    with open(os.path.join(data_dir, "simplified_reward_training_data.pkl"), 'rb') as f:
        training_data = pickle.load(f)
    
    # 加载训练报告
    with open(os.path.join(data_dir, "simplified_reward_training_report.json"), 'r', encoding='utf-8') as f:
        report = json.load(f)
    
    return training_data, report

def generate_phase_summary_plot(training_data, final_stats, phase=2):
    """生成单个阶段的训练总结图表"""
    try:
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 确定阶段数据范围
        random_episodes = 200
        if phase == 1:
            start_idx, end_idx = 0, random_episodes
            phase_name = "第一阶段(随机场景)"
            episodes_range = f"Episodes 1-{random_episodes}"
        else:
            start_idx, end_idx = random_episodes, len(training_data['episode_rewards'])
            phase_name = "第二阶段(固定场景)"
            episodes_range = f"Episodes {random_episodes+1}-{len(training_data['episode_rewards'])}"
        
        # 提取阶段数据
        phase_rewards = training_data['episode_rewards'][start_idx:end_idx]
        phase_steps = training_data['episode_steps'][start_idx:end_idx]
        phase_episodes = list(range(start_idx, end_idx))
        
        # 阶段成功episodes
        phase_success_episodes = [ep for ep in training_data['success_episodes'] if start_idx <= ep < end_idx]
        phase_collision_episodes = [ep for ep in training_data['collision_episodes'] if start_idx <= ep < end_idx]
        phase_timeout_episodes = [ep for ep in training_data['timeout_episodes'] if start_idx <= ep < end_idx]
        
        main_title = f'增强训练总结 - {phase_name}\n{episodes_range} ({len(phase_rewards)} Episodes)'
        fig.suptitle(main_title, fontsize=16, fontweight='bold')
        
        # 1. Episode奖励趋势
        axes[0, 0].plot(phase_episodes, phase_rewards, 'b-o', alpha=0.7, markersize=3)
        
        # 添加趋势线
        if len(phase_rewards) > 1:
            z = np.polyfit(range(len(phase_rewards)), phase_rewards, 1)
            p = np.poly1d(z)
            axes[0, 0].plot(phase_episodes, p(range(len(phase_rewards))), 
                           "r--", alpha=0.8, label=f'趋势: {z[0]:.2f}x+{z[1]:.2f}')
        
        axes[0, 0].axhline(np.mean(phase_rewards), color='green', linestyle='--',
                          label=f'平均: {np.mean(phase_rewards):.1f}')
        
        axes[0, 0].set_title('Episode奖励趋势')
        axes[0, 0].set_xlabel('Episode')
        axes[0, 0].set_ylabel('奖励')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 成功率进展
        success_rates = []
        for i in range(len(phase_rewards)):
            success_count = len([ep for ep in phase_success_episodes if ep <= start_idx + i])
            success_rates.append(success_count / (i + 1))

        phase_success_rate = len(phase_success_episodes) / len(phase_rewards) if len(phase_rewards) > 0 else 0
        
        axes[0, 1].plot(phase_episodes, success_rates, 'g-', linewidth=2)
        axes[0, 1].axhline(phase_success_rate, color='red', linestyle='--', 
                          label=f'阶段成功率: {phase_success_rate:.3f}')
        
        axes[0, 1].set_title('成功率进展')
        axes[0, 1].set_xlabel('Episode')
        axes[0, 1].set_ylabel('成功率')
        axes[0, 1].set_ylim(0, 1)
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. Episode步数分布
        axes[1, 0].hist(phase_steps, bins=20, alpha=0.7, color='orange', edgecolor='black')
        axes[1, 0].axvline(np.mean(phase_steps), color='red', linestyle='--', 
                          label=f'平均: {np.mean(phase_steps):.1f}')
        axes[1, 0].set_title('Episode步数分布')
        axes[1, 0].set_xlabel('步数')
        axes[1, 0].set_ylabel('频次')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. Episode结果分布
        success_count = len(phase_success_episodes)
        collision_count = len(phase_collision_episodes)
        timeout_count = len(phase_timeout_episodes)
        
        labels = ['成功', '碰撞', '超时']
        sizes = [success_count, collision_count, timeout_count]
        colors = ['green', 'red', 'orange']
        
        # 只显示非零的部分
        non_zero_sizes = []
        non_zero_labels = []
        non_zero_colors = []
        
        for i, size in enumerate(sizes):
            if size > 0:
                non_zero_sizes.append(size)
                non_zero_labels.append(labels[i])
                non_zero_colors.append(colors[i])
        
        if non_zero_sizes:
            wedges, texts, autotexts = axes[1, 1].pie(non_zero_sizes, labels=non_zero_labels, 
                                                     colors=non_zero_colors, autopct='%1.1f%%', startangle=90)
            # 设置百分比文字颜色为白色
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')
        
        axes[1, 1].set_title('Episode结果分布')
        
        plt.tight_layout()
        
        # 保存图片
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        phase_name_en = "phase1_random" if phase == 1 else "phase2_fixed"
        filename = f'training_summary_{phase_name_en}_{timestamp}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"📊 {phase_name}训练总结图已保存: {filename}")

        plt.close(fig)
        
    except Exception as e:
        print(f"⚠️ {phase_name}训练总结图生成失败: {e}")

def generate_comparison_plot(training_data, final_stats):
    """生成两个阶段的对比图"""
    try:
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        main_title = f'训练阶段对比 - 总计 300 Episodes'
        fig.suptitle(main_title, fontsize=16, fontweight='bold')
        
        # 分割数据
        random_episodes = 200
        phase1_rewards = training_data['episode_rewards'][:random_episodes]
        phase2_rewards = training_data['episode_rewards'][random_episodes:]
        phase1_episodes = list(range(0, random_episodes))
        phase2_episodes = list(range(random_episodes, len(training_data['episode_rewards'])))
        
        # 1. 奖励对比
        axes[0, 0].plot(phase1_episodes, phase1_rewards, 'b-', alpha=0.7, label='第一阶段(随机场景)')
        axes[0, 0].plot(phase2_episodes, phase2_rewards, 'r-', alpha=0.7, label='第二阶段(固定场景)')
        axes[0, 0].axvline(random_episodes, color='gray', linestyle='--', alpha=0.5, label='阶段分界')
        axes[0, 0].set_title('Episode奖励对比')
        axes[0, 0].set_xlabel('Episode')
        axes[0, 0].set_ylabel('奖励')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 成功率对比
        phase1_success = [ep for ep in training_data['success_episodes'] if ep < random_episodes]
        phase2_success = [ep for ep in training_data['success_episodes'] if ep >= random_episodes]
        
        phase1_success_rate = len(phase1_success) / random_episodes if random_episodes > 0 else 0
        phase2_success_rate = len(phase2_success) / (len(training_data['episode_rewards']) - random_episodes) if len(training_data['episode_rewards']) > random_episodes else 0
        
        phases = ['第一阶段\n(随机场景)', '第二阶段\n(固定场景)']
        success_rates = [phase1_success_rate, phase2_success_rate]
        
        bars = axes[0, 1].bar(phases, success_rates, color=['blue', 'red'], alpha=0.7)
        axes[0, 1].set_title('阶段成功率对比')
        axes[0, 1].set_ylabel('成功率')
        axes[0, 1].set_ylim(0, 1)
        
        # 添加数值标签
        for bar, rate in zip(bars, success_rates):
            height = bar.get_height()
            axes[0, 1].text(bar.get_x() + bar.get_width()/2., height + 0.01,
                           f'{rate:.3f}', ha='center', va='bottom')
        
        # 3. 平均奖励对比
        phase1_avg_reward = np.mean(phase1_rewards) if len(phase1_rewards) > 0 else 0
        phase2_avg_reward = np.mean(phase2_rewards) if len(phase2_rewards) > 0 else 0
        
        avg_rewards = [phase1_avg_reward, phase2_avg_reward]
        bars2 = axes[1, 0].bar(phases, avg_rewards, color=['blue', 'red'], alpha=0.7)
        axes[1, 0].set_title('阶段平均奖励对比')
        axes[1, 0].set_ylabel('平均奖励')
        
        # 添加数值标签
        for bar, reward in zip(bars2, avg_rewards):
            height = bar.get_height()
            axes[1, 0].text(bar.get_x() + bar.get_width()/2., height + abs(height)*0.01,
                           f'{reward:.1f}', ha='center', va='bottom')
        
        # 4. 整体统计对比
        phase1_collision = len([ep for ep in training_data['collision_episodes'] if ep < random_episodes])
        phase1_timeout = len([ep for ep in training_data['timeout_episodes'] if ep < random_episodes])
        
        phase2_collision = len([ep for ep in training_data['collision_episodes'] if ep >= random_episodes])
        phase2_timeout = len([ep for ep in training_data['timeout_episodes'] if ep >= random_episodes])
        
        categories = ['成功', '碰撞', '超时']
        
        phase1_counts = [len(phase1_success), phase1_collision, phase1_timeout]
        phase2_counts = [len(phase2_success), phase2_collision, phase2_timeout]
        
        x = np.arange(len(categories))
        width = 0.35
        
        bars3 = axes[1, 1].bar(x - width/2, phase1_counts, width, label='第一阶段', color='blue', alpha=0.7)
        bars4 = axes[1, 1].bar(x + width/2, phase2_counts, width, label='第二阶段', color='red', alpha=0.7)
        
        axes[1, 1].set_title('阶段结果统计对比')
        axes[1, 1].set_ylabel('Episode数量')
        axes[1, 1].set_xticks(x)
        axes[1, 1].set_xticklabels(categories)
        axes[1, 1].legend()
        
        # 添加数值标签
        for bars in [bars3, bars4]:
            for bar in bars:
                height = bar.get_height()
                if height > 0:
                    axes[1, 1].text(bar.get_x() + bar.get_width()/2., height + 0.5,
                                   f'{int(height)}', ha='center', va='bottom')
        
        plt.tight_layout()
        
        # 保存对比图
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f'training_comparison_{timestamp}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"📊 训练对比图已保存: {filename}")

        plt.close(fig)
        
    except Exception as e:
        print(f"⚠️ 训练对比图生成失败: {e}")

def main():
    """主函数"""
    print("🎯 生成剩余的训练总结图表")
    
    # 加载数据
    training_data, report = load_training_data()
    final_stats = report.get('final_statistics', {})
    
    # 生成第二阶段图表
    print("📊 生成第二阶段训练总结图...")
    generate_phase_summary_plot(training_data, final_stats, phase=2)
    
    # 生成对比图
    print("📊 生成训练阶段对比图...")
    generate_comparison_plot(training_data, final_stats)
    
    print("✅ 所有图表生成完成！")

if __name__ == "__main__":
    main()
