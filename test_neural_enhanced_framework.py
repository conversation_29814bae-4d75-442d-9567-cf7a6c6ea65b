"""
测试神经网络增强的分层安全强化学习框架
验证PINN预测和强化学习接口的一致性
"""

import sys
import os
import numpy as np
import torch
import time
from typing import Dict, List

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from neural_constraint_predictor import NeuralConstraintPredictor
    from neural_enhanced_rl_framework import NeuralEnhancedSafeRL
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保neural_constraint_predictor.py和neural_enhanced_rl_framework.py在当前目录")
    sys.exit(1)

class MockEnvironment:
    """模拟环境用于测试"""
    
    def __init__(self):
        self.state = np.array([100.0, 100.0, 50.0, 0.0, 0.0, 0.0])  # [x,y,z,vx,vy,vz]
        self.goal = np.array([800.0, 800.0, 50.0])
        self.obstacles = [
            {'center': np.array([300.0, 300.0, 50.0]), 'radius': 30.0, 'velocity': np.array([0.0, 0.0, 0.0])},
            {'center': np.array([500.0, 500.0, 50.0]), 'radius': 25.0, 'velocity': np.array([1.0, 0.0, 0.0])},
            {'center': np.array([600.0, 400.0, 60.0]), 'radius': 20.0, 'velocity': np.array([0.0, 1.0, 0.0])}
        ]
        self.bounds = np.array([1000.0, 1000.0, 100.0])
        
    def step(self, action):
        """执行动作，更新状态"""
        dt = 0.1
        
        # 更新速度
        self.state[3:6] += action * dt
        
        # 限制速度
        max_vel = 30.0
        vel_magnitude = np.linalg.norm(self.state[3:6])
        if vel_magnitude > max_vel:
            self.state[3:6] = self.state[3:6] / vel_magnitude * max_vel
        
        # 更新位置
        self.state[:3] += self.state[3:6] * dt
        
        # 计算奖励
        goal_dist = np.linalg.norm(self.state[:3] - self.goal)
        reward = -goal_dist / 50.0 - 0.1  # 距离奖励 + 时间惩罚
        
        # 检查终止条件
        done = False
        info = {}
        
        # 成功到达目标
        if goal_dist < 20.0:
            reward += 100.0
            done = True
            info['success'] = True
        
        # 碰撞检测
        for obs in self.obstacles:
            dist = np.linalg.norm(self.state[:3] - obs['center'])
            if dist < obs['radius'] + 5.0:  # 5m安全距离
                reward = -100.0
                done = True
                info['collision'] = True
                break
        
        # 越界检测
        if (self.state[:3] < 0).any() or (self.state[:3] > self.bounds).any():
            reward = -100.0
            done = True
            info['out_of_bounds'] = True
        
        return self.state.copy(), reward, done, info
    
    def reset(self):
        """重置环境"""
        self.state = np.array([100.0, 100.0, 50.0, 0.0, 0.0, 0.0])
        return self.state.copy()

def test_neural_constraint_predictor():
    """测试神经网络约束预测器"""
    print("🧪 测试神经网络约束预测器...")
    
    # 创建预测器
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")
    
    predictor = NeuralConstraintPredictor(device=device)
    
    # 创建测试环境
    env = MockEnvironment()
    state = env.reset()
    
    print(f"初始状态: {state}")
    print(f"目标位置: {env.goal}")
    print(f"障碍物数量: {len(env.obstacles)}")
    
    # 测试安全动作集生成
    start_time = time.time()
    safe_actions = predictor.generate_safe_action_set(
        state, env.obstacles, env.goal, num_candidates=100
    )
    generation_time = time.time() - start_time
    
    print(f"\n📊 约束预测器测试结果:")
    print(f"  生成时间: {generation_time:.4f}s")
    print(f"  候选动作数: 100")
    print(f"  安全动作数: {len(safe_actions)}")
    
    if safe_actions:
        best_action = safe_actions[0]
        print(f"  最优动作: {best_action['action']}")
        print(f"  安全评分: {best_action['safety_score']:.3f}")
        print(f"  总体评分: {best_action['total_score']:.3f}")
        
        # 测试轨迹预测
        if 'predicted_trajectory' in best_action:
            traj = best_action['predicted_trajectory']
            print(f"  预测轨迹长度: {len(traj)}")
            print(f"  轨迹起点: {traj[0] if len(traj) > 0 else 'N/A'}")
            print(f"  轨迹终点: {traj[-1] if len(traj) > 0 else 'N/A'}")
    else:
        print("  ⚠️ 未生成安全动作")
    
    return predictor, safe_actions

def test_neural_enhanced_rl():
    """测试神经网络增强的强化学习框架"""
    print("\n🤖 测试神经网络增强的强化学习框架...")
    
    # 创建框架
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    rl_framework = NeuralEnhancedSafeRL(state_dim=6, action_dim=3, device=device)
    
    # 创建测试环境
    env = MockEnvironment()
    state = env.reset()
    
    print(f"当前episode: {rl_framework.current_episode}")
    print(f"约束引导阶段: {rl_framework.constraint_guidance_episodes} episodes")
    
    # 测试动作选择
    start_time = time.time()
    action, info = rl_framework.get_action(state, env.obstacles, env.goal, training=True)
    selection_time = time.time() - start_time
    
    print(f"\n📊 强化学习框架测试结果:")
    print(f"  动作选择时间: {selection_time:.4f}s")
    print(f"  选择的动作: {action}")
    print(f"  选择方法: {info['selection_method']}")
    print(f"  安全候选数: {info['num_safe_candidates']}")
    print(f"  最优候选评分: {info['top_candidate_score']:.3f}")
    
    return rl_framework, action, info

def test_interface_compatibility():
    """测试接口兼容性"""
    print("\n🔗 测试接口兼容性...")
    
    # 创建组件
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    predictor = NeuralConstraintPredictor(device=device)
    rl_framework = NeuralEnhancedSafeRL(state_dim=6, action_dim=3, device=device)
    env = MockEnvironment()
    
    # 测试完整的交互流程
    state = env.reset()
    total_reward = 0
    steps = 0
    max_steps = 50
    
    print("开始交互测试...")
    
    try:
        for step in range(max_steps):
            # 使用框架选择动作
            action, info = rl_framework.get_action(state, env.obstacles, env.goal, training=True)
            
            # 执行动作
            next_state, reward, done, env_info = env.step(action)
            
            total_reward += reward
            steps += 1
            
            # 每10步打印一次
            if step % 10 == 0:
                goal_dist = np.linalg.norm(state[:3] - env.goal)
                print(f"  步骤 {step}: 距离目标 {goal_dist:.1f}m, 奖励 {reward:.2f}, 方法 {info['selection_method']}")
            
            # 更新状态
            state = next_state
            
            # 检查终止
            if done:
                if env_info.get('success', False):
                    print(f"  ✅ 成功到达目标! 步骤: {steps}")
                elif env_info.get('collision', False):
                    print(f"  ❌ 发生碰撞! 步骤: {steps}")
                elif env_info.get('out_of_bounds', False):
                    print(f"  ❌ 越界! 步骤: {steps}")
                break
            
            # 更新episode计数（模拟）
            if step % 20 == 0:
                rl_framework.update_episode_count()
        
        print(f"\n📈 交互测试结果:")
        print(f"  总步骤: {steps}")
        print(f"  总奖励: {total_reward:.2f}")
        print(f"  平均奖励: {total_reward/steps:.2f}")
        print(f"  最终距离: {np.linalg.norm(state[:3] - env.goal):.1f}m")
        
        return True
        
    except Exception as e:
        print(f"❌ 接口兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_comparison():
    """性能对比测试"""
    print("\n⚡ 性能对比测试...")
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    predictor = NeuralConstraintPredictor(device=device)
    env = MockEnvironment()
    state = env.reset()
    
    # 测试不同候选数量的性能
    candidate_nums = [20, 50, 100, 200]
    
    for num_candidates in candidate_nums:
        times = []
        for _ in range(10):  # 测试10次取平均
            start_time = time.time()
            safe_actions = predictor.generate_safe_action_set(
                state, env.obstacles, env.goal, num_candidates=num_candidates
            )
            times.append(time.time() - start_time)
        
        avg_time = np.mean(times)
        std_time = np.std(times)
        
        print(f"  候选数 {num_candidates:3d}: {avg_time:.4f}±{std_time:.4f}s, 安全动作: {len(safe_actions)}")

def main():
    """主测试函数"""
    print("🚀 神经网络增强的分层安全强化学习框架测试")
    print("=" * 60)
    
    try:
        # 1. 测试约束预测器
        predictor, safe_actions = test_neural_constraint_predictor()
        
        # 2. 测试强化学习框架
        rl_framework, action, info = test_neural_enhanced_rl()
        
        # 3. 测试接口兼容性
        compatibility_ok = test_interface_compatibility()
        
        # 4. 性能对比测试
        test_performance_comparison()
        
        print("\n" + "=" * 60)
        if compatibility_ok:
            print("✅ 所有测试通过! 框架运行正常")
        else:
            print("❌ 部分测试失败，需要修复接口问题")
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
