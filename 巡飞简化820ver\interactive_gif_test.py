"""
交互式GIF测试脚本 - 简化版
"""

import os
import sys
import glob
import json
from datetime import datetime

def find_target_training_dir():
    """查找目标训练目录"""
    print("🔍 查找训练结果目录...")
    
    # 检查上级目录中的目标训练结果
    target_dir = "../loitering_munition_staged_training_20250726_085626"
    if os.path.isdir(target_dir):
        print(f"✅ 找到目标训练目录: {target_dir}")
        return target_dir
    
    # 检查当前目录
    target_dir = "loitering_munition_staged_training_20250726_085626"
    if os.path.isdir(target_dir):
        print(f"✅ 找到目标训练目录: {target_dir}")
        return target_dir
    
    print("❌ 未找到目标训练目录")
    return None

def check_available_stages(training_dir):
    """检查可用的训练阶段"""
    model_files = glob.glob(os.path.join(training_dir, '*_model.pth'))
    available_stages = []
    
    for model_file in model_files:
        filename = os.path.basename(model_file)
        if 'stage_1' in filename:
            available_stages.append(1)
        elif 'stage_2' in filename:
            available_stages.append(2)
        elif 'stage_3' in filename:
            available_stages.append(3)
    
    available_stages.sort()
    return available_stages

def select_stage_interactively(available_stages):
    """交互式选择测试阶段"""
    print("\n🎯 选择要测试的训练阶段:")
    print("=" * 40)
    
    # 显示可用阶段和对应的训练结果
    stage_info = {
        1: {"name": "阶段1 - 简单环境基础训练", "success_rate": "98.3%", "env": "简单静态障碍物"},
        2: {"name": "阶段2 - 复杂静态环境训练", "success_rate": "91.6%", "env": "高密度静态障碍物"},
        3: {"name": "阶段3 - 动态环境适应训练", "success_rate": "4.0%", "env": "动态障碍物环境"}
    }
    
    for stage in available_stages:
        info = stage_info.get(stage, {})
        print(f"  {stage}. {info.get('name', f'阶段{stage}')}")
        print(f"     成功率: {info.get('success_rate', 'N/A')} | 环境: {info.get('env', 'N/A')}")
    
    print(f"  0. 退出程序")
    print("=" * 40)
    
    while True:
        try:
            choice = input(f"请选择要测试的阶段 ({'/'.join(map(str, available_stages))}/0): ").strip()
            
            if choice == '0':
                print("👋 退出程序")
                return None
            
            choice = int(choice)
            if choice in available_stages:
                selected_info = stage_info.get(choice, {})
                print(f"\n✅ 已选择: {selected_info.get('name', f'阶段{choice}')}")
                print(f"📊 预期成功率: {selected_info.get('success_rate', 'N/A')}")
                print(f"🌍 测试环境: {selected_info.get('env', 'N/A')}")
                print(f"⚙️  默认设置: 最大步数=1500, 帧率=12fps")
                return choice
            else:
                print(f"❌ 无效选择，请输入 {'/'.join(map(str, available_stages))} 或 0")
        except ValueError:
            print("❌ 请输入有效的数字")
        except KeyboardInterrupt:
            print("\n👋 用户取消操作")
            return None

def run_gif_generator(stage):
    """运行GIF生成器"""
    print(f"\n🚀 启动阶段 {stage} 的GIF生成...")
    print("⏳ 正在调用完整的GIF生成器...")
    
    # 调用完整的GIF生成器
    import subprocess
    import sys
    
    try:
        cmd = [sys.executable, 'test_training_gif_generator.py', '--stage', str(stage), '--steps', '1500']
        print(f"🔧 执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print("✅ GIF生成成功!")
            if result.stdout:
                print("📋 输出信息:")
                print(result.stdout[-1000:])  # 显示最后1000字符
        else:
            print("❌ GIF生成失败")
            if result.stderr:
                print("错误信息:")
                print(result.stderr[-500:])
                
    except subprocess.TimeoutExpired:
        print("⚠️ GIF生成超时（10分钟）")
    except Exception as e:
        print(f"❌ 调用GIF生成器时出错: {e}")

def main():
    """主函数"""
    print("🎬 交互式训练结果GIF生成器")
    print("=" * 50)
    print(f"📅 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 1. 查找训练目录
    training_dir = find_target_training_dir()
    if not training_dir:
        print("❌ 未找到训练结果目录")
        return False
    
    # 2. 检查可用阶段
    available_stages = check_available_stages(training_dir)
    if not available_stages:
        print("❌ 未找到可用的模型文件")
        return False
    
    print(f"📦 找到 {len(available_stages)} 个可用的训练阶段: {available_stages}")
    
    # 3. 交互式选择阶段
    selected_stage = select_stage_interactively(available_stages)
    if selected_stage is None:
        return False
    
    # 4. 运行GIF生成器
    run_gif_generator(selected_stage)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print(f"\n🎉 程序执行完成!")
        else:
            print(f"\n❌ 程序执行失败或用户取消")
    except KeyboardInterrupt:
        print(f"\n👋 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序运行出现错误: {e}")
        import traceback
        traceback.print_exc()
