"""
独立的DWA-RL测试脚本
包含修复后的探索策略和完整测试功能
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import json
import os
import time
import argparse
from datetime import datetime
import glob

from dwa_rl_core import StabilizedEnvironment, load_trained_model, td3_config

def test_dwa_rl_model(model_path, num_test_episodes=10, enable_visualization=True,
                     exploration_strategy='deterministic', save_outputs=True):
    """测试训练好的DWA-RL模型

    Args:
        exploration_strategy: 探索策略
            - 'deterministic': 确定性策略（使用训练时的噪声策略以避免超时）
    """
    print("🧪 DWA-RL模型测试")
    print("=" * 60)
    print(f"模型路径: {model_path}")
    print(f"测试Episodes: {num_test_episodes}")
    print(f"探索策略: {exploration_strategy}")
    print(f"3D可视化: {'启用' if enable_visualization else '禁用'}")
    print(f"结果保存: {'启用' if save_outputs else '禁用'}")
    print("=" * 60)

    # 加载模型
    controller = load_trained_model(model_path, td3_config)

    # 测试统计
    test_results = {
        'episode_rewards': [],
        'episode_steps': [],
        'success_count': 0,
        'collision_count': 0,
        'timeout_count': 0,
        'trajectories': [],
        'completion_times': [],
        'path_lengths': [],
        'smoothness_scores': [],
        'safety_margins': []
    }

    # 约束量统计
    test_constraint_data = {
        'velocities': [],
        'accelerations': [],
        'obstacle_distances': [],
        'goal_distances': [],
        'constraint_summaries': []
    }

    # 创建测试环境
    env = StabilizedEnvironment()

    # 3D可视化设置
    fig_3d = None
    ax_3d = None
    if enable_visualization:
        try:
            plt.ion()
            fig_3d, ax_3d = plt.subplots(1, 1, figsize=(12, 10), subplot_kw={'projection': '3d'})
            fig_3d.suptitle('DWA-RL Model Testing - 3D Trajectory Visualization', fontsize=14)

            # 设置3D环境
            start = env.start
            goal = env.goal
            ax_3d.scatter(start[0], start[1], start[2], c='green', s=200, marker='o', label='Start', alpha=0.8)
            ax_3d.scatter(goal[0], goal[1], goal[2], c='red', s=200, marker='*', label='Goal', alpha=0.8)

            # 绘制障碍物
            for obs in env.obstacles:
                center = obs['center']
                radius = obs['radius']
                u = np.linspace(0, 2 * np.pi, 10)
                v = np.linspace(0, np.pi, 10)
                x = radius * np.outer(np.cos(u), np.sin(v)) + center[0]
                y = radius * np.outer(np.sin(u), np.sin(v)) + center[1]
                z = radius * np.outer(np.ones(np.size(u)), np.cos(v)) + center[2]
                ax_3d.plot_surface(x, y, z, alpha=0.3, color='gray')

            ax_3d.set_xlabel('X (m)')
            ax_3d.set_ylabel('Y (m)')
            ax_3d.set_zlabel('Z (m)')
            ax_3d.set_xlim(0, 100)
            ax_3d.set_ylim(0, 100)
            ax_3d.set_zlim(0, 100)
            ax_3d.grid(True, alpha=0.3)
            ax_3d.legend()

            plt.tight_layout()
            plt.draw()
            plt.pause(0.01)
            print("✅ 3D Visualization initialized")

        except Exception as e:
            print(f"⚠️ Visualization failed: {e}")
            enable_visualization = False

    trajectory_colors = ['blue', 'orange', 'purple', 'brown', 'pink', 'olive', 'cyan', 'magenta', 'red', 'green']

    print(f"\n🚀 开始测试 {num_test_episodes} 个episodes...")
    print("-" * 60)

    for episode in range(num_test_episodes):
        state = env.reset()
        full_state = np.concatenate([env.state, state[6:]])

        episode_reward = 0
        step_count = 0
        trajectory = []
        start_time = time.time()

        while step_count < 500:
            # 记录轨迹
            trajectory.append(env.state[:3].copy())

            # 使用确定性策略（实际使用训练时的噪声策略以避免超时）
            add_noise = True

            action, _, _ = controller.get_action_with_quality(
                full_state, env.goal, env.obstacles, add_noise=add_noise
            )

            # 执行动作
            next_state, reward, done, env_info = env.step(action)
            next_full_state = np.concatenate([env.state, next_state[6:]])

            episode_reward += reward
            step_count += 1

            if done:
                completion_time = time.time() - start_time
                test_results['completion_times'].append(completion_time)

                # 记录结果
                if env_info.get('success', False):
                    test_results['success_count'] += 1
                    result_type = 'SUCCESS'
                    color = 'green'
                elif env_info.get('collision', False):
                    test_results['collision_count'] += 1
                    result_type = 'COLLISION'
                    color = 'red'
                else:
                    test_results['timeout_count'] += 1
                    result_type = 'TIMEOUT'
                    color = 'orange'

                break

            full_state = next_full_state
        else:
            # 超时情况
            completion_time = time.time() - start_time
            test_results['completion_times'].append(completion_time)
            test_results['timeout_count'] += 1
            result_type = 'TIMEOUT'
            color = 'orange'

        # 计算轨迹指标
        if len(trajectory) > 1:
            trajectory_array = np.array(trajectory)

            # 路径长度
            path_length = np.sum(np.linalg.norm(np.diff(trajectory_array, axis=0), axis=1))
            test_results['path_lengths'].append(path_length)

            # 平滑度评分（基于方向变化）
            if len(trajectory) > 2:
                directions = np.diff(trajectory_array, axis=0)
                direction_changes = []
                for i in range(len(directions) - 1):
                    if np.linalg.norm(directions[i]) > 0 and np.linalg.norm(directions[i+1]) > 0:
                        cos_angle = np.dot(directions[i], directions[i+1]) / (
                            np.linalg.norm(directions[i]) * np.linalg.norm(directions[i+1])
                        )
                        direction_changes.append(np.arccos(np.clip(cos_angle, -1, 1)))

                smoothness = 1.0 / (1.0 + np.mean(direction_changes)) if direction_changes else 1.0
                test_results['smoothness_scores'].append(smoothness)

            # 安全边距（最小障碍物距离）
            min_safety_margin = float('inf')
            for pos in trajectory_array:
                for obs in env.obstacles:
                    dist = np.linalg.norm(pos - obs['center']) - obs['radius']
                    min_safety_margin = min(min_safety_margin, dist)
            test_results['safety_margins'].append(max(0, min_safety_margin))

        # 收集约束数据
        constraint_summary = env.get_constraint_summary()
        if constraint_summary:
            test_constraint_data['velocities'].extend(env.constraint_history['velocities'])
            test_constraint_data['accelerations'].extend(env.constraint_history['accelerations'])
            test_constraint_data['obstacle_distances'].extend(env.constraint_history['distances_to_obstacles'])
            test_constraint_data['goal_distances'].extend(env.constraint_history['goal_distances'])
            test_constraint_data['constraint_summaries'].append(constraint_summary)

        test_results['episode_rewards'].append(episode_reward)
        test_results['episode_steps'].append(step_count)
        test_results['trajectories'].append(trajectory)

        # 更新可视化
        if enable_visualization and fig_3d is not None and ax_3d is not None:
            try:
                if trajectory and len(trajectory) > 1:
                    positions = np.array(trajectory)
                    color = trajectory_colors[episode % len(trajectory_colors)]

                    if result_type == 'SUCCESS':
                        alpha, linewidth, linestyle = 0.8, 2, '-'
                    elif result_type == 'COLLISION':
                        alpha, linewidth, linestyle = 0.6, 1.5, '--'
                        color = 'red'
                    else:
                        alpha, linewidth, linestyle = 0.4, 1, ':'

                    ax_3d.plot(positions[:, 0], positions[:, 1], positions[:, 2],
                             color=color, alpha=alpha, linewidth=linewidth, linestyle=linestyle,
                             label=f'Test {episode+1} ({result_type})')

                # 更新标题
                success_rate = test_results['success_count'] / (episode + 1)
                avg_reward = np.mean(test_results['episode_rewards'])
                ax_3d.set_title(f'Test Episode {episode + 1}/{num_test_episodes} | '
                              f'Success Rate: {success_rate:.2%} | Avg Reward: {avg_reward:.1f}')

                fig_3d.canvas.draw_idle()
                plt.pause(0.001)

            except Exception as e:
                print(f"⚠️ Visualization update failed: {e}")

        # 打印进度
        print(f"Test {episode+1:2d}: {result_type:9s} | Reward={episode_reward:6.1f} | Steps={step_count:3d} | "
              f"Time={completion_time:.2f}s" + (f" | Path={path_length:.1f}m" if len(trajectory) > 1 else ""))

    if enable_visualization:
        plt.ioff()

    return test_results, test_constraint_data

def generate_test_report(test_results, model_path, exploration_strategy, save_outputs=True):
    """生成详细的测试报告"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # 计算统计指标
    num_episodes = len(test_results['episode_rewards'])
    success_rate = test_results['success_count'] / num_episodes
    collision_rate = test_results['collision_count'] / num_episodes
    timeout_rate = test_results['timeout_count'] / num_episodes

    avg_reward = np.mean(test_results['episode_rewards'])
    std_reward = np.std(test_results['episode_rewards'])
    avg_steps = np.mean(test_results['episode_steps'])

    avg_completion_time = np.mean(test_results['completion_times']) if test_results['completion_times'] else 0
    avg_path_length = np.mean(test_results['path_lengths']) if test_results['path_lengths'] else 0
    avg_smoothness = np.mean(test_results['smoothness_scores']) if test_results['smoothness_scores'] else 0
    avg_safety_margin = np.mean(test_results['safety_margins']) if test_results['safety_margins'] else 0

    # 打印测试结果
    print(f"\n📊 测试结果总结:")
    print("=" * 60)
    print(f"测试模型: {model_path}")
    print(f"探索策略: {exploration_strategy}")
    print(f"测试Episodes: {num_episodes}")
    print(f"成功率: {success_rate:.3f} ({test_results['success_count']}/{num_episodes})")
    print(f"碰撞率: {collision_rate:.3f} ({test_results['collision_count']}/{num_episodes})")
    print(f"超时率: {timeout_rate:.3f} ({test_results['timeout_count']}/{num_episodes})")
    print(f"平均奖励: {avg_reward:.2f} ± {std_reward:.2f}")
    print(f"平均步数: {avg_steps:.1f}")
    print(f"平均完成时间: {avg_completion_time:.2f}s")
    print(f"平均路径长度: {avg_path_length:.1f}m")
    print(f"平均轨迹平滑度: {avg_smoothness:.3f}")
    print(f"平均安全边距: {avg_safety_margin:.1f}m")

    if save_outputs:
        # 创建测试报告
        test_report = {
            "test_summary": {
                "model_path": model_path,
                "exploration_strategy": exploration_strategy,
                "test_timestamp": datetime.now().isoformat(),
                "num_test_episodes": num_episodes,
                "test_type": "dwa_rl_model_evaluation"
            },
            "performance_metrics": {
                "success_rate": round(success_rate, 4),
                "collision_rate": round(collision_rate, 4),
                "timeout_rate": round(timeout_rate, 4),
                "average_reward": round(avg_reward, 3),
                "reward_std": round(std_reward, 3),
                "average_steps": round(avg_steps, 1),
                "average_completion_time": round(avg_completion_time, 3),
                "average_path_length": round(avg_path_length, 2),
                "average_smoothness": round(avg_smoothness, 4),
                "average_safety_margin": round(avg_safety_margin, 2)
            },
            "detailed_results": {
                "episode_rewards": [round(r, 3) for r in test_results['episode_rewards']],
                "episode_steps": test_results['episode_steps'],
                "completion_times": [round(t, 3) for t in test_results['completion_times']],
                "path_lengths": [round(l, 2) for l in test_results['path_lengths']],
                "smoothness_scores": [round(s, 4) for s in test_results['smoothness_scores']],
                "safety_margins": [round(m, 2) for m in test_results['safety_margins']]
            },
            "quality_assessment": {
                "overall_grade": "A" if success_rate >= 0.9 else "B" if success_rate >= 0.7 else "C" if success_rate >= 0.5 else "D",
                "safety_grade": "A" if collision_rate <= 0.05 else "B" if collision_rate <= 0.1 else "C" if collision_rate <= 0.2 else "D",
                "efficiency_grade": "A" if avg_steps <= 300 else "B" if avg_steps <= 400 else "C" if avg_steps <= 450 else "D",
                "smoothness_grade": "A" if avg_smoothness >= 0.8 else "B" if avg_smoothness >= 0.6 else "C" if avg_smoothness >= 0.4 else "D"
            }
        }

        # 保存测试报告
        if not os.path.exists('training_outputs'):
            os.makedirs('training_outputs')

        report_path = f'training_outputs/test_report_{timestamp}.json'
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(test_report, f, indent=2, ensure_ascii=False)

        print(f"\n✅ 测试报告已保存: {report_path}")

        # 生成测试可视化
        create_test_visualization(test_results, timestamp)

        return test_report

    return None

def create_test_visualization(test_results, timestamp):
    """创建测试结果可视化"""
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('DWA-RL Model Test Results Analysis', fontsize=16, fontweight='bold')

    # 1. 成功率饼图
    ax1 = axes[0, 0]
    labels = ['Success', 'Collision', 'Timeout']
    sizes = [test_results['success_count'], test_results['collision_count'], test_results['timeout_count']]
    colors = ['green', 'red', 'orange']
    ax1.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
    ax1.set_title('Episode Outcomes')

    # 2. 奖励分布
    ax2 = axes[0, 1]
    ax2.hist(test_results['episode_rewards'], bins=10, alpha=0.7, color='skyblue', edgecolor='black')
    ax2.axvline(np.mean(test_results['episode_rewards']), color='red', linestyle='--',
               label=f'Mean: {np.mean(test_results["episode_rewards"]):.1f}')
    ax2.set_title('Reward Distribution')
    ax2.set_xlabel('Episode Reward')
    ax2.set_ylabel('Frequency')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # 3. 步数分布
    ax3 = axes[0, 2]
    ax3.hist(test_results['episode_steps'], bins=10, alpha=0.7, color='lightgreen', edgecolor='black')
    ax3.axvline(np.mean(test_results['episode_steps']), color='red', linestyle='--',
               label=f'Mean: {np.mean(test_results["episode_steps"]):.1f}')
    ax3.set_title('Steps Distribution')
    ax3.set_xlabel('Episode Steps')
    ax3.set_ylabel('Frequency')
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # 4. 路径长度 vs 步数
    ax4 = axes[1, 0]
    if test_results['path_lengths']:
        ax4.scatter(test_results['episode_steps'], test_results['path_lengths'], alpha=0.7, color='purple')
        ax4.set_title('Path Length vs Steps')
        ax4.set_xlabel('Steps')
        ax4.set_ylabel('Path Length (m)')
        ax4.grid(True, alpha=0.3)

    # 5. 平滑度评分
    ax5 = axes[1, 1]
    if test_results['smoothness_scores']:
        ax5.hist(test_results['smoothness_scores'], bins=10, alpha=0.7, color='orange', edgecolor='black')
        ax5.axvline(np.mean(test_results['smoothness_scores']), color='red', linestyle='--',
                   label=f'Mean: {np.mean(test_results["smoothness_scores"]):.3f}')
        ax5.set_title('Trajectory Smoothness')
        ax5.set_xlabel('Smoothness Score')
        ax5.set_ylabel('Frequency')
        ax5.legend()
        ax5.grid(True, alpha=0.3)

    # 6. 安全边距
    ax6 = axes[1, 2]
    if test_results['safety_margins']:
        ax6.hist(test_results['safety_margins'], bins=10, alpha=0.7, color='cyan', edgecolor='black')
        ax6.axvline(np.mean(test_results['safety_margins']), color='red', linestyle='--',
                   label=f'Mean: {np.mean(test_results["safety_margins"]):.1f}m')
        ax6.set_title('Safety Margins')
        ax6.set_xlabel('Min Distance to Obstacles (m)')
        ax6.set_ylabel('Frequency')
        ax6.legend()
        ax6.grid(True, alpha=0.3)

    plt.tight_layout()

    # 保存可视化
    viz_path = f'training_outputs/test_visualization_{timestamp}.png'
    plt.savefig(viz_path, dpi=300, bbox_inches='tight')
    print(f"✅ 测试可视化已保存: {viz_path}")

    plt.show()

def create_constraint_analysis(constraint_data, timestamp, phase="testing"):
    """Create constraint analysis charts"""
    if not constraint_data['velocities']:
        print("⚠️ No constraint data available for analysis")
        return

    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle(f'DWA-RL {phase.title()} - Constraint Analysis', fontsize=16, fontweight='bold')

    velocities = np.array(constraint_data['velocities'])
    accelerations = np.array(constraint_data['accelerations'])
    obstacle_distances = np.array(constraint_data['obstacle_distances'])

    # 约束限制线 - 使用数学一致的约束值
    max_velocity_components = [3.0, 3.0, 3.0]  # DWA分量约束
    max_velocity = np.sqrt(sum([v**2 for v in max_velocity_components]))  # 对应的合速度约束
    max_acceleration_components = [5.0, 5.0, 5.0]  # DWA分量约束
    max_acceleration = np.sqrt(sum([a**2 for a in max_acceleration_components]))  # 对应的合加速度约束
    min_obstacle_distance = 3.0

    # 1. 速度时间序列
    ax1 = axes[0, 0]
    time_steps = np.arange(len(velocities))
    ax1.plot(time_steps, velocities, 'b-', alpha=0.7, linewidth=1)
    ax1.axhline(y=max_velocity, color='red', linestyle='--', linewidth=2, label=f'Total Velocity Limit ({max_velocity:.2f} m/s)')
    ax1.axhline(y=max_velocity_components[0], color='orange', linestyle=':', linewidth=1, label=f'Component Limit ({max_velocity_components[0]} m/s)')
    ax1.fill_between(time_steps, 0, max_velocity, alpha=0.2, color='green', label='Safe Zone')
    ax1.fill_between(time_steps, max_velocity, velocities.max()*1.1, alpha=0.2, color='red', label='Violation Zone')
    ax1.set_title('Velocity Time Series')
    ax1.set_xlabel('Time Steps')
    ax1.set_ylabel('Velocity (m/s)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 2. 加速度时间序列
    ax2 = axes[0, 1]
    ax2.plot(time_steps, accelerations, 'g-', alpha=0.7, linewidth=1)
    ax2.axhline(y=max_acceleration, color='red', linestyle='--', linewidth=2, label=f'Total Acceleration Limit ({max_acceleration:.2f} m/s²)')
    ax2.axhline(y=max_acceleration_components[0], color='orange', linestyle=':', linewidth=1, label=f'Component Limit ({max_acceleration_components[0]} m/s²)')
    ax2.fill_between(time_steps, 0, max_acceleration, alpha=0.2, color='green', label='Safe Zone')
    ax2.fill_between(time_steps, max_acceleration, accelerations.max()*1.1, alpha=0.2, color='red', label='Violation Zone')
    ax2.set_title('Acceleration Time Series')
    ax2.set_xlabel('Time Steps')
    ax2.set_ylabel('Acceleration (m/s²)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # 3. 障碍物距离时间序列
    ax3 = axes[0, 2]
    ax3.plot(time_steps, obstacle_distances, 'orange', alpha=0.7, linewidth=1)
    ax3.axhline(y=min_obstacle_distance, color='red', linestyle='--', linewidth=2, label=f'Min Safe Distance ({min_obstacle_distance} m)')
    ax3.fill_between(time_steps, min_obstacle_distance, obstacle_distances.max()*1.1, alpha=0.2, color='green', label='Safe Zone')
    ax3.fill_between(time_steps, 0, min_obstacle_distance, alpha=0.2, color='red', label='Danger Zone')
    ax3.set_title('Obstacle Distance Time Series')
    ax3.set_xlabel('Time Steps')
    ax3.set_ylabel('Min Obstacle Distance (m)')
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # 4. 速度分布直方图
    ax4 = axes[1, 0]
    ax4.hist(velocities, bins=30, alpha=0.7, color='blue', edgecolor='black')
    ax4.axvline(x=max_velocity, color='red', linestyle='--', linewidth=2, label=f'Total Velocity Limit ({max_velocity:.2f} m/s)')
    ax4.axvline(x=max_velocity_components[0], color='orange', linestyle=':', linewidth=1, label=f'Component Limit ({max_velocity_components[0]} m/s)')
    ax4.axvline(x=np.mean(velocities), color='green', linestyle='-', linewidth=2, label=f'Mean Velocity ({np.mean(velocities):.2f} m/s)')
    violation_rate = np.sum(velocities > max_velocity) / len(velocities) * 100
    ax4.set_title(f'Velocity Distribution (Total Violation Rate: {violation_rate:.1f}%)')
    ax4.set_xlabel('Velocity (m/s)')
    ax4.set_ylabel('Frequency')
    ax4.legend()
    ax4.grid(True, alpha=0.3)

    # 5. 加速度分布直方图
    ax5 = axes[1, 1]
    ax5.hist(accelerations, bins=30, alpha=0.7, color='green', edgecolor='black')
    ax5.axvline(x=max_acceleration, color='red', linestyle='--', linewidth=2, label=f'Total Acceleration Limit ({max_acceleration:.2f} m/s²)')
    ax5.axvline(x=max_acceleration_components[0], color='orange', linestyle=':', linewidth=1, label=f'Component Limit ({max_acceleration_components[0]} m/s²)')
    ax5.axvline(x=np.mean(accelerations), color='blue', linestyle='-', linewidth=2, label=f'Mean Acceleration ({np.mean(accelerations):.2f} m/s²)')
    violation_rate = np.sum(accelerations > max_acceleration) / len(accelerations) * 100
    ax5.set_title(f'Acceleration Distribution (Total Violation Rate: {violation_rate:.1f}%)')
    ax5.set_xlabel('Acceleration (m/s²)')
    ax5.set_ylabel('Frequency')
    ax5.legend()
    ax5.grid(True, alpha=0.3)

    # 6. 约束违反统计
    ax6 = axes[1, 2]
    if constraint_data['constraint_summaries']:
        summaries = constraint_data['constraint_summaries']
        velocity_violations = [s['velocity_stats']['magnitude_violations'] for s in summaries]
        acceleration_violations = [s['acceleration_stats']['magnitude_violations'] for s in summaries]
        obstacle_violations = [s['obstacle_distance_stats']['violations'] for s in summaries]

        episodes = np.arange(len(summaries))
        width = 0.25

        ax6.bar(episodes - width, velocity_violations, width, label='Velocity Violations', color='blue', alpha=0.7)
        ax6.bar(episodes, acceleration_violations, width, label='Acceleration Violations', color='green', alpha=0.7)
        ax6.bar(episodes + width, obstacle_violations, width, label='Obstacle Violations', color='orange', alpha=0.7)

        ax6.set_title('Constraint Violations per Episode')
        ax6.set_xlabel('Episode')
        ax6.set_ylabel('Violation Count')
        ax6.legend()
        ax6.grid(True, alpha=0.3)

    plt.tight_layout()

    # 保存图表
    constraint_plot_path = f'training_outputs/{phase}_constraint_analysis_{timestamp}.png'
    plt.savefig(constraint_plot_path, dpi=300, bbox_inches='tight')
    print(f"✅ 约束分析图已保存: {constraint_plot_path}")

    plt.show()

    # Print constraint statistics summary
    print(f"\n📊 {phase.title()} Constraint Statistics Summary (DWA-Environment Constraint Consistency Fixed):")
    print("=" * 80)
    print(f"Total Time Steps: {len(velocities)}")
    print(f"Velocity Constraint Statistics:")
    print(f"  - DWA Component Constraints: {max_velocity_components} m/s")
    print(f"  - Corresponding Total Velocity Constraint: {max_velocity:.2f} m/s")
    print(f"  - Actual Maximum: {np.max(velocities):.2f} m/s")
    print(f"  - Average: {np.mean(velocities):.2f} m/s")
    print(f"  - Total Velocity Violations: {np.sum(velocities > max_velocity)} ({np.sum(velocities > max_velocity)/len(velocities)*100:.1f}%)")
    print(f"Acceleration Constraint Statistics:")
    print(f"  - DWA Component Constraints: {max_acceleration_components} m/s²")
    print(f"  - Corresponding Total Acceleration Constraint: {max_acceleration:.2f} m/s²")
    print(f"  - Actual Maximum: {np.max(accelerations):.2f} m/s²")
    print(f"  - Average: {np.mean(accelerations):.2f} m/s²")
    print(f"  - Total Acceleration Violations: {np.sum(accelerations > max_acceleration)} ({np.sum(accelerations > max_acceleration)/len(accelerations)*100:.1f}%)")
    print(f"Obstacle Distance Statistics:")
    print(f"  - Minimum: {np.min(obstacle_distances):.2f} m (Safe Distance: {min_obstacle_distance} m)")
    print(f"  - Average: {np.mean(obstacle_distances):.2f} m")
    print(f"  - Violations: {np.sum(obstacle_distances < min_obstacle_distance)} ({np.sum(obstacle_distances < min_obstacle_distance)/len(obstacle_distances)*100:.1f}%)")

    return constraint_plot_path

def find_latest_model():
    """查找最新的模型文件"""
    model_files = glob.glob('training_outputs/dwa_rl_model_*.pth')
    if not model_files:
        # 尝试查找旧的模型文件
        model_files = glob.glob('training_outputs/stabilized_td3_model_*.pth')

    if model_files:
        return max(model_files, key=os.path.getctime)
    else:
        return None

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='测试DWA-RL模型')
    parser.add_argument('--model', type=str, help='模型文件路径')
    parser.add_argument('--episodes', type=int, default=10, help='测试episodes数量')
    parser.add_argument('--strategy', type=str, choices=['deterministic'],
                       default='deterministic', help='探索策略（固定为deterministic）')
    parser.add_argument('--no-viz', action='store_true', help='禁用3D可视化')
    parser.add_argument('--no-save', action='store_true', help='禁用结果保存')

    args = parser.parse_args()

    print("🧪 DWA-RL独立测试脚本")
    print("=" * 50)

    # 确定模型路径
    if args.model:
        model_path = args.model
    else:
        model_path = find_latest_model()
        if model_path:
            print(f"🔍 使用最新模型: {model_path}")
        else:
            print("❌ 未找到训练好的模型文件")
            print("请先运行训练脚本或指定模型路径")
            exit(1)

    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        exit(1)

    # 运行测试
    test_results, test_constraint_data = test_dwa_rl_model(
        model_path=model_path,
        num_test_episodes=args.episodes,
        enable_visualization=not args.no_viz,
        exploration_strategy=args.strategy,
        save_outputs=not args.no_save
    )

    # 生成测试报告
    report = generate_test_report(test_results, model_path, args.strategy, not args.no_save)

    # 生成约束分析
    if not args.no_save:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        create_constraint_analysis(test_constraint_data, timestamp, "testing")

    print("\n✅ 测试完成!")
    print(f"📊 最终成功率: {test_results['success_count']}/{args.episodes} = {test_results['success_count']/args.episodes:.1%}")
    print(f"📈 平均奖励: {np.mean(test_results['episode_rewards']):.2f}")
    print(f"⏱️ 平均完成时间: {np.mean(test_results['completion_times']):.2f}s")

    if not args.no_save:
        print("\n📁 输出文件:")
        print("• training_outputs/test_report_*.json - 详细测试报告")
        print("• training_outputs/test_visualization_*.png - 测试结果可视化")

    # 根据测试结果给出建议
    if test_results['success_count'] / args.episodes < 0.5:
        print("\n⚠️ 注意: 成功率较低，可能需要:")
        print("• 检查模型训练质量")
        print("• 增加测试episodes数量以获得更稳定的结果")
        print("• 重新训练模型")