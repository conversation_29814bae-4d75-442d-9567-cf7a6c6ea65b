# 巡飞弹六自由度分阶段训练系统依赖库
# Loitering Munition 6-DOF Staged Training System Dependencies

# 核心计算库
numpy>=1.21.0
torch>=1.9.0
matplotlib>=3.3.0

# 可选依赖（用于可视化和分析）
scipy>=1.7.0
seaborn>=0.11.0
pandas>=1.3.0

# 开发和测试工具（可选）
pytest>=6.0.0
jupyter>=1.0.0

# 说明：
# - numpy: 数值计算和数组操作
# - torch: 深度学习框架（TD3网络）
# - matplotlib: 绘图和可视化
# - scipy: 科学计算（可选，用于高级数学函数）
# - seaborn: 统计图表（可选，用于结果分析）
# - pandas: 数据处理（可选，用于结果分析）
# - pytest: 单元测试（可选，用于开发）
# - jupyter: 交互式开发环境（可选）

# 安装命令：
# pip install -r requirements.txt

# 最小安装（仅核心功能）：
# pip install numpy torch matplotlib
