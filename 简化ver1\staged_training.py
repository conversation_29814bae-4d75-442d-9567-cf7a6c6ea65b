"""
分阶段训练脚本 - 简化ver1
实现三个阶段的渐进式训练：简单-复杂-动态
每个阶段内部分为固定场景+随机场景两个子阶段
"""

import os
import argparse
import time
import json
import numpy as np
import pickle
from datetime import datetime
from collections import deque

# 导入matplotlib用于图表生成
try:
    import matplotlib.pyplot as plt
    from mpl_toolkits.mplot3d import Axes3D
    import matplotlib
    matplotlib.use('Agg')  # 使用非交互式后端
    MATPLOTLIB_AVAILABLE = True

    # 检查中文字体支持
    try:
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        CHINESE_FONT_AVAILABLE = True
    except:
        CHINESE_FONT_AVAILABLE = False

except ImportError:
    MATPLOTLIB_AVAILABLE = False
    CHINESE_FONT_AVAILABLE = False
    print("⚠️ matplotlib未安装，将跳过图表生成")

# 导入环境配置
from environment_config import (
    get_environment_config, 
    get_training_stage_config, 
    TRAINING_STAGES,
    print_all_configs
)

# 导入核心组件
from dwa_rl_core import StabilizedRewardEnvironment, StabilizedTD3Controller, td3_config

class StagedTrainer:
    """分阶段训练器"""
    
    def __init__(self, start_stage=1, end_stage=3, seed=42, visualization_interval=20):
        self.start_stage = start_stage
        self.end_stage = end_stage
        self.seed = seed
        self.visualization_interval = visualization_interval
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 创建输出目录 - 直接保存在当前目录（简化ver1文件夹）
        self.output_dir = '.'  # 当前目录
        # 创建一个子目录用于组织文件，保持与之前文件的命名一致
        self.results_subdir = f'staged_training_{self.timestamp}'
        self.full_output_dir = os.path.join(self.output_dir, self.results_subdir)
        os.makedirs(self.full_output_dir, exist_ok=True)

        # 图表和字体设置
        self.chinese_font_available = CHINESE_FONT_AVAILABLE

        # 训练结果记录
        self.training_results = {
            "start_time": datetime.now().isoformat(),
            "stages": {},
            "overall_performance": {},
            "stage_transitions": []
        }
        
        print(f"🎯 分阶段训练系统")
        print(f"📅 训练时间: {self.timestamp}")
        print(f"🎯 训练阶段: {start_stage} 到 {end_stage}")
        print(f"📁 输出目录: {self.full_output_dir}")
        print("=" * 60)
    
    def run_staged_training(self):
        """执行分阶段训练"""
        print(f"\n🚀 开始分阶段训练...")
        
        # 阶段名称映射
        stage_names = {
            1: "stage1_simple",
            2: "stage2_complex", 
            3: "stage3_dynamic"
        }
        
        current_model = None
        total_start_time = time.time()
        
        # 逐阶段训练
        for stage_num in range(self.start_stage, self.end_stage + 1):
            stage_key = stage_names[stage_num]
            stage_config = get_training_stage_config(stage_key)
            env_config = get_environment_config(stage_config["environment"])
            
            print(f"\n📍 阶段 {stage_num}: {stage_config['description']}")
            print("=" * 60)
            print(f"环境: {stage_config['environment']}")
            print(f"固定场景训练: {stage_config['fixed_episodes']} episodes")
            print(f"随机场景训练: {stage_config['random_episodes']} episodes")
            print(f"总计: {stage_config['total_episodes']} episodes")
            print(f"环境描述: {env_config['description']}")
            
            try:
                # 训练当前阶段
                stage_results, trained_controller = self._train_single_stage(
                    stage_num, stage_config, env_config, current_model
                )
                
                # 记录阶段结果
                self.training_results["stages"][f"stage_{stage_num}"] = stage_results
                current_model = trained_controller
                
                print(f"✅ 阶段 {stage_num} 完成")
                print(f"   随机场景成功率: {stage_results['final_stats']['phase1_success_rate']:.2%}")
                print(f"   固定场景成功率: {stage_results['final_stats']['phase2_success_rate']:.2%}")
                print(f"   总体成功率: {stage_results['final_stats']['overall_success_rate']:.2%}")
                print(f"   平均奖励: {stage_results['final_stats']['avg_reward']:.1f}")
                print(f"   训练时间: {stage_results['final_stats']['total_training_time']:.1f}秒")
                
                # 保存阶段模型
                stage_model_path = os.path.join(self.output_dir, f"stage_{stage_num}_model.pth")
                # TODO: 保存模型的实际实现
                
            except Exception as e:
                print(f"❌ 阶段 {stage_num} 训练失败: {e}")
                self.training_results["stages"][f"stage_{stage_num}"] = {"error": str(e)}
                break
        
        total_training_time = time.time() - total_start_time
        
        # 最终测试
        print(f"\n🧪 最终综合测试")
        print("=" * 50)
        
        try:
            final_test_results = self._run_final_test(current_model)
            self.training_results["final_test"] = final_test_results
        except Exception as e:
            print(f"⚠️ 最终测试失败: {e}")
            self.training_results["final_test"] = {"error": str(e)}
        
        # 保存训练结果
        self.training_results["end_time"] = datetime.now().isoformat()
        self.training_results["total_training_time"] = total_training_time
        
        results_path = os.path.join(self.full_output_dir, "staged_training_results.json")
        with open(results_path, 'w', encoding='utf-8') as f:
            json.dump(self.training_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📊 训练结果已保存: {results_path}")
        
        # 打印总结
        self._print_training_summary()
        
        return self.training_results
    
    def _train_single_stage(self, stage_num, stage_config, env_config, previous_controller=None):
        """训练单个阶段"""
        stage_start_time = time.time()
        
        # 创建环境
        env = StabilizedRewardEnvironment(
            bounds=[100, 100, 100],
            environment_config=env_config,
            reward_type='simplified'
        )
        
        # 创建或继承控制器
        if previous_controller is not None:
            controller = previous_controller
            print(f"🔄 继承上一阶段的训练模型")
        else:
            controller = StabilizedTD3Controller(td3_config)
            print(f"🆕 创建新的训练模型")
        
        # 阶段训练数据（与原始训练脚本格式一致）
        stage_data = {
            'episode_rewards': [],
            'episode_steps': [],
            'success_episodes': [],
            'collision_episodes': [],
            'timeout_episodes': [],
            'visualization_episodes': [],
            'action_qualities': [],
            'phase_transitions': [],
            'scenario_complexity': [],
            'most_complex_scenario': None
        }
        
        total_episodes = 0
        
        # 第一子阶段：随机场景训练（探索各种场景）
        print(f"\n🎲 子阶段1: 随机场景训练 ({stage_config['random_episodes']} episodes)")
        print("目标：探索各种随机场景，收集场景复杂度数据")

        scenario_candidates = []

        for episode in range(stage_config['random_episodes']):
            episode_reward, episode_success, episode_steps, episode_result, trajectory = self._train_single_episode_detailed(
                env, controller, episode, total_episodes, None, f"Stage{stage_num}_Random", stage_data
            )

            # 保存当前场景数据用于复杂度评估
            scenario_data = env.save_scenario()
            complexity_score = self._evaluate_scenario_complexity(env, episode_reward, episode_success)

            scenario_candidates.append({
                'scenario': scenario_data,
                'complexity_score': complexity_score,
                'episode_reward': episode_reward,
                'episode_success': episode_success,
                'episode_num': episode
            })

            # 记录详细数据
            stage_data['episode_rewards'].append(episode_reward)
            stage_data['episode_steps'].append(episode_steps)

            if episode_result == 'success':
                stage_data['success_episodes'].append(total_episodes)
            elif episode_result == 'collision':
                stage_data['collision_episodes'].append(total_episodes)
            else:
                stage_data['timeout_episodes'].append(total_episodes)

            stage_data['scenario_complexity'].append({
                'episode': total_episodes,
                'complexity_score': complexity_score,
                'scenario_data': scenario_data,
                'episode_data': {
                    'episode_reward': episode_reward,
                    'episode_result': episode_result,
                    'episode_steps': episode_steps
                }
            })

            total_episodes += 1

        # 选择最复杂的场景用于固定训练
        most_complex = max(scenario_candidates, key=lambda x: x['complexity_score'])
        fixed_scenario = most_complex['scenario']
        stage_data['most_complex_scenario'] = most_complex

        print(f"\n🎯 选定最复杂场景进行固定训练:")
        print(f"  • Episode: {most_complex['episode_num'] + 1}")
        print(f"  • 复杂度分数: {most_complex['complexity_score']:.1f}")
        print(f"  • 障碍物数量: {len(most_complex['scenario']['obstacles'])}")
        print(f"  • 当时表现: {'成功' if most_complex['episode_success'] else '失败'}")

        # 记录阶段转换
        stage_data['phase_transitions'].append({
            'phase': 'random_to_fixed',
            'episode': total_episodes,
            'selected_scenario': most_complex,
            'phase1_success_rate': len(stage_data['success_episodes']) / stage_config['random_episodes']
        })

        # 第二子阶段：固定场景强化训练
        print(f"\n📌 子阶段2: 固定场景强化训练 ({stage_config['fixed_episodes']} episodes)")
        print("目标：在选定的最复杂场景中强化训练")

        for episode in range(stage_config['fixed_episodes']):
            episode_reward, episode_success, episode_steps, episode_result, trajectory = self._train_single_episode_detailed(
                env, controller, episode, total_episodes, fixed_scenario, f"Stage{stage_num}_Fixed", stage_data
            )

            # 记录详细数据
            stage_data['episode_rewards'].append(episode_reward)
            stage_data['episode_steps'].append(episode_steps)

            if episode_result == 'success':
                stage_data['success_episodes'].append(total_episodes)
            elif episode_result == 'collision':
                stage_data['collision_episodes'].append(total_episodes)
            else:
                stage_data['timeout_episodes'].append(total_episodes)

            total_episodes += 1
        
        stage_training_time = time.time() - stage_start_time
        
        # 计算阶段统计（与原始训练脚本格式一致）
        random_episodes = stage_config['random_episodes']
        fixed_episodes = stage_config['fixed_episodes']
        total_episodes = len(stage_data['episode_rewards'])

        # 分割阶段数据
        random_success = [ep for ep in stage_data['success_episodes'] if ep < random_episodes]
        fixed_success = [ep for ep in stage_data['success_episodes'] if ep >= random_episodes]

        stage_results = {
            'training_data': stage_data,
            'final_stats': {
                'total_episodes': total_episodes,
                'random_episodes': random_episodes,
                'fixed_episodes': fixed_episodes,
                'total_training_time': stage_training_time,
                'visualization_count': len(stage_data['visualization_episodes']),
                'phase1_success_rate': len(random_success) / random_episodes if random_episodes > 0 else 0,
                'phase2_success_rate': len(fixed_success) / fixed_episodes if fixed_episodes > 0 else 0,
                'overall_success_rate': len(stage_data['success_episodes']) / total_episodes if total_episodes > 0 else 0,
                'avg_reward': np.mean(stage_data['episode_rewards']) if stage_data['episode_rewards'] else 0,
                'avg_steps': np.mean(stage_data['episode_steps']) if stage_data['episode_steps'] else 0,
                'collision_rate': len(stage_data['collision_episodes']) / total_episodes if total_episodes > 0 else 0,
                'timeout_rate': len(stage_data['timeout_episodes']) / total_episodes if total_episodes > 0 else 0
            },
            'environment': stage_config['environment']
        }

        # 生成图表和保存数据
        self._save_stage_results(stage_num, stage_results, controller)
        self._generate_stage_plots(stage_num, stage_results)
        
        return stage_results, controller
    
    def _train_single_episode(self, env, controller, episode, global_episode, fixed_scenario, phase_name):
        """训练单个episode"""
        # 重置环境
        if fixed_scenario is not None:
            state = env.reset(fixed_scenario)
        else:
            state = env.reset()
        
        full_state = np.concatenate([env.state, state[6:]])
        
        episode_reward = 0
        step_count = 0
        episode_success = False
        trajectory = []
        
        while step_count < 500:
            # 记录轨迹
            trajectory.append(env.state[:3].copy())
            
            # 获取动作
            action, info, safe_actions = controller.get_action_with_quality(
                full_state, env.goal, env.obstacles, add_noise=True
            )
            
            # 执行动作
            next_state, reward, done, env_info = env.step(action)
            next_full_state = np.concatenate([env.state, next_state[6:]])
            
            episode_reward += reward
            step_count += 1
            
            # 存储经验
            controller.replay_buffer.add(
                full_state.copy(),
                action.copy(),
                reward,
                next_full_state.copy(),
                done,
                safe_actions,
                env.goal.copy(),
                [obs.copy() for obs in env.obstacles],
                info.get('selected_idx', 0)
            )
            
            # 训练更新
            controller.immediate_update(batch_size=64)
            
            if done:
                if env_info.get('success', False):
                    episode_success = True
                break
            
            full_state = next_full_state
        
        # 生成可视化（每隔指定间隔）
        if (global_episode + 1) % self.visualization_interval == 0:
            self._generate_3d_trajectory_plot(env, trajectory, global_episode, episode_success, phase_name)
        
        # 每10个episode报告一次
        if (episode + 1) % 10 == 0:
            print(f"Episode {episode+1:3d}: Reward={episode_reward:7.1f}, "
                  f"Success={'Yes' if episode_success else 'No'}, Steps={step_count}")
        
        return episode_reward, episode_success

    def _train_single_episode_detailed(self, env, controller, episode, global_episode, fixed_scenario, phase_name, stage_data):
        """训练单个episode（详细版本，与原始训练脚本一致）"""
        # 重置环境
        if fixed_scenario is not None:
            state = env.reset(fixed_scenario)
        else:
            state = env.reset()

        full_state = np.concatenate([env.state, state[6:]])

        episode_reward = 0
        step_count = 0
        episode_done = False
        episode_result = 'timeout'
        trajectory = []
        episode_step_rewards = []

        while step_count < 500 and not episode_done:
            # 记录轨迹
            trajectory.append(env.state[:3].copy())

            # 获取动作
            action, info, safe_actions = controller.get_action_with_quality(
                full_state, env.goal, env.obstacles, add_noise=(episode > 10)
            )

            # 执行动作
            next_state, reward, done, env_info = env.step(action)
            next_full_state = np.concatenate([env.state, next_state[6:]])

            # 记录奖励和质量
            episode_step_rewards.append(reward)
            episode_reward += reward
            step_count += 1

            # 记录动作质量
            if 'quality_score' in info:
                stage_data['action_qualities'].append(info['quality_score'])

            # 存储经验
            controller.replay_buffer.add(
                full_state.copy(),
                action.copy(),
                reward,
                next_full_state.copy(),
                done,
                safe_actions,
                env.goal.copy(),
                [obs.copy() for obs in env.obstacles],
                info.get('selected_idx', 0)
            )

            # 训练更新
            controller.immediate_update(batch_size=64)

            if done:
                episode_done = True
                if env_info.get('success', False):
                    episode_result = 'success'
                elif env_info.get('collision', False):
                    episode_result = 'collision'
                else:
                    episode_result = 'timeout'
                break

            full_state = next_full_state

        # 生成可视化（每隔指定间隔）
        if (global_episode + 1) % self.visualization_interval == 0:
            self._generate_3d_trajectory_plot(env, trajectory, global_episode, episode_result, phase_name)
            stage_data['visualization_episodes'].append(global_episode)

        # 每10个episode报告一次
        if (episode + 1) % 10 == 0:
            print(f"Episode {episode+1:3d}: Reward={episode_reward:7.1f}, "
                  f"Result={episode_result}, Steps={step_count}")

        return episode_reward, episode_result == 'success', step_count, episode_result, trajectory

    def _evaluate_scenario_complexity(self, env, episode_reward, episode_success):
        """评估场景复杂度（简化版本）"""
        complexity_score = 0

        # 1. 障碍物数量
        num_obstacles = len(env.obstacles)
        complexity_score += num_obstacles * 2

        # 2. 障碍物大小变化
        if num_obstacles > 0:
            radii = [obs['radius'] for obs in env.obstacles]
            radius_variance = np.var(radii) if len(radii) > 1 else 0
            complexity_score += radius_variance * 5

        # 3. 训练表现（失败或低奖励增加复杂度）
        if not episode_success:
            complexity_score += 30
        elif episode_reward < 0:
            complexity_score += abs(episode_reward) * 0.1

        # 4. 动态障碍物（如果有）
        if hasattr(env, 'dynamic_obstacles') and env.dynamic_obstacles:
            complexity_score += len(env.dynamic_obstacles) * 15

        return complexity_score

    def _generate_3d_trajectory_plot(self, env, trajectory, episode, episode_result, phase_name=""):
        """生成3D轨迹图（与原始训练脚本一致）"""
        if not MATPLOTLIB_AVAILABLE:
            return

        try:
            fig = plt.figure(figsize=(12, 10))
            ax = fig.add_subplot(111, projection='3d')

            # 绘制起点和终点
            start = env.start
            goal = env.goal
            ax.scatter(start[0], start[1], start[2], c='green', s=200, marker='o', label='Start', alpha=0.8)
            ax.scatter(goal[0], goal[1], goal[2], c='red', s=200, marker='*', label='Goal', alpha=0.8)

            # 绘制障碍物球体
            for i, obs in enumerate(env.obstacles):
                center = obs['center']
                radius = obs['radius']
                u = np.linspace(0, 2 * np.pi, 20)
                v = np.linspace(0, np.pi, 20)
                x = radius * np.outer(np.cos(u), np.sin(v)) + center[0]
                y = radius * np.outer(np.sin(u), np.sin(v)) + center[1]
                z = radius * np.outer(np.ones(np.size(u)), np.cos(v)) + center[2]
                ax.plot_surface(x, y, z, alpha=0.3, color='gray')

            # 绘制轨迹
            if trajectory and len(trajectory) > 1:
                positions = np.array(trajectory)

                # 根据结果设置轨迹样式
                if episode_result == 'success':
                    color = 'blue'
                    alpha = 0.8
                    linewidth = 3
                    linestyle = '-'
                    label = f'Episode {episode+1} - Success'
                elif episode_result == 'collision':
                    color = 'red'
                    alpha = 0.7
                    linewidth = 2
                    linestyle = '--'
                    label = f'Episode {episode+1} - Collision'
                else:  # timeout
                    color = 'orange'
                    alpha = 0.6
                    linewidth = 2
                    linestyle = ':'
                    label = f'Episode {episode+1} - Timeout'

                ax.plot(positions[:, 0], positions[:, 1], positions[:, 2],
                       color=color, alpha=alpha, linewidth=linewidth, linestyle=linestyle, label=label)

                # 标记轨迹起点和终点
                ax.scatter(positions[0, 0], positions[0, 1], positions[0, 2],
                          c='lightgreen', s=100, marker='o', alpha=0.8)
                ax.scatter(positions[-1, 0], positions[-1, 1], positions[-1, 2],
                          c='lightcoral', s=100, marker='x', alpha=0.8)

            # 设置图形属性
            ax.set_xlabel('X (m)')
            ax.set_ylabel('Y (m)')
            ax.set_zlabel('Z (m)')
            ax.set_xlim(0, 100)
            ax.set_ylim(0, 100)
            ax.set_zlim(0, 100)
            ax.grid(True, alpha=0.3)
            ax.legend()

            # 设置标题
            title = f'{phase_name} - Episode {episode+1} - {episode_result.title()}'
            ax.set_title(title, fontsize=14, fontweight='bold')

            # 保存图片
            filename = f'{phase_name}_episode_{episode+1:03d}_3d_trajectory.png'
            filepath = os.path.join(self.full_output_dir, filename)
            plt.savefig(filepath, dpi=150, bbox_inches='tight')
            plt.close()

        except Exception as e:
            print(f"⚠️ 生成3D轨迹图失败: {e}")

    def _save_stage_results(self, stage_num, stage_results, controller):
        """保存阶段训练结果（与原始训练脚本一致）"""
        try:
            stage_prefix = f'stage_{stage_num}'
            training_data = stage_results['training_data']
            final_stats = stage_results['final_stats']

            # 保存模型
            model_path = os.path.join(self.full_output_dir, f'{stage_prefix}_model.pth')
            controller.save_model(model_path)

            # 保存训练数据
            data_path = os.path.join(self.full_output_dir, f'{stage_prefix}_training_data.pkl')
            with open(data_path, 'wb') as f:
                pickle.dump({
                    'training_data': training_data,
                    'final_stats': final_stats
                }, f)

            # 保存JSON报告
            json_path = os.path.join(self.full_output_dir, f'{stage_prefix}_training_report.json')

            # 准备JSON兼容的数据
            json_data = {
                'experiment_info': {
                    'stage_number': stage_num,
                    'environment': stage_results['environment'],
                    'total_episodes': final_stats['total_episodes'],
                    'random_episodes': final_stats['random_episodes'],
                    'fixed_episodes': final_stats['fixed_episodes'],
                    'timestamp': self.timestamp,
                    'training_time_seconds': final_stats['total_training_time'],
                    'visualization_interval': self.visualization_interval,
                    'visualization_count': final_stats['visualization_count']
                },
                'performance_metrics': final_stats,
                'episode_rewards': training_data['episode_rewards'],
                'episode_steps': training_data['episode_steps'],
                'success_episodes': training_data['success_episodes'],
                'collision_episodes': training_data['collision_episodes'],
                'timeout_episodes': training_data['timeout_episodes'],
                'visualization_episodes': training_data['visualization_episodes'],
                'phase_transitions': training_data['phase_transitions'],
                'most_complex_scenario': training_data['most_complex_scenario']
            }

            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, indent=2, ensure_ascii=False)

            # 保存CSV格式的奖励数据
            csv_path = os.path.join(self.full_output_dir, f'{stage_prefix}_training_rewards.csv')
            with open(csv_path, 'w', encoding='utf-8') as f:
                f.write("Episode,Episode_Reward,Steps,Result,Phase\n")
                for i, (ep_reward, steps) in enumerate(zip(training_data['episode_rewards'], training_data['episode_steps'])):
                    # 确定结果类型
                    if i in training_data['success_episodes']:
                        result = 'success'
                    elif i in training_data['collision_episodes']:
                        result = 'collision'
                    else:
                        result = 'timeout'

                    # 确定阶段
                    phase = 'random' if i < final_stats['random_episodes'] else 'fixed'

                    f.write(f"{i+1},{ep_reward:.3f},{steps},{result},{phase}\n")

            print(f"💾 阶段 {stage_num} 训练结果已保存:")
            print(f"  • 模型: {model_path}")
            print(f"  • 数据: {data_path}")
            print(f"  • 报告: {json_path}")
            print(f"  • CSV数据: {csv_path}")
            print(f"  • 3D轨迹图: {final_stats['visualization_count']}个PNG文件")

        except Exception as e:
            print(f"⚠️ 保存阶段结果失败: {e}")

    def _generate_stage_plots(self, stage_num, stage_results):
        """生成阶段训练图表（与原始训练脚本一致）"""
        if not MATPLOTLIB_AVAILABLE:
            return

        try:
            training_data = stage_results['training_data']
            final_stats = stage_results['final_stats']

            # 生成阶段总结图表
            self._generate_phase_summary_plot(stage_num, training_data, final_stats, phase=1)
            self._generate_phase_summary_plot(stage_num, training_data, final_stats, phase=2)

            # 生成两阶段对比图
            self._generate_comparison_plot(stage_num, training_data, final_stats)

        except Exception as e:
            print(f"⚠️ 阶段 {stage_num} 图表生成失败: {e}")

    def _generate_phase_summary_plot(self, stage_num, training_data, final_stats, phase=1):
        """生成单个阶段的训练总结图表（与原始训练脚本一致）"""
        if not MATPLOTLIB_AVAILABLE:
            return

        try:
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))

            # 确定阶段数据范围
            if phase == 1:
                start_idx, end_idx = 0, final_stats['random_episodes']
                phase_name = "第一阶段(随机场景)" if self.chinese_font_available else "Phase 1 (Random Scenarios)"
                episodes_range = f"Episodes 1-{final_stats['random_episodes']}"
            else:
                start_idx, end_idx = final_stats['random_episodes'], len(training_data['episode_rewards'])
                phase_name = "第二阶段(固定场景)" if self.chinese_font_available else "Phase 2 (Fixed Scenario)"
                episodes_range = f"Episodes {final_stats['random_episodes']+1}-{len(training_data['episode_rewards'])}"

            # 提取阶段数据
            phase_rewards = training_data['episode_rewards'][start_idx:end_idx]
            phase_steps = training_data['episode_steps'][start_idx:end_idx]
            phase_episodes = list(range(start_idx, end_idx))

            # 阶段成功episodes
            phase_success_episodes = [ep for ep in training_data['success_episodes'] if start_idx <= ep < end_idx]
            phase_collision_episodes = [ep for ep in training_data['collision_episodes'] if start_idx <= ep < end_idx]
            phase_timeout_episodes = [ep for ep in training_data['timeout_episodes'] if start_idx <= ep < end_idx]

            # 根据字体支持选择标题
            if self.chinese_font_available:
                main_title = f'阶段 {stage_num} 训练总结 - {phase_name}\n{episodes_range} ({len(phase_rewards)} Episodes)'
            else:
                main_title = f'Stage {stage_num} Training Summary - {phase_name}\n{episodes_range} ({len(phase_rewards)} Episodes)'

            fig.suptitle(main_title, fontsize=16, fontweight='bold')

            # 1. Episode奖励趋势
            if phase_rewards:
                axes[0, 0].plot(phase_episodes, phase_rewards, 'b-o', alpha=0.7, markersize=3)

                # 添加趋势线
                if len(phase_rewards) > 1:
                    z = np.polyfit(range(len(phase_rewards)), phase_rewards, 1)
                    p = np.poly1d(z)
                    axes[0, 0].plot(phase_episodes, p(range(len(phase_rewards))),
                                   "r--", alpha=0.8, label=f'趋势: {z[0]:.2f}x+{z[1]:.2f}' if self.chinese_font_available else f'Trend: {z[0]:.2f}x+{z[1]:.2f}')

                axes[0, 0].axhline(np.mean(phase_rewards), color='green', linestyle='--',
                                  label=f'平均: {np.mean(phase_rewards):.1f}' if self.chinese_font_available else f'Mean: {np.mean(phase_rewards):.1f}')

            title1 = 'Episode奖励趋势' if self.chinese_font_available else 'Episode Reward Trend'
            xlabel1 = 'Episode'
            ylabel1 = '奖励' if self.chinese_font_available else 'Reward'

            axes[0, 0].set_title(title1)
            axes[0, 0].set_xlabel(xlabel1)
            axes[0, 0].set_ylabel(ylabel1)
            axes[0, 0].legend()
            axes[0, 0].grid(True, alpha=0.3)

            # 2. 成功率进展
            success_rates = []
            for i in range(len(phase_rewards)):
                success_count = len([ep for ep in phase_success_episodes if ep <= start_idx + i])
                success_rates.append(success_count / (i + 1) if i >= 0 else 0)

            if success_rates:
                axes[0, 1].plot(phase_episodes, success_rates, 'g-o', alpha=0.7, markersize=3)
                axes[0, 1].axhline(np.mean(success_rates), color='red', linestyle='--',
                                  label=f'平均: {np.mean(success_rates):.3f}' if self.chinese_font_available else f'Mean: {np.mean(success_rates):.3f}')

            title2 = '成功率进展' if self.chinese_font_available else 'Success Rate Progress'
            ylabel2 = '成功率' if self.chinese_font_available else 'Success Rate'

            axes[0, 1].set_title(title2)
            axes[0, 1].set_xlabel(xlabel1)
            axes[0, 1].set_ylabel(ylabel2)
            axes[0, 1].set_ylim(0, 1)
            axes[0, 1].legend()
            axes[0, 1].grid(True, alpha=0.3)

            # 3. 步数统计
            if phase_steps:
                axes[1, 0].plot(phase_episodes, phase_steps, 'purple', alpha=0.7, marker='s', markersize=3)
                axes[1, 0].axhline(np.mean(phase_steps), color='orange', linestyle='--',
                                  label=f'平均: {np.mean(phase_steps):.1f}' if self.chinese_font_available else f'Mean: {np.mean(phase_steps):.1f}')

            title3 = '每Episode步数' if self.chinese_font_available else 'Steps per Episode'
            ylabel3 = '步数' if self.chinese_font_available else 'Steps'

            axes[1, 0].set_title(title3)
            axes[1, 0].set_xlabel(xlabel1)
            axes[1, 0].set_ylabel(ylabel3)
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)

            # 4. 结果分布饼图
            success_count = len(phase_success_episodes)
            collision_count = len(phase_collision_episodes)
            timeout_count = len(phase_timeout_episodes)

            if self.chinese_font_available:
                labels = ['成功', '碰撞', '超时']
            else:
                labels = ['Success', 'Collision', 'Timeout']

            sizes = [success_count, collision_count, timeout_count]
            colors = ['lightgreen', 'lightcoral', 'lightyellow']
            explode = (0.1, 0, 0)  # 突出显示成功部分

            # 只显示非零的部分
            non_zero_sizes = []
            non_zero_labels = []
            non_zero_colors = []
            non_zero_explode = []

            for i, size in enumerate(sizes):
                if size > 0:
                    non_zero_sizes.append(size)
                    non_zero_labels.append(f'{labels[i]}\n({size})')
                    non_zero_colors.append(colors[i])
                    non_zero_explode.append(explode[i])

            if non_zero_sizes:
                axes[1, 1].pie(non_zero_sizes, labels=non_zero_labels, colors=non_zero_colors,
                               explode=non_zero_explode, autopct='%1.1f%%', shadow=True, startangle=90)

            title4 = '结果分布' if self.chinese_font_available else 'Result Distribution'
            axes[1, 1].set_title(title4)

            plt.tight_layout()

            # 保存图片
            filename = f'stage_{stage_num}_phase_{phase}_summary.png'
            filepath = os.path.join(self.full_output_dir, filename)
            plt.savefig(filepath, dpi=150, bbox_inches='tight')
            plt.close()

        except Exception as e:
            print(f"⚠️ 阶段 {stage_num} 第 {phase} 阶段总结图生成失败: {e}")

    def _generate_comparison_plot(self, stage_num, training_data, final_stats):
        """生成两个阶段的对比图（与原始训练脚本一致）"""
        if not MATPLOTLIB_AVAILABLE:
            return

        try:
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))

            # 根据字体支持选择标题
            if self.chinese_font_available:
                main_title = f'阶段 {stage_num} 训练对比 - 总计 {final_stats["total_episodes"]} Episodes'
            else:
                main_title = f'Stage {stage_num} Training Comparison - Total {final_stats["total_episodes"]} Episodes'

            fig.suptitle(main_title, fontsize=16, fontweight='bold')

            # 分割数据
            random_episodes = final_stats['random_episodes']
            phase1_rewards = training_data['episode_rewards'][:random_episodes]
            phase2_rewards = training_data['episode_rewards'][random_episodes:]
            phase1_episodes = list(range(0, random_episodes))
            phase2_episodes = list(range(random_episodes, len(training_data['episode_rewards'])))

            # 1. 奖励对比
            if phase1_rewards:
                axes[0, 0].plot(phase1_episodes, phase1_rewards, 'b-', alpha=0.7,
                               label='第一阶段(随机场景)' if self.chinese_font_available else 'Phase 1 (Random)')
            if phase2_rewards:
                axes[0, 0].plot(phase2_episodes, phase2_rewards, 'r-', alpha=0.7,
                               label='第二阶段(固定场景)' if self.chinese_font_available else 'Phase 2 (Fixed)')

            axes[0, 0].axvline(random_episodes, color='gray', linestyle='--', alpha=0.5,
                              label='阶段分界' if self.chinese_font_available else 'Phase Boundary')
            axes[0, 0].set_title('Episode奖励对比' if self.chinese_font_available else 'Episode Reward Comparison')
            axes[0, 0].set_xlabel('Episode')
            axes[0, 0].set_ylabel('奖励' if self.chinese_font_available else 'Reward')
            axes[0, 0].legend()
            axes[0, 0].grid(True, alpha=0.3)

            # 2. 成功率对比
            phase1_success = [ep for ep in training_data['success_episodes'] if ep < random_episodes]
            phase2_success = [ep for ep in training_data['success_episodes'] if ep >= random_episodes]

            phase1_success_rate = len(phase1_success) / random_episodes if random_episodes > 0 else 0
            phase2_success_rate = len(phase2_success) / (len(training_data['episode_rewards']) - random_episodes) if len(training_data['episode_rewards']) > random_episodes else 0

            phases = ['第一阶段\n(随机场景)' if self.chinese_font_available else 'Phase 1\n(Random)',
                     '第二阶段\n(固定场景)' if self.chinese_font_available else 'Phase 2\n(Fixed)']
            success_rates = [phase1_success_rate, phase2_success_rate]

            bars = axes[0, 1].bar(phases, success_rates, color=['blue', 'red'], alpha=0.7)
            axes[0, 1].set_title('阶段成功率对比' if self.chinese_font_available else 'Phase Success Rate Comparison')
            axes[0, 1].set_ylabel('成功率' if self.chinese_font_available else 'Success Rate')
            axes[0, 1].set_ylim(0, 1)

            # 添加数值标签
            for bar, rate in zip(bars, success_rates):
                height = bar.get_height()
                axes[0, 1].text(bar.get_x() + bar.get_width()/2., height + 0.01,
                               f'{rate:.3f}', ha='center', va='bottom')

            # 3. 平均奖励对比
            phase1_avg_reward = np.mean(phase1_rewards) if len(phase1_rewards) > 0 else 0
            phase2_avg_reward = np.mean(phase2_rewards) if len(phase2_rewards) > 0 else 0

            avg_rewards = [phase1_avg_reward, phase2_avg_reward]
            bars2 = axes[1, 0].bar(phases, avg_rewards, color=['lightblue', 'lightcoral'], alpha=0.7)
            axes[1, 0].set_title('平均奖励对比' if self.chinese_font_available else 'Average Reward Comparison')
            axes[1, 0].set_ylabel('平均奖励' if self.chinese_font_available else 'Average Reward')

            # 添加数值标签
            for bar, reward in zip(bars2, avg_rewards):
                height = bar.get_height()
                axes[1, 0].text(bar.get_x() + bar.get_width()/2., height + (abs(height) * 0.01),
                               f'{reward:.1f}', ha='center', va='bottom' if height >= 0 else 'top')

            # 4. 步数对比
            phase1_steps = training_data['episode_steps'][:random_episodes]
            phase2_steps = training_data['episode_steps'][random_episodes:]

            phase1_avg_steps = np.mean(phase1_steps) if len(phase1_steps) > 0 else 0
            phase2_avg_steps = np.mean(phase2_steps) if len(phase2_steps) > 0 else 0

            avg_steps = [phase1_avg_steps, phase2_avg_steps]
            bars3 = axes[1, 1].bar(phases, avg_steps, color=['lightgreen', 'lightyellow'], alpha=0.7)
            axes[1, 1].set_title('平均步数对比' if self.chinese_font_available else 'Average Steps Comparison')
            axes[1, 1].set_ylabel('平均步数' if self.chinese_font_available else 'Average Steps')

            # 添加数值标签
            for bar, steps in zip(bars3, avg_steps):
                height = bar.get_height()
                axes[1, 1].text(bar.get_x() + bar.get_width()/2., height + (height * 0.01),
                               f'{steps:.1f}', ha='center', va='bottom')

            plt.tight_layout()

            # 保存图片
            filename = f'stage_{stage_num}_comparison.png'
            filepath = os.path.join(self.full_output_dir, filename)
            plt.savefig(filepath, dpi=150, bbox_inches='tight')
            plt.close()

        except Exception as e:
            print(f"⚠️ 阶段 {stage_num} 对比图生成失败: {e}")

    def _generate_3d_trajectory_plot(self, env, trajectory, episode, success, phase_name):
        """生成3D轨迹图"""
        try:
            import matplotlib.pyplot as plt
            from mpl_toolkits.mplot3d import Axes3D

            fig = plt.figure(figsize=(12, 10))
            ax = fig.add_subplot(111, projection='3d')

            # 绘制起点和终点
            start = env.start
            goal = env.goal
            ax.scatter(start[0], start[1], start[2], c='green', s=200, marker='o', label='Start', alpha=0.8)
            ax.scatter(goal[0], goal[1], goal[2], c='red', s=200, marker='*', label='Goal', alpha=0.8)

            # 绘制静态障碍物
            for i, obs in enumerate(env.obstacles):
                center = obs['center']
                radius = obs['radius']
                u = np.linspace(0, 2 * np.pi, 20)
                v = np.linspace(0, np.pi, 20)
                x = radius * np.outer(np.cos(u), np.sin(v)) + center[0]
                y = radius * np.outer(np.sin(u), np.sin(v)) + center[1]
                z = radius * np.outer(np.ones(np.size(u)), np.cos(v)) + center[2]
                ax.plot_surface(x, y, z, alpha=0.3, color='gray')

            # 绘制动态障碍物
            for i, obs in enumerate(env.dynamic_obstacles):
                center = obs['center']
                radius = obs['radius']
                u = np.linspace(0, 2 * np.pi, 20)
                v = np.linspace(0, np.pi, 20)
                x = radius * np.outer(np.cos(u), np.sin(v)) + center[0]
                y = radius * np.outer(np.sin(u), np.sin(v)) + center[1]
                z = radius * np.outer(np.ones(np.size(u)), np.cos(v)) + center[2]
                ax.plot_surface(x, y, z, alpha=0.4, color='orange')

            # 绘制轨迹
            if trajectory and len(trajectory) > 1:
                positions = np.array(trajectory)

                if success:
                    color = 'blue'
                    alpha = 0.8
                    linewidth = 3
                    label = f'Episode {episode+1} - Success'
                else:
                    color = 'red'
                    alpha = 0.7
                    linewidth = 2
                    label = f'Episode {episode+1} - Failed'

                ax.plot(positions[:, 0], positions[:, 1], positions[:, 2],
                       color=color, alpha=alpha, linewidth=linewidth, label=label)

                # 标记轨迹起点和终点
                ax.scatter(positions[0, 0], positions[0, 1], positions[0, 2],
                          c='lightgreen', s=100, marker='o', alpha=0.8)
                ax.scatter(positions[-1, 0], positions[-1, 1], positions[-1, 2],
                          c='lightcoral', s=100, marker='x', alpha=0.8)

            # 设置图形属性
            ax.set_xlabel('X (m)')
            ax.set_ylabel('Y (m)')
            ax.set_zlabel('Z (m)')
            ax.set_xlim(0, 100)
            ax.set_ylim(0, 100)
            ax.set_zlim(0, 100)
            ax.grid(True, alpha=0.3)
            ax.legend()

            # 设置标题
            ax.set_title(f'{phase_name} - Episode {episode+1} - {"Success" if success else "Failed"}',
                        fontsize=14, fontweight='bold')

            # 保存图片
            filename = f'{phase_name}_episode_{episode+1:03d}_3d_trajectory.png'
            filepath = os.path.join(self.output_dir, filename)
            plt.savefig(filepath, dpi=150, bbox_inches='tight')
            plt.close()

        except Exception as e:
            print(f"⚠️ 生成3D轨迹图失败: {e}")

    def _run_final_test(self, controller):
        """运行最终测试"""
        if controller is None:
            return {"error": "No trained controller available"}

        test_results = {}

        # 在所有环境中测试
        test_environments = ["stage1_simple", "stage2_complex", "stage3_dynamic"]

        for env_name in test_environments:
            print(f"\n测试环境: {env_name}")
            env_config = get_environment_config(env_name)

            # 创建测试环境
            test_env = StabilizedRewardEnvironment(
                bounds=[100, 100, 100],
                environment_config=env_config,
                reward_type='simplified'
            )

            # 运行测试episodes
            test_episodes = 10
            success_count = 0
            total_rewards = []
            completion_times = []

            for episode in range(test_episodes):
                state = test_env.reset()
                full_state = np.concatenate([test_env.state, state[6:]])

                episode_reward = 0
                step_count = 0

                while step_count < 500:
                    # 获取动作（测试时不添加噪声）
                    action, info, safe_actions = controller.get_action_with_quality(
                        full_state, test_env.goal, test_env.obstacles, add_noise=False
                    )

                    # 执行动作
                    next_state, reward, done, env_info = test_env.step(action)
                    next_full_state = np.concatenate([test_env.state, next_state[6:]])

                    episode_reward += reward
                    step_count += 1

                    if done:
                        if env_info.get('success', False):
                            success_count += 1
                            completion_times.append(step_count * 0.1)  # 转换为秒
                        break

                    full_state = next_full_state

                total_rewards.append(episode_reward)

            # 计算测试结果
            test_results[env_name] = {
                "success_rate": success_count / test_episodes,
                "avg_reward": np.mean(total_rewards),
                "avg_completion_time": np.mean(completion_times) if completion_times else None,
                "total_episodes": test_episodes
            }

            print(f"   成功率: {test_results[env_name]['success_rate']:.2%}")
            print(f"   平均奖励: {test_results[env_name]['avg_reward']:.1f}")
            if completion_times:
                print(f"   平均完成时间: {test_results[env_name]['avg_completion_time']:.1f}秒")

        return test_results

    def _print_training_summary(self):
        """打印训练总结"""
        print(f"\n📋 分阶段训练总结")
        print("=" * 60)

        for stage_key, stage_result in self.training_results["stages"].items():
            if "error" not in stage_result:
                print(f"{stage_key}:")
                print(f"  • 随机场景成功率: {stage_result['final_stats']['phase1_success_rate']:.2%}")
                print(f"  • 固定场景成功率: {stage_result['final_stats']['phase2_success_rate']:.2%}")
                print(f"  • 总体成功率: {stage_result['final_stats']['overall_success_rate']:.2%}")
                print(f"  • 平均奖励: {stage_result['final_stats']['avg_reward']:.1f}")
                print(f"  • 训练时间: {stage_result['final_stats']['total_training_time']:.1f}秒")
                print(f"  • 可视化图表: {stage_result['final_stats']['visualization_count']}个3D轨迹图 + 3个总结图表")
            else:
                print(f"{stage_key}: 失败 - {stage_result['error']}")

        if "final_test" in self.training_results and "error" not in self.training_results["final_test"]:
            print(f"\n🧪 最终测试结果:")
            for env_name, result in self.training_results["final_test"].items():
                print(f"  {env_name}: 成功率 {result['success_rate']:.2%}")

        print(f"\n⏱️ 总训练时间: {self.training_results.get('total_training_time', 0):.1f}秒")
        print(f"📁 结果保存在: {self.full_output_dir}")

def main():
    parser = argparse.ArgumentParser(description='DWA-RL分阶段训练 - 简化ver1')
    parser.add_argument('--start-stage', type=int, default=1, choices=[1, 2, 3],
                       help='开始阶段 (1: 简单, 2: 复杂, 3: 动态)')
    parser.add_argument('--end-stage', type=int, default=3, choices=[1, 2, 3],
                       help='结束阶段 (1: 简单, 2: 复杂, 3: 动态)')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    parser.add_argument('--viz-interval', type=int, default=20, help='3D轨迹图生成间隔')
    parser.add_argument('--show-configs', action='store_true', help='显示所有可用配置')

    args = parser.parse_args()

    if args.show_configs:
        print_all_configs()
        return

    # 显示训练计划
    print("📋 分阶段训练计划:")
    print("=" * 40)
    stage_names = {1: "简单环境", 2: "复杂环境", 3: "动态环境"}
    for stage in range(args.start_stage, args.end_stage + 1):
        stage_config = get_training_stage_config(f"stage{stage}_{'simple' if stage==1 else 'complex' if stage==2 else 'dynamic'}")
        print(f"阶段 {stage}: {stage_names[stage]}")
        print(f"  • 固定场景: {stage_config['fixed_episodes']} episodes")
        print(f"  • 随机场景: {stage_config['random_episodes']} episodes")
        print(f"  • 总计: {stage_config['total_episodes']} episodes")
    print()

    # 执行分阶段训练
    trainer = StagedTrainer(
        start_stage=args.start_stage,
        end_stage=args.end_stage,
        seed=args.seed,
        visualization_interval=args.viz_interval
    )

    results = trainer.run_staged_training()

    print("\n✅ 分阶段训练完成!")

if __name__ == "__main__":
    main()
