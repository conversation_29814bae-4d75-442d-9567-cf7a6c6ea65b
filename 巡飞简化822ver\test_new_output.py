"""
测试新的简洁输出格式
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from staged_training_framework import LoiteringMunitionStagedTrainer

def test_new_output():
    """测试新的输出格式"""
    print("🧪 测试新的简洁输出格式")
    print("=" * 50)
    
    try:
        # 创建测试训练器（只运行少量episodes）
        trainer = LoiteringMunitionStagedTrainer(
            start_stage=1,
            end_stage=1,
            seed=42,
            visualization_interval=20  # 减少可视化频率
        )

        # 修改配置为超快速测试
        from environment_config import TRAINING_STAGES
        TRAINING_STAGES["stage1_simple"]["random_episodes"] = 10
        TRAINING_STAGES["stage1_simple"]["fixed_episodes"] = 5
        TRAINING_STAGES["stage1_simple"]["total_episodes"] = 15

        print("⚡ 使用超快速测试配置（15个episodes）")
        print("📊 测试新的输出格式...")

        # 执行训练
        results, final_controller = trainer.run_staged_training()

        print("\n✅ 输出格式测试完成!")
        return results, final_controller

    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_new_output()
