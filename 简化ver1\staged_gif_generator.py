"""
分阶段训练GIF生成器 - 简化ver1
自动检测最新的staged_training训练结果并生成导航动画
基于分层ver的fixed_gif_generator.py修改
"""

import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.animation as animation
import os
import glob
import json
from datetime import datetime

# 设置matplotlib使用英文字体，避免中文字体问题
plt.rcParams['font.family'] = ['DejaVu Sans', 'Arial', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

from dwa_rl_core import StabilizedRewardEnvironment, StabilizedTD3Controller, td3_config
from environment_config import get_environment_config

def generate_staged_gif(model_path, environment_config, max_steps=400, fps=12):
    """生成分阶段训练结果的导航GIF"""
    print("🎬 Staged Training GIF Generator")
    print("=" * 60)
    print(f"Model: {os.path.basename(model_path)}")
    print(f"Environment: {environment_config.get('description', 'Unknown')}")
    print(f"Steps: {max_steps} | FPS: {fps}")
    print(f"Dynamic Obstacles: {'ON' if environment_config.get('enable_dynamic_obstacles', False) else 'OFF'}")

    # 显示环境详细配置
    static_count = environment_config.get('static_obstacle_count', (0, 0))
    dynamic_count = environment_config.get('dynamic_obstacle_count', (0, 0))
    print(f"Static Obstacles: {static_count[0]}-{static_count[1]}")
    print(f"Dynamic Obstacles: {dynamic_count[0]}-{dynamic_count[1]}")
    print("=" * 60)

    # 加载模型和环境
    controller = StabilizedTD3Controller(td3_config)
    controller.load_model(model_path)

    # 使用训练时的完整环境配置创建环境
    env = StabilizedRewardEnvironment(
        bounds=[100, 100, 100],
        environment_config=environment_config,
        reward_type='simplified'
    )

    # 验证环境配置是否正确应用
    print(f"✅ Environment created with:")
    print(f"   Static obstacles: {len(env.obstacles)}")
    if hasattr(env, 'dynamic_obstacles') and env.dynamic_obstacles:
        print(f"   Dynamic obstacles: {len(env.dynamic_obstacles)}")
    else:
        print(f"   Dynamic obstacles: 0")
    print(f"   Start: {env.start}")
    print(f"   Goal: {env.goal}")
    
    # 收集导航数据
    nav_data = collect_navigation_data(env, controller, max_steps)

    # 创建GIF
    gif_path = create_staged_gif(env, nav_data, fps, environment_config)

    # 生成约束分析图
    stage_name = environment_config.get('description', 'Unknown').split('：')[0] if '：' in environment_config.get('description', '') else 'Stage'
    constraint_plot_path = generate_constraint_plots(nav_data, stage_name)

    # 生成可交互的3D图窗
    interactive_3d_path = generate_interactive_3d_plot(nav_data, stage_name, environment_config)

    return gif_path, constraint_plot_path, interactive_3d_path

def collect_navigation_data(env, controller, max_steps):
    """收集导航数据"""
    state = env.reset()
    full_state = np.concatenate([env.state, state[6:]])
    
    data = {
        'positions': [],
        'velocities': [],
        'accelerations': [],
        'goal_distances': [],
        'rewards': [],
        'dynamic_obs_positions': [],
        'velocity_magnitudes': [],
        'acceleration_magnitudes': [],
        'constraint_violations': [],
        'timestamps': [],
        'obstacles': env.obstacles,
        'dynamic_obstacles': getattr(env, 'dynamic_obstacles', []),
        'start': env.start.copy(),
        'goal': env.goal.copy()
    }
    
    prev_velocity = np.zeros(3)
    dt = 0.1
    
    print("📊 Collecting navigation data...")
    print(f"🎯 Target: {env.goal}")
    print(f"🚁 Start: {env.state[:3]}")
    initial_goal_dist = np.linalg.norm(env.state[:3] - env.goal)
    print(f"📏 Initial distance to goal: {initial_goal_dist:.1f}m")

    for step in range(max_steps):
        current_pos = env.state[:3].copy()
        current_vel = env.state[3:6].copy()
        acceleration = (current_vel - prev_velocity) / dt
        goal_dist = np.linalg.norm(current_pos - env.goal)

        # 记录数据
        data['positions'].append(current_pos)
        data['velocities'].append(current_vel)
        data['accelerations'].append(acceleration)
        data['goal_distances'].append(goal_dist)
        data['timestamps'].append(step * dt)

        # 记录约束量
        vel_magnitude = np.linalg.norm(current_vel)
        acc_magnitude = np.linalg.norm(acceleration)
        data['velocity_magnitudes'].append(vel_magnitude)
        data['acceleration_magnitudes'].append(acc_magnitude)

        # 检查约束违反
        max_vel = 5.0  # 最大速度限制
        max_acc = 8.0  # 最大加速度限制
        violations = {
            'velocity': vel_magnitude > max_vel,
            'acceleration': acc_magnitude > max_acc
        }
        data['constraint_violations'].append(violations)

        # 记录动态障碍物位置
        if hasattr(env, 'dynamic_obstacles') and env.dynamic_obstacles:
            dyn_pos = [obs['center'].copy() for obs in env.dynamic_obstacles]
            data['dynamic_obs_positions'].append(dyn_pos)
        else:
            data['dynamic_obs_positions'].append([])

        # 获取动作并执行
        action, _, _ = controller.get_action_with_quality(
            full_state, env.goal, env.obstacles, add_noise=False  # 测试时不添加噪声
        )

        next_state, reward, done, env_info = env.step(action)
        next_full_state = np.concatenate([env.state, next_state[6:]])

        data['rewards'].append(reward)

        # 更详细的进度报告
        if step % 10 == 0 or goal_dist < 5.0:
            vel_mag = np.linalg.norm(current_vel)
            acc_mag = np.linalg.norm(acceleration)
            progress = (initial_goal_dist - goal_dist) / initial_goal_dist * 100
            print(f"  Step {step:3d}: Goal={goal_dist:5.1f}m | Progress={progress:5.1f}% | Vel={vel_mag:.1f}m/s | Acc={acc_mag:.1f}m/s²")

        # 检查是否到达目标
        if done:
            if goal_dist < 2.0:  # 成功到达
                print(f"🎉 SUCCESS! Reached goal at step {step} (distance: {goal_dist:.1f}m)")
            else:
                print(f"⚠️  Episode ended at step {step} (reason: {env_info.get('reason', 'unknown')})")
            break

        # 检查是否接近目标
        if goal_dist < 1.0:
            print(f"🎯 Very close to goal! Distance: {goal_dist:.2f}m")

        full_state = next_full_state
        prev_velocity = current_vel
    
    print(f"📈 Collected {len(data['positions'])} navigation steps")
    return data

def create_staged_gif(env, data, fps, environment_config):
    """创建分阶段训练GIF动画"""
    print("🎨 Creating staged training GIF animation...")
    
    # 设置图形 - 使用与训练一致的尺寸
    fig = plt.figure(figsize=(12, 10))
    ax = fig.add_subplot(111, projection='3d')
    
    # 绘制静态环境
    setup_staged_environment(ax, env, environment_config)
    
    # 初始化动画元素
    trajectory_line, = ax.plot([], [], [], 'b-', linewidth=3, alpha=0.9, label='UAV Path')
    uav_marker = ax.scatter([], [], [], c='blue', s=150, marker='D', 
                           edgecolors='navy', linewidth=2, label='UAV')
    
    # 信息显示
    info_text = ax.text2D(0.02, 0.98, '', transform=ax.transAxes, fontsize=10,
                         verticalalignment='top', fontweight='bold',
                         bbox=dict(boxstyle='round,pad=0.4', facecolor='lightcyan', alpha=0.9),
                         family='monospace')
    
    # 动态障碍物容器
    dynamic_surfaces = []
    
    def animate_frame(frame):
        """动画帧更新函数"""
        if frame >= len(data['positions']):
            return trajectory_line, uav_marker, info_text
        
        # 更新轨迹
        positions = np.array(data['positions'][:frame+1])
        if len(positions) > 1:
            trajectory_line.set_data_3d(positions[:, 0], positions[:, 1], positions[:, 2])
        
        # 更新无人机位置
        current_pos = data['positions'][frame]
        uav_marker._offsets3d = ([current_pos[0]], [current_pos[1]], [current_pos[2]])
        
        # 更新动态障碍物
        nonlocal dynamic_surfaces
        for surface in dynamic_surfaces:
            surface.remove()
        dynamic_surfaces.clear()
        
        enable_dynamic = environment_config.get('enable_dynamic_obstacles', False)
        if enable_dynamic and frame < len(data['dynamic_obs_positions']):
            dyn_positions = data['dynamic_obs_positions'][frame]
            for i, pos in enumerate(dyn_positions):
                if hasattr(env, 'dynamic_obstacles') and i < len(env.dynamic_obstacles):
                    radius = env.dynamic_obstacles[i]['radius']
                    # 使用较少的点数提高性能
                    u = np.linspace(0, 2 * np.pi, 8)
                    v = np.linspace(0, np.pi, 8)
                    x = radius * np.outer(np.cos(u), np.sin(v)) + pos[0]
                    y = radius * np.outer(np.sin(u), np.sin(v)) + pos[1]
                    z = radius * np.outer(np.ones(np.size(u)), np.cos(v)) + pos[2]
                    surface = ax.plot_surface(x, y, z, alpha=0.7, color='orange', 
                                            edgecolor='red', linewidth=0.5)
                    dynamic_surfaces.append(surface)
        
        # 更新信息显示
        velocity = data['velocities'][frame]
        acceleration = data['accelerations'][frame]
        goal_dist = data['goal_distances'][frame]
        reward = data['rewards'][frame] if frame < len(data['rewards']) else 0
        
        vel_mag = np.linalg.norm(velocity)
        acc_mag = np.linalg.norm(acceleration)
        
        # 信息显示格式
        info_content = f"""Step: {frame + 1:3d}/{len(data['positions'])}
Pos: [{current_pos[0]:5.1f}, {current_pos[1]:5.1f}, {current_pos[2]:5.1f}]

Velocity: {vel_mag:5.2f} m/s
  Vx: {velocity[0]:+6.2f}  Vy: {velocity[1]:+6.2f}  Vz: {velocity[2]:+6.2f}

Acceleration: {acc_mag:5.2f} m/s²
  Ax: {acceleration[0]:+6.2f}  Ay: {acceleration[1]:+6.2f}  Az: {acceleration[2]:+6.2f}

Goal Distance: {goal_dist:6.1f} m
Reward: {reward:8.1f}"""
        
        info_text.set_text(info_content)
        
        # 更新标题 - 使用英文避免字体问题
        env_desc = 'Staged Training Navigation'
        if environment_config.get('enable_dynamic_obstacles', False):
            env_desc = 'Stage 3: Dynamic Environment'
        elif environment_config.get('static_obstacle_count', (0, 0))[1] > 8:
            env_desc = 'Stage 2: Complex Environment'
        else:
            env_desc = 'Stage 1: Simple Environment'

        ax.set_title(f'{env_desc} | Step {frame + 1} | Vel: {vel_mag:.1f}m/s | Acc: {acc_mag:.1f}m/s²',
                    fontsize=12, fontweight='bold')
        
        return trajectory_line, uav_marker, info_text
    
    # 创建动画
    print(f"🎨 Creating animation with {len(data['positions'])} frames...")
    anim = animation.FuncAnimation(fig, animate_frame, frames=len(data['positions']), 
                                 interval=1000//fps, blit=False, repeat=True)
    
    # 保存GIF
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    # 使用简化的英文环境名称避免中文字体问题
    env_mapping = {
        'stage1_simple': 'Stage1_Simple',
        'stage2_complex': 'Stage2_Complex',
        'stage3_dynamic': 'Stage3_Dynamic'
    }

    # 从环境配置中推断环境类型
    env_name = 'Unknown'
    for key, value in env_mapping.items():
        if key in str(environment_config):
            env_name = value
            break

    # 如果无法推断，使用默认名称
    if env_name == 'Unknown':
        if environment_config.get('enable_dynamic_obstacles', False):
            env_name = 'Stage3_Dynamic'
        elif environment_config.get('static_obstacle_count', (0, 0))[1] > 8:
            env_name = 'Stage2_Complex'
        else:
            env_name = 'Stage1_Simple'

    gif_filename = f"staged_navigation_{env_name}_{timestamp}.gif"
    
    print(f"💾 Saving GIF: {gif_filename}")
    
    # 使用优化的设置保存
    writer = animation.PillowWriter(fps=fps)
    anim.save(gif_filename, writer=writer, dpi=100)
    
    plt.close()
    
    # 打印统计信息
    print_navigation_stats(data, gif_filename)
    
    return gif_filename

def generate_constraint_plots(nav_data, stage_name):
    """生成约束曲线图"""
    timestamps = np.array(nav_data['timestamps'])
    vel_magnitudes = np.array(nav_data['velocity_magnitudes'])
    acc_magnitudes = np.array(nav_data['acceleration_magnitudes'])
    goal_distances = np.array(nav_data['goal_distances'])

    # 创建子图
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle(f'{stage_name} - Navigation Constraints Analysis', fontsize=16, fontweight='bold')

    # 1. 速度曲线
    ax1.plot(timestamps, vel_magnitudes, 'b-', linewidth=2, label='Velocity Magnitude')
    ax1.axhline(y=5.0, color='r', linestyle='--', alpha=0.7, label='Max Velocity (5.0 m/s)')
    ax1.set_xlabel('Time (s)')
    ax1.set_ylabel('Velocity (m/s)')
    ax1.set_title('Velocity Constraints')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    ax1.set_ylim(0, max(6.0, max(vel_magnitudes) * 1.1))

    # 2. 加速度曲线
    ax2.plot(timestamps, acc_magnitudes, 'g-', linewidth=2, label='Acceleration Magnitude')
    ax2.axhline(y=8.0, color='r', linestyle='--', alpha=0.7, label='Max Acceleration (8.0 m/s²)')
    ax2.set_xlabel('Time (s)')
    ax2.set_ylabel('Acceleration (m/s²)')
    ax2.set_title('Acceleration Constraints')
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    ax2.set_ylim(0, max(9.0, max(acc_magnitudes) * 1.1))

    # 3. 目标距离
    ax3.plot(timestamps, goal_distances, 'm-', linewidth=2, label='Distance to Goal')
    ax3.set_xlabel('Time (s)')
    ax3.set_ylabel('Distance (m)')
    ax3.set_title('Goal Distance Progress')
    ax3.grid(True, alpha=0.3)
    ax3.legend()

    # 4. 约束违反统计
    vel_violations = [v['velocity'] for v in nav_data['constraint_violations']]
    acc_violations = [v['acceleration'] for v in nav_data['constraint_violations']]

    violation_times = []
    violation_types = []
    for i, (vel_viol, acc_viol) in enumerate(zip(vel_violations, acc_violations)):
        if vel_viol:
            violation_times.append(timestamps[i])
            violation_types.append('Velocity')
        if acc_viol:
            violation_times.append(timestamps[i])
            violation_types.append('Acceleration')

    if violation_times:
        vel_count = violation_types.count('Velocity')
        acc_count = violation_types.count('Acceleration')
        ax4.bar(['Velocity', 'Acceleration'], [vel_count, acc_count],
                color=['red', 'orange'], alpha=0.7)
        ax4.set_ylabel('Violation Count')
        ax4.set_title('Constraint Violations')
    else:
        ax4.text(0.5, 0.5, 'No Constraint\nViolations',
                ha='center', va='center', transform=ax4.transAxes,
                fontsize=14, color='green', fontweight='bold')
        ax4.set_title('Constraint Violations')

    ax4.grid(True, alpha=0.3)

    plt.tight_layout()

    # 保存图片
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    plot_filename = f"constraint_analysis_{stage_name}_{timestamp}.png"
    plt.savefig(plot_filename, dpi=150, bbox_inches='tight')
    plt.close()

    print(f"📊 Constraint analysis saved: {plot_filename}")
    return plot_filename

def generate_interactive_3d_plot(nav_data, stage_name, environment_config):
    """生成可交互的3D导航轨迹图"""
    from mpl_toolkits.mplot3d import Axes3D

    # 创建3D图形
    fig = plt.figure(figsize=(12, 9))
    ax = fig.add_subplot(111, projection='3d')

    # 获取数据
    positions = np.array(nav_data['positions'])
    velocities = np.array(nav_data['velocity_magnitudes'])
    start = nav_data['start']
    goal = nav_data['goal']
    obstacles = nav_data['obstacles']
    dynamic_obstacles = nav_data.get('dynamic_obstacles', [])

    # 绘制轨迹 - 使用速度进行颜色映射
    if len(positions) > 0:
        x, y, z = positions[:, 0], positions[:, 1], positions[:, 2]

        # 创建颜色映射
        scatter = ax.scatter(x, y, z, c=velocities, cmap='viridis',
                           s=30, alpha=0.8, label='Trajectory')

        # 添加轨迹线
        ax.plot(x, y, z, 'b-', alpha=0.6, linewidth=2)

        # 添加颜色条
        cbar = plt.colorbar(scatter, ax=ax, shrink=0.8, aspect=20)
        cbar.set_label('Velocity (m/s)', fontsize=12)

    # 绘制起点和终点
    ax.scatter(*start, color='green', s=200, marker='o',
              label='Start', edgecolors='black', linewidth=2)
    ax.scatter(*goal, color='red', s=200, marker='*',
              label='Goal', edgecolors='black', linewidth=2)

    # 绘制静态障碍物
    for obs in obstacles:
        center = obs['center']
        radius = obs['radius']

        # 创建球体
        u = np.linspace(0, 2 * np.pi, 20)
        v = np.linspace(0, np.pi, 20)
        x_sphere = radius * np.outer(np.cos(u), np.sin(v)) + center[0]
        y_sphere = radius * np.outer(np.sin(u), np.sin(v)) + center[1]
        z_sphere = radius * np.outer(np.ones(np.size(u)), np.cos(v)) + center[2]

        ax.plot_surface(x_sphere, y_sphere, z_sphere,
                       alpha=0.6, color='gray', edgecolor='none')

    # 绘制动态障碍物（如果有）
    for obs in dynamic_obstacles:
        center = obs['center']
        radius = obs.get('radius', 3.0)

        # 创建球体
        u = np.linspace(0, 2 * np.pi, 15)
        v = np.linspace(0, np.pi, 15)
        x_sphere = radius * np.outer(np.cos(u), np.sin(v)) + center[0]
        y_sphere = radius * np.outer(np.sin(u), np.sin(v)) + center[1]
        z_sphere = radius * np.outer(np.ones(np.size(u)), np.cos(v)) + center[2]

        ax.plot_surface(x_sphere, y_sphere, z_sphere,
                       alpha=0.7, color='orange', edgecolor='none')

    # 设置坐标轴
    ax.set_xlabel('X (m)', fontsize=12)
    ax.set_ylabel('Y (m)', fontsize=12)
    ax.set_zlabel('Z (m)', fontsize=12)

    # 设置标题
    description = environment_config.get('description', 'Unknown Environment')
    ax.set_title(f'{stage_name} - 3D Navigation Trajectory\n{description}',
                fontsize=14, fontweight='bold', pad=20)

    # 设置坐标轴范围
    ax.set_xlim(0, 100)
    ax.set_ylim(0, 100)
    ax.set_zlim(0, 100)

    # 添加图例
    ax.legend(loc='upper left', bbox_to_anchor=(0, 1))

    # 设置视角
    ax.view_init(elev=20, azim=45)

    # 添加网格
    ax.grid(True, alpha=0.3)

    # 保存为可交互的HTML文件（使用plotly）
    try:
        import plotly.graph_objects as go
        import plotly.offline as pyo

        # 创建plotly图形
        plotly_fig = go.Figure()

        # 添加轨迹
        if len(positions) > 0:
            plotly_fig.add_trace(go.Scatter3d(
                x=x, y=y, z=z,
                mode='markers+lines',
                marker=dict(
                    size=4,
                    color=velocities,
                    colorscale='Viridis',
                    colorbar=dict(title="Velocity (m/s)"),
                    showscale=True
                ),
                line=dict(color='blue', width=3),
                name='Trajectory'
            ))

        # 添加起点和终点
        plotly_fig.add_trace(go.Scatter3d(
            x=[start[0]], y=[start[1]], z=[start[2]],
            mode='markers',
            marker=dict(size=10, color='green', symbol='circle'),
            name='Start'
        ))

        plotly_fig.add_trace(go.Scatter3d(
            x=[goal[0]], y=[goal[1]], z=[goal[2]],
            mode='markers',
            marker=dict(size=12, color='red', symbol='diamond'),
            name='Goal'
        ))

        # 设置布局
        plotly_fig.update_layout(
            title=f'{stage_name} - Interactive 3D Navigation<br>{description}',
            scene=dict(
                xaxis_title='X (m)',
                yaxis_title='Y (m)',
                zaxis_title='Z (m)',
                xaxis=dict(range=[0, 100]),
                yaxis=dict(range=[0, 100]),
                zaxis=dict(range=[0, 100])
            ),
            width=1000,
            height=800
        )

        # 保存为HTML文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        html_filename = f"interactive_3d_{stage_name}_{timestamp}.html"
        pyo.plot(plotly_fig, filename=html_filename, auto_open=False)
        print(f"🌐 Interactive 3D plot saved: {html_filename}")

    except ImportError:
        print("⚠️ Plotly not available, saving static 3D plot only")
        html_filename = None

    # 保存静态3D图
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    static_filename = f"static_3d_{stage_name}_{timestamp}.png"
    plt.savefig(static_filename, dpi=150, bbox_inches='tight')
    plt.close()

    print(f"🎨 Static 3D plot saved: {static_filename}")

    return html_filename or static_filename

def setup_staged_environment(ax, env, environment_config):
    """设置分阶段训练环境视角"""
    # 绘制起点和终点
    start, goal = env.start, env.goal
    ax.scatter(start[0], start[1], start[2], c='green', s=200, marker='o',
              label='Start', alpha=0.8, edgecolors='darkgreen', linewidth=2)
    ax.scatter(goal[0], goal[1], goal[2], c='red', s=200, marker='*',
              label='Goal', alpha=0.8, edgecolors='darkred', linewidth=2)

    # 绘制静态障碍物
    for obs in env.obstacles:
        center = obs['center']
        radius = obs['radius']
        u = np.linspace(0, 2 * np.pi, 15)
        v = np.linspace(0, np.pi, 15)
        x = radius * np.outer(np.cos(u), np.sin(v)) + center[0]
        y = radius * np.outer(np.sin(u), np.sin(v)) + center[1]
        z = radius * np.outer(np.ones(np.size(u)), np.cos(v)) + center[2]
        ax.plot_surface(x, y, z, alpha=0.4, color='gray',
                       edgecolor='black', linewidth=0.5)

    # 设置坐标轴
    ax.set_xlabel('X (m)', fontsize=12)
    ax.set_ylabel('Y (m)', fontsize=12)
    ax.set_zlabel('Z (m)', fontsize=12)
    ax.set_xlim(0, 100)
    ax.set_ylim(0, 100)
    ax.set_zlim(0, 100)
    ax.grid(True, alpha=0.3)
    ax.legend(loc='upper right', fontsize=10)

def print_navigation_stats(data, gif_filename):
    """打印导航统计信息"""
    positions = np.array(data['positions'])
    velocities = np.array(data['velocities'])
    accelerations = np.array(data['accelerations'])

    if len(positions) > 1:
        path_length = np.sum(np.linalg.norm(np.diff(positions, axis=0), axis=1))
        avg_vel = np.mean([np.linalg.norm(v) for v in velocities])
        max_vel = np.max([np.linalg.norm(v) for v in velocities])
        avg_acc = np.mean([np.linalg.norm(a) for a in accelerations])
        max_acc = np.max([np.linalg.norm(a) for a in accelerations])

        initial_dist = data['goal_distances'][0]
        final_dist = data['goal_distances'][-1]
        improvement = initial_dist - final_dist

        print(f"\n📊 Navigation Statistics:")
        print("=" * 40)
        print(f"Steps: {len(positions)}")
        print(f"Path Length: {path_length:.1f}m")
        print(f"Velocity - Avg: {avg_vel:.2f}m/s | Max: {max_vel:.2f}m/s")
        print(f"Acceleration - Avg: {avg_acc:.2f}m/s² | Max: {max_acc:.2f}m/s²")
        print(f"Goal Distance - Initial: {initial_dist:.1f}m | Final: {final_dist:.1f}m")
        print(f"Distance Improvement: {improvement:.1f}m")
        print(f"GIF File: {gif_filename}")
        print("=" * 40)

def find_latest_staged_training():
    """查找最新的分阶段训练结果"""
    # 获取当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))

    # 查找所有staged_training目录
    pattern = os.path.join(current_dir, 'staged_training_*')
    training_dirs = glob.glob(pattern)

    print(f"[INFO] Searching in: {current_dir}")
    print(f"[INFO] Pattern: {pattern}")
    print(f"[INFO] Found directories: {training_dirs}")

    if not training_dirs:
        # 尝试在当前工作目录查找
        training_dirs = glob.glob('staged_training_*')
        print(f"[INFO] Fallback search found: {training_dirs}")

        if not training_dirs:
            return None, None, None

    # 过滤掉非目录的文件（如.py文件）
    training_dirs = [d for d in training_dirs if os.path.isdir(d)]

    if not training_dirs:
        print("[ERROR] No valid training directories found")
        return None, None, None

    # 按时间戳排序，获取最新的
    latest_dir = max(training_dirs, key=lambda x: os.path.getctime(x))

    print(f"[INFO] Found latest training directory: {latest_dir}")

    # 查找该目录中的模型文件和报告
    model_files = glob.glob(os.path.join(latest_dir, 'stage_*_model.pth'))
    report_files = glob.glob(os.path.join(latest_dir, 'stage_*_training_report.json'))

    print(f"[INFO] Model files found: {model_files}")
    print(f"[INFO] Report files found: {report_files}")

    if not model_files:
        print(f"[ERROR] No model files found in {latest_dir}")
        # 列出目录内容进行调试
        try:
            dir_contents = os.listdir(latest_dir)
            print(f"[INFO] Directory contents: {dir_contents}")
        except Exception as e:
            print(f"[ERROR] Cannot list directory contents: {e}")
        return None, None, None

    # 获取最新阶段的模型（按文件名中的阶段号排序）
    def extract_stage_number(filename):
        import re
        match = re.search(r'stage_(\d+)_', os.path.basename(filename))
        return int(match.group(1)) if match else 0

    latest_model = max(model_files, key=extract_stage_number)

    # 尝试找到对应的报告文件
    stage_num = extract_stage_number(latest_model)
    corresponding_report = None

    for report_file in report_files:
        if f'stage_{stage_num}_' in os.path.basename(report_file):
            corresponding_report = report_file
            break

    if not corresponding_report and report_files:
        corresponding_report = report_files[0]  # 使用第一个可用的报告

    # 读取训练报告获取环境信息
    environment_config = None
    if corresponding_report:
        try:
            with open(corresponding_report, 'r', encoding='utf-8') as f:
                report_data = json.load(f)

            environment_name = report_data.get('experiment_info', {}).get('environment', 'stage1_simple')
            environment_config = get_environment_config(environment_name)

            print(f"[INFO] Latest model: {os.path.basename(latest_model)}")
            print(f"[INFO] Environment: {environment_name}")
            print(f"[INFO] Report: {os.path.basename(corresponding_report)}")

        except Exception as e:
            print(f"[WARNING] Error reading report file: {e}")
            environment_config = None

    # 如果无法读取报告，根据阶段号推断环境
    if environment_config is None:
        stage_num = extract_stage_number(latest_model)
        if stage_num == 1:
            environment_name = 'stage1_simple'
        elif stage_num == 2:
            environment_name = 'stage2_complex'
        elif stage_num == 3:
            environment_name = 'stage3_dynamic'
        else:
            environment_name = 'stage1_simple'

        environment_config = get_environment_config(environment_name)
        print(f"[INFO] Latest model: {os.path.basename(latest_model)}")
        print(f"[INFO] Environment (inferred): {environment_name}")

        # 验证推断的环境配置
        static_count = environment_config.get('static_obstacle_count', (0, 0))
        dynamic_count = environment_config.get('dynamic_obstacle_count', (0, 0))
        print(f"[INFO] Static obstacles: {static_count[0]}-{static_count[1]}")
        print(f"[INFO] Dynamic obstacles: {dynamic_count[0]}-{dynamic_count[1]}")
        print(f"[INFO] Enable dynamic: {environment_config.get('enable_dynamic_obstacles', False)}")

    return latest_model, environment_config, latest_dir

def find_all_staged_models():
    """查找所有阶段的模型文件"""
    # 获取当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))

    # 查找所有staged_training目录
    pattern = os.path.join(current_dir, 'staged_training_*')
    all_dirs = glob.glob(pattern)

    # 过滤出有效的训练目录（排除.py文件）
    training_dirs = [d for d in all_dirs if os.path.isdir(d)]

    if not training_dirs:
        print("[ERROR] No valid training directories found")
        return []

    # 按时间戳排序，获取最新的
    latest_dir = max(training_dirs, key=lambda x: os.path.getctime(x))
    print(f"[INFO] Using latest training directory: {latest_dir}")

    # 查找该目录中的所有模型文件
    model_files = glob.glob(os.path.join(latest_dir, 'stage_*_model.pth'))

    if not model_files:
        print(f"[ERROR] No model files found in {latest_dir}")
        return []

    # 按阶段号排序
    def extract_stage_number(filename):
        import re
        match = re.search(r'stage_(\d+)_', os.path.basename(filename))
        return int(match.group(1)) if match else 0

    model_files.sort(key=extract_stage_number)

    # 为每个模型推断环境配置
    stage_models = []
    for model_file in model_files:
        stage_num = extract_stage_number(model_file)
        if stage_num == 1:
            environment_name = 'stage1_simple'
        elif stage_num == 2:
            environment_name = 'stage2_complex'
        elif stage_num == 3:
            environment_name = 'stage3_dynamic'
        else:
            continue

        environment_config = get_environment_config(environment_name)
        stage_models.append({
            'model_path': model_file,
            'environment_config': environment_config,
            'stage_num': stage_num,
            'environment_name': environment_name
        })

    return stage_models

def print_training_summary(training_dir):
    """打印训练结果摘要"""
    try:
        # 查找总体结果文件
        results_file = os.path.join(training_dir, 'staged_training_results.json')
        if os.path.exists(results_file):
            with open(results_file, 'r', encoding='utf-8') as f:
                results = json.load(f)

            print(f"\n📋 Training Summary from {training_dir}:")
            print("=" * 50)

            for stage_key, stage_data in results.get('stages', {}).items():
                if 'final_stats' in stage_data:
                    stats = stage_data['final_stats']
                    print(f"{stage_key}:")
                    print(f"  • 随机场景成功率: {stats.get('phase1_success_rate', 0):.2%}")
                    print(f"  • 固定场景成功率: {stats.get('phase2_success_rate', 0):.2%}")
                    print(f"  • 总体成功率: {stats.get('overall_success_rate', 0):.2%}")
                    print(f"  • 平均奖励: {stats.get('avg_reward', 0):.1f}")

            print("=" * 50)

    except Exception as e:
        print(f"⚠️ Could not read training summary: {e}")

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='Staged Training GIF Generator - 简化ver1')
    parser.add_argument('--model', type=str, help='Specific model file path')
    parser.add_argument('--environment', type=str, help='Environment config name (stage1_simple, stage2_complex, stage3_dynamic)')
    parser.add_argument('--steps', type=int, default=400, help='Max steps (default 400)')
    parser.add_argument('--fps', type=int, default=12, help='Frame rate (default 12)')
    parser.add_argument('--auto', action='store_true', help='Auto-detect latest training results and generate single stage GIF')
    parser.add_argument('--all-stages', action='store_true', help='Generate GIFs for all available stages')
    parser.add_argument('--single-stage', action='store_true', help='Generate GIF for single stage only')

    args = parser.parse_args()

    # 默认行为：如果没有指定特定参数，则生成所有阶段的GIF
    if not args.model and not args.auto and not args.single_stage:
        args.all_stages = True

    # 检查是否要生成所有阶段的GIF
    if args.all_stages:
        print("🎬 Generating GIFs for all available stages...")
        stage_models = find_all_staged_models()

        if not stage_models:
            print("❌ No staged training results found")
            exit(1)

        generated_gifs = []
        for stage_info in stage_models:
            print(f"\n{'='*60}")
            print(f"🎯 Processing Stage {stage_info['stage_num']}: {stage_info['environment_name']}")
            print(f"{'='*60}")

            try:
                gif_path, constraint_plot_path, interactive_3d_path = generate_staged_gif(
                    model_path=stage_info['model_path'],
                    environment_config=stage_info['environment_config'],
                    max_steps=args.steps,
                    fps=args.fps
                )
                generated_gifs.append(gif_path)
                print(f"✅ Stage {stage_info['stage_num']} GIF generated: {os.path.basename(gif_path)}")
                print(f"📊 Stage {stage_info['stage_num']} Constraint plot: {os.path.basename(constraint_plot_path)}")
                print(f"🎨 Stage {stage_info['stage_num']} Interactive 3D plot: {os.path.basename(interactive_3d_path)}")
            except Exception as e:
                print(f"❌ Error generating Stage {stage_info['stage_num']} GIF: {e}")

        print(f"\n🎉 All stages GIF generation completed!")
        print(f"📁 Generated {len(generated_gifs)} GIF files:")
        for gif_path in generated_gifs:
            print(f"   • {os.path.basename(gif_path)}")
        exit(0)

    # 确定模型路径和环境配置（单个GIF模式）
    if args.model:
        # 指定了模型路径
        model_path = args.model
        if not os.path.isabs(model_path):
            model_path = os.path.join(os.getcwd(), model_path)

        if args.environment:
            # 同时指定了环境
            environment_config = get_environment_config(args.environment)
        else:
            # 根据模型名称推断环境
            model_name = os.path.basename(model_path)
            if "stage_1" in model_name:
                environment_name = 'stage1_simple'
            elif "stage_2" in model_name:
                environment_name = 'stage2_complex'
            elif "stage_3" in model_name:
                environment_name = 'stage3_dynamic'
            else:
                environment_name = 'stage1_simple'

            environment_config = get_environment_config(environment_name)
            print(f"[INFO] Environment inferred from model name: {environment_name}")

        training_dir = os.path.dirname(model_path)
        print(f"[INFO] Using specified model: {os.path.basename(model_path)}")
    else:
        # 自动检测最新训练结果
        model_path, environment_config, training_dir = find_latest_staged_training()

        if not model_path:
            print("❌ No staged training results found")
            print("💡 Please run staged_training.py first or specify --model and --environment manually")
            exit(1)

    if not os.path.exists(model_path):
        print(f"❌ Model file not found: {model_path}")
        exit(1)

    # 打印训练摘要
    if training_dir:
        print_training_summary(training_dir)

    # 生成GIF
    try:
        gif_path, constraint_plot_path, interactive_3d_path = generate_staged_gif(
            model_path=model_path,
            environment_config=environment_config,
            max_steps=args.steps,
            fps=args.fps
        )

        print(f"\n🎉 Staged training GIF generation completed!")
        print(f"📁 GIF File: {gif_path}")
        print(f"📊 Constraint Plot: {constraint_plot_path}")
        print(f"🎨 Interactive 3D Plot: {interactive_3d_path}")
        print(f"🎬 Based on latest staged training results!")
        print(f"✅ Ready to visualize navigation performance!")

    except Exception as e:
        print(f"❌ Error generating GIF: {e}")
        import traceback
        traceback.print_exc()
        exit(1)
