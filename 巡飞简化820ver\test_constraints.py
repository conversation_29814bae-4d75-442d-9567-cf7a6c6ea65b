"""
约束系统验证脚本
测试巡飞弹物理约束的有效性和合理性
"""

import numpy as np
import matplotlib.pyplot as plt
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from loitering_munition_environment import LoiteringMunitionEnvironment
from environment_config import get_environment_config, get_loitering_munition_config

def test_speed_constraints():
    """测试速度约束"""
    print("🚀 测试速度约束")
    print("=" * 40)
    
    env_config = get_environment_config('test_simple')
    lm_config = get_loitering_munition_config()
    
    env = LoiteringMunitionEnvironment(
        bounds=[1000, 1000, 100],
        environment_config=env_config
    )
    
    # 设置简单场景
    env.start = np.array([100, 100, 50])
    env.goal = np.array([800, 800, 50])
    env.obstacles = []
    env.dynamic_obstacles = []
    
    state = env.reset()
    
    print(f"初始速度: {env.state[3]:.2f} m/s")
    print(f"速度约束: [{env.V_min}, {env.V_max}] m/s")
    print(f"巡航速度: {25.0} m/s")
    print()
    
    # 测试极端控制输入
    extreme_controls = [
        ([50.0, 0, 0], "超大正加速度"),
        ([-50.0, 0, 0], "超大负加速度"),
        ([8.0, 0, 0], "最大正加速度"),
        ([-8.0, 0, 0], "最大负加速度"),
        ([0, 0, 0], "无控制")
    ]
    
    speeds = []
    labels = []
    
    for control, description in extreme_controls:
        env.reset()
        speed_history = []
        
        # 运行20步观察速度变化
        for step in range(20):
            next_state, reward, done, info = env.step(np.array(control))
            speed_history.append(env.state[3])
            
            if done:
                break
        
        speeds.append(speed_history)
        labels.append(description)
        
        final_speed = speed_history[-1]
        print(f"{description:15s}: 最终速度 {final_speed:6.2f} m/s, "
              f"约束检查 {'✅' if env.V_min <= final_speed <= env.V_max else '❌'}")
    
    # 绘制速度变化图
    plt.figure(figsize=(12, 6))
    
    plt.subplot(1, 2, 1)
    for i, (speed_hist, label) in enumerate(zip(speeds, labels)):
        plt.plot(speed_hist, label=label, linewidth=2)
    
    plt.axhline(y=env.V_min, color='r', linestyle='--', label=f'最小速度 {env.V_min}')
    plt.axhline(y=env.V_max, color='r', linestyle='--', label=f'最大速度 {env.V_max}')
    plt.axhline(y=25.0, color='g', linestyle=':', label='巡航速度 25.0')
    
    plt.xlabel('时间步')
    plt.ylabel('速度 (m/s)')
    plt.title('速度约束测试')
    plt.legend()
    plt.grid(True)
    
    return speeds

def test_angle_constraints():
    """测试角度约束"""
    print(f"\n🔄 测试角度约束")
    print("=" * 40)
    
    env_config = get_environment_config('test_simple')
    env = LoiteringMunitionEnvironment(
        bounds=[1000, 1000, 100],
        environment_config=env_config
    )
    
    env.start = np.array([100, 100, 50])
    env.goal = np.array([800, 800, 50])
    env.obstacles = []
    env.dynamic_obstacles = []
    
    state = env.reset()
    
    print(f"初始航迹倾斜角: {env.state[4]:.2f} rad ({np.degrees(env.state[4]):.1f}°)")
    print(f"初始偏航角: {env.state[5]:.2f} rad ({np.degrees(env.state[5]):.1f}°)")
    print(f"航迹倾斜角约束: ±{np.degrees(env.gamma_max):.1f}°")
    print(f"偏航角约束: 周期性 [0, 360°]")
    print()
    
    # 测试极端角度控制
    angle_controls = [
        ([0, 100.0, 0], "超大法向加速度"),
        ([0, -100.0, 0], "超大负法向加速度"),
        ([0, 39.24, 0], "最大法向加速度"),
        ([0, 0, 3.14], "超大倾斜角"),
        ([0, 20, 1.57], "组合角度控制")
    ]
    
    gammas = []
    psis = []
    labels = []
    
    for control, description in angle_controls:
        env.reset()
        gamma_history = []
        psi_history = []
        
        # 运行15步观察角度变化
        for step in range(15):
            next_state, reward, done, info = env.step(np.array(control))
            gamma_history.append(env.state[4])
            psi_history.append(env.state[5])
            
            if done:
                break
        
        gammas.append(gamma_history)
        psis.append(psi_history)
        labels.append(description)
        
        final_gamma = gamma_history[-1]
        final_psi = psi_history[-1]
        
        gamma_ok = -env.gamma_max <= final_gamma <= env.gamma_max
        psi_ok = 0 <= final_psi <= 2*np.pi
        
        print(f"{description:15s}: γ={np.degrees(final_gamma):6.1f}° ψ={np.degrees(final_psi):6.1f}° "
              f"约束检查 {'✅' if gamma_ok and psi_ok else '❌'}")
    
    # 绘制角度变化图
    plt.subplot(1, 2, 2)
    for i, (gamma_hist, label) in enumerate(zip(gammas, labels)):
        plt.plot([np.degrees(g) for g in gamma_hist], label=label, linewidth=2)
    
    plt.axhline(y=np.degrees(env.gamma_max), color='r', linestyle='--', 
                label=f'最大倾斜角 {np.degrees(env.gamma_max):.1f}°')
    plt.axhline(y=-np.degrees(env.gamma_max), color='r', linestyle='--', 
                label=f'最小倾斜角 {-np.degrees(env.gamma_max):.1f}°')
    
    plt.xlabel('时间步')
    plt.ylabel('航迹倾斜角 (度)')
    plt.title('角度约束测试')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('constraints_test.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    return gammas, psis

def test_cruise_speed_concept():
    """测试巡航速度概念"""
    print(f"\n✈️  测试巡航速度概念")
    print("=" * 40)
    
    env_config = get_environment_config('test_simple')
    env = LoiteringMunitionEnvironment(
        bounds=[1000, 1000, 100],
        environment_config=env_config
    )
    
    env.start = np.array([100, 100, 50])
    env.goal = np.array([800, 800, 50])
    env.obstacles = []
    env.dynamic_obstacles = []
    
    # 测试不同速度下的奖励
    test_speeds = [15, 20, 25, 30, 35, 40, 50, 60]  # 测试速度范围
    speed_rewards = []
    
    print("速度 (m/s)  速度奖励  说明")
    print("-" * 35)
    
    for test_speed in test_speeds:
        env.reset()
        env.state[3] = test_speed  # 设置测试速度
        
        # 执行无控制动作，只看速度奖励
        next_state, reward, done, info = env.step(np.array([0, 0, 0]))
        
        # 计算速度奖励组件
        target_speed = 25.0
        speed_reward = max(0, 5.0 - abs(test_speed - target_speed) * 0.2)
        speed_rewards.append(speed_reward)
        
        if test_speed == 25:
            description = "巡航速度 (最优)"
        elif test_speed < 20:
            description = "接近失速"
        elif test_speed > 50:
            description = "高速飞行"
        else:
            description = "正常范围"
        
        print(f"{test_speed:6.0f}      {speed_reward:6.2f}    {description}")
    
    # 绘制速度-奖励关系
    plt.figure(figsize=(10, 6))
    plt.plot(test_speeds, speed_rewards, 'bo-', linewidth=2, markersize=8)
    plt.axvline(x=25, color='g', linestyle='--', label='巡航速度 25 m/s')
    plt.axvline(x=15, color='r', linestyle=':', label='最小速度 15 m/s')
    plt.axvline(x=60, color='r', linestyle=':', label='最大速度 60 m/s')
    
    plt.xlabel('飞行速度 (m/s)')
    plt.ylabel('速度奖励')
    plt.title('巡航速度概念：速度奖励函数')
    plt.legend()
    plt.grid(True)
    
    plt.savefig('cruise_speed_concept.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print(f"\n💡 巡航速度概念解释:")
    print(f"  - 巡航速度 (25 m/s) 是目标速度，不是恒定速度")
    print(f"  - 实际速度可以在 [{env.V_min}, {env.V_max}] m/s 范围内变化")
    print(f"  - 奖励函数鼓励接近巡航速度，但允许偏离")
    print(f"  - 在避障、机动时速度会动态调整")

def test_constraint_interactions():
    """测试约束之间的相互作用"""
    print(f"\n🔗 测试约束相互作用")
    print("=" * 40)
    
    env_config = get_environment_config('test_simple')
    env = LoiteringMunitionEnvironment(
        bounds=[1000, 1000, 100],
        environment_config=env_config
    )
    
    env.start = np.array([100, 100, 50])
    env.goal = np.array([800, 800, 50])
    env.obstacles = []
    env.dynamic_obstacles = []
    
    # 测试复杂机动
    complex_maneuvers = [
        ([8.0, 39.24, 1.57], "最大加速+最大转向"),
        ([-8.0, -39.24, -1.57], "最大减速+反向转向"),
        ([4.0, 20.0, 0.5], "中等机动"),
        ([0, 0, 0], "无控制基准")
    ]
    
    print("机动类型           速度变化  角度变化  约束状态")
    print("-" * 55)
    
    for control, description in complex_maneuvers:
        env.reset()
        initial_speed = env.state[3]
        initial_gamma = env.state[4]
        
        # 执行5步机动
        for _ in range(5):
            next_state, reward, done, info = env.step(np.array(control))
            if done:
                break
        
        final_speed = env.state[3]
        final_gamma = env.state[4]
        
        speed_change = final_speed - initial_speed
        gamma_change = np.degrees(final_gamma - initial_gamma)
        
        # 检查约束
        speed_ok = env.V_min <= final_speed <= env.V_max
        gamma_ok = -env.gamma_max <= final_gamma <= env.gamma_max
        
        status = "✅" if speed_ok and gamma_ok else "❌"
        
        print(f"{description:18s} {speed_change:+6.2f}   {gamma_change:+7.1f}°    {status}")

def main():
    """主函数"""
    print("🔒 巡飞弹约束系统验证")
    print("=" * 60)
    
    try:
        # 1. 速度约束测试
        speeds = test_speed_constraints()
        
        # 2. 角度约束测试
        gammas, psis = test_angle_constraints()
        
        # 3. 巡航速度概念测试
        test_cruise_speed_concept()
        
        # 4. 约束相互作用测试
        test_constraint_interactions()
        
        print(f"\n🎉 约束系统验证完成!")
        print(f"📁 生成文件:")
        print(f"  - constraints_test.png: 速度和角度约束测试图")
        print(f"  - cruise_speed_concept.png: 巡航速度概念图")
        
        print(f"\n📊 验证结果总结:")
        print(f"  ✅ 速度约束: 有效防止超出 [{15}, {60}] m/s 范围")
        print(f"  ✅ 角度约束: 航迹倾斜角限制在 ±60° 范围内")
        print(f"  ✅ 偏航角处理: 周期性约束，无幅度限制")
        print(f"  ✅ 巡航速度: 作为目标速度，允许动态调节")
        print(f"  ✅ 约束相互作用: 多重约束协调工作")
        
    except Exception as e:
        print(f"❌ 验证过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
