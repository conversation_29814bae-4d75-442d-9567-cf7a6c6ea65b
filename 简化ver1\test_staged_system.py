"""
分阶段训练系统测试脚本 - 简化ver1
用于验证新的分阶段训练系统是否正常工作
"""

import os
import sys
import traceback
from datetime import datetime

def test_imports():
    """测试所有必要的导入"""
    print("🔍 测试模块导入...")
    
    try:
        # 测试环境配置
        from environment_config import (
            get_environment_config, 
            get_training_stage_config,
            print_all_configs,
            generate_obstacles_for_config,
            generate_dynamic_obstacles_for_config
        )
        print("✅ environment_config 导入成功")
        
        # 测试核心组件
        from dwa_rl_core import StabilizedRewardEnvironment, StabilizedTD3Controller, td3_config
        print("✅ dwa_rl_core 导入成功")
        
        # 测试训练器
        from staged_training import StagedTrainer
        print("✅ staged_training 导入成功")
        
        from enhanced_staged_trainer import EnhancedStagedTrainer
        print("✅ enhanced_staged_trainer 导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        traceback.print_exc()
        return False

def test_environment_configs():
    """测试环境配置"""
    print("\n🔍 测试环境配置...")
    
    try:
        from environment_config import get_environment_config, get_training_stage_config
        
        # 测试所有环境配置
        env_configs = ["stage1_simple", "stage2_complex", "stage3_dynamic"]
        for config_name in env_configs:
            config = get_environment_config(config_name)
            print(f"✅ {config_name}: {config['description']}")
        
        # 测试所有训练阶段配置
        stage_configs = ["stage1_simple", "stage2_complex", "stage3_dynamic"]
        for stage_name in stage_configs:
            config = get_training_stage_config(stage_name)
            print(f"✅ {stage_name}: {config['total_episodes']} episodes")
        
        return True
        
    except Exception as e:
        print(f"❌ 环境配置测试失败: {e}")
        traceback.print_exc()
        return False

def test_environment_creation():
    """测试环境创建"""
    print("\n🔍 测试环境创建...")
    
    try:
        from environment_config import get_environment_config
        from dwa_rl_core import StabilizedRewardEnvironment
        
        # 测试每个阶段的环境创建
        for stage_name in ["stage1_simple", "stage2_complex", "stage3_dynamic"]:
            env_config = get_environment_config(stage_name)
            
            env = StabilizedRewardEnvironment(
                bounds=[100, 100, 100],
                environment_config=env_config,
                reward_type='simplified'
            )
            
            # 测试环境重置
            state = env.reset()
            print(f"✅ {stage_name} 环境创建成功，观察状态维度: {len(state)}")
            
            # 测试场景保存
            scenario = env.save_scenario()
            print(f"   障碍物数量: {len(scenario['obstacles'])}")
            
            # 测试固定场景加载
            state2 = env.reset(scenario)
            print(f"   固定场景加载成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 环境创建测试失败: {e}")
        traceback.print_exc()
        return False

def test_obstacle_generation():
    """测试障碍物生成"""
    print("\n🔍 测试障碍物生成...")
    
    try:
        from environment_config import (
            get_environment_config,
            generate_obstacles_for_config,
            generate_dynamic_obstacles_for_config
        )
        
        for stage_name in ["stage1_simple", "stage2_complex", "stage3_dynamic"]:
            env_config = get_environment_config(stage_name)
            
            # 生成静态障碍物
            obstacles = generate_obstacles_for_config(env_config, seed=42)
            print(f"✅ {stage_name} 静态障碍物生成: {len(obstacles)} 个")
            
            # 生成动态障碍物（如果启用）
            if env_config.get("enable_dynamic_obstacles", False):
                dynamic_obstacles = generate_dynamic_obstacles_for_config(env_config, seed=42)
                print(f"   动态障碍物生成: {len(dynamic_obstacles)} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ 障碍物生成测试失败: {e}")
        traceback.print_exc()
        return False

def test_controller_creation():
    """测试控制器创建"""
    print("\n🔍 测试控制器创建...")
    
    try:
        from dwa_rl_core import StabilizedTD3Controller, td3_config
        
        controller = StabilizedTD3Controller(td3_config)
        print("✅ TD3控制器创建成功")
        
        # 测试经验回放缓冲区
        buffer_size = len(controller.replay_buffer)
        print(f"   经验回放缓冲区大小: {buffer_size}")
        
        return True
        
    except Exception as e:
        print(f"❌ 控制器创建测试失败: {e}")
        traceback.print_exc()
        return False

def test_mini_training():
    """测试迷你训练（几个episodes）"""
    print("\n🔍 测试迷你训练...")
    
    try:
        from environment_config import get_environment_config
        from dwa_rl_core import StabilizedRewardEnvironment, StabilizedTD3Controller, td3_config
        import numpy as np
        
        # 创建简单环境
        env_config = get_environment_config("stage1_simple")
        env = StabilizedRewardEnvironment(
            bounds=[100, 100, 100],
            environment_config=env_config,
            reward_type='simplified'
        )
        
        # 创建控制器
        controller = StabilizedTD3Controller(td3_config)
        
        # 运行几个episodes
        for episode in range(3):
            state = env.reset()
            full_state = np.concatenate([env.state, state[6:]])
            
            episode_reward = 0
            step_count = 0
            
            while step_count < 50:  # 限制步数以加快测试
                # 获取动作
                action, info, safe_actions = controller.get_action_with_quality(
                    full_state, env.goal, env.obstacles, add_noise=True
                )
                
                # 执行动作
                next_state, reward, done, env_info = env.step(action)
                next_full_state = np.concatenate([env.state, next_state[6:]])
                
                episode_reward += reward
                step_count += 1
                
                # 存储经验
                controller.replay_buffer.add(
                    full_state.copy(),
                    action.copy(),
                    reward,
                    next_full_state.copy(),
                    done,
                    safe_actions,
                    env.goal.copy(),
                    [obs.copy() for obs in env.obstacles],
                    info.get('selected_idx', 0)
                )
                
                # 训练更新
                controller.immediate_update(batch_size=32)
                
                if done:
                    break
                
                full_state = next_full_state
            
            result = "成功" if env_info.get('success', False) else "失败"
            print(f"   Episode {episode+1}: {step_count} 步, 奖励 {episode_reward:.1f}, {result}")
        
        print("✅ 迷你训练测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 迷你训练测试失败: {e}")
        traceback.print_exc()
        return False

def test_visualization():
    """测试可视化功能"""
    print("\n🔍 测试可视化功能...")
    
    try:
        import matplotlib
        matplotlib.use('Agg')  # 使用非交互式后端
        import matplotlib.pyplot as plt
        from mpl_toolkits.mplot3d import Axes3D
        import numpy as np
        
        # 创建简单的测试图
        fig = plt.figure(figsize=(8, 6))
        ax = fig.add_subplot(111, projection='3d')
        
        # 绘制测试数据
        x = np.linspace(0, 100, 10)
        y = np.linspace(0, 100, 10)
        z = np.linspace(0, 100, 10)
        
        ax.plot(x, y, z, 'b-', label='Test Trajectory')
        ax.set_xlabel('X (m)')
        ax.set_ylabel('Y (m)')
        ax.set_zlabel('Z (m)')
        ax.legend()
        
        # 保存测试图片
        test_dir = 'test_output'
        os.makedirs(test_dir, exist_ok=True)
        test_file = os.path.join(test_dir, 'test_visualization.png')
        plt.savefig(test_file, dpi=100, bbox_inches='tight')
        plt.close()
        
        if os.path.exists(test_file):
            print("✅ 可视化功能测试成功")
            print(f"   测试图片保存在: {test_file}")
            return True
        else:
            print("❌ 可视化测试失败：图片未保存")
            return False
        
    except Exception as e:
        print(f"❌ 可视化测试失败: {e}")
        traceback.print_exc()
        return False

def run_all_tests():
    """运行所有测试"""
    print("🧪 分阶段训练系统测试")
    print("=" * 50)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    tests = [
        ("模块导入", test_imports),
        ("环境配置", test_environment_configs),
        ("环境创建", test_environment_creation),
        ("障碍物生成", test_obstacle_generation),
        ("控制器创建", test_controller_creation),
        ("迷你训练", test_mini_training),
        ("可视化功能", test_visualization)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统可以正常使用。")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关问题。")
        return False

def main():
    """主函数"""
    success = run_all_tests()
    
    if success:
        print("\n💡 系统测试完成，可以开始使用分阶段训练：")
        print("   python run_staged_training.py")
        print("   或者")
        print("   python staged_training.py --start-stage 1 --end-stage 3")
    else:
        print("\n🔧 请修复测试中发现的问题后再使用系统。")
    
    return success

if __name__ == "__main__":
    main()
