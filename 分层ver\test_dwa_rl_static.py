"""
静态版本的DWA-RL测试脚本
生成静态图片展示动态导航效果
"""

import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import json
import os
import time
import argparse
from datetime import datetime
import glob

from dwa_rl_core import StabilizedEnvironment, load_trained_model, td3_config

def test_dwa_rl_static(model_path, num_test_episodes=3, enable_dynamic_obstacles=True, save_frames=True):
    """静态版本的DWA-RL测试，生成图片序列"""
    print("🧪 DWA-RL静态测试脚本")
    print("=" * 60)
    print(f"模型路径: {model_path}")
    print(f"测试Episodes: {num_test_episodes}")
    print(f"动态障碍物: {'启用' if enable_dynamic_obstacles else '禁用'}")
    print(f"保存帧序列: {'启用' if save_frames else '禁用'}")
    print("=" * 60)

    # 创建输出目录
    output_dir = "dwa_rl_test_outputs"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 加载模型
    controller = load_trained_model(model_path, td3_config)

    # 测试统计
    test_results = {
        'episode_rewards': [],
        'episode_steps': [],
        'success_count': 0,
        'collision_count': 0,
        'timeout_count': 0,
        'trajectories': [],
        'completion_times': [],
        'path_lengths': []
    }

    print(f"\n🚀 开始测试 {num_test_episodes} 个episodes...")
    print("-" * 60)

    for episode in range(num_test_episodes):
        # 创建环境
        env = StabilizedEnvironment(enable_dynamic_obstacles=enable_dynamic_obstacles)
        state = env.reset()
        full_state = np.concatenate([env.state, state[6:]])

        episode_reward = 0
        step_count = 0
        trajectory = []
        start_time = time.time()
        
        # 记录环境信息
        episode_obstacles = {
            'static': [{'center': obs['center'].copy(), 'radius': obs['radius']} for obs in env.obstacles],
            'dynamic': []
        }
        
        if enable_dynamic_obstacles and hasattr(env, 'dynamic_obstacles'):
            episode_obstacles['dynamic'] = [
                {'center': obs['center'].copy(), 'radius': obs['radius'], 
                 'motion_type': obs['motion_type'], 'initial_center': obs['center'].copy()}
                for obs in env.dynamic_obstacles
            ]

        max_steps = 300  # 减少最大步数以加快测试
        frame_interval = 10  # 每10步保存一帧

        while step_count < max_steps:
            # 记录轨迹
            trajectory.append(env.state[:3].copy())

            # 获取所有障碍物
            all_obstacles = env.obstacles.copy()
            if enable_dynamic_obstacles and hasattr(env, 'dynamic_obstacles'):
                all_obstacles.extend(env.dynamic_obstacles)

            # 获取动作
            action, _, _ = controller.get_action_with_quality(
                full_state, env.goal, all_obstacles, add_noise=True
            )

            # 执行动作
            next_state, reward, done, env_info = env.step(action)
            next_full_state = np.concatenate([env.state, next_state[6:]])

            episode_reward += reward
            step_count += 1

            # 保存关键帧
            if save_frames and (step_count % frame_interval == 0 or done):
                save_navigation_frame(env, trajectory, episode, step_count, output_dir, 
                                    episode_obstacles, enable_dynamic_obstacles)

            if done:
                completion_time = time.time() - start_time
                test_results['completion_times'].append(completion_time)

                # 记录结果
                if env_info.get('success', False):
                    test_results['success_count'] += 1
                    result_type = 'SUCCESS'
                elif env_info.get('collision', False):
                    test_results['collision_count'] += 1
                    result_type = 'COLLISION'
                else:
                    test_results['timeout_count'] += 1
                    result_type = 'TIMEOUT'

                break

            full_state = next_full_state
        else:
            # 超时情况
            completion_time = time.time() - start_time
            test_results['completion_times'].append(completion_time)
            test_results['timeout_count'] += 1
            result_type = 'TIMEOUT'

        # 计算轨迹指标
        path_length = 0
        if len(trajectory) > 1:
            trajectory_array = np.array(trajectory)
            path_length = np.sum(np.linalg.norm(np.diff(trajectory_array, axis=0), axis=1))
            test_results['path_lengths'].append(path_length)

        test_results['episode_rewards'].append(episode_reward)
        test_results['episode_steps'].append(step_count)
        test_results['trajectories'].append(trajectory)

        # 生成episode总结图
        generate_episode_summary(env, trajectory, episode, result_type, output_dir, 
                                episode_obstacles, enable_dynamic_obstacles)

        # 打印进度
        print(f"Episode {episode+1:2d}: {result_type:9s} | Reward={episode_reward:6.1f} | Steps={step_count:3d} | "
              f"Time={completion_time:.2f}s | Path={path_length:.1f}m")

    # 生成最终总结
    generate_final_summary(test_results, output_dir, num_test_episodes)

    return test_results

def save_navigation_frame(env, trajectory, episode, step, output_dir, episode_obstacles, enable_dynamic):
    """保存导航帧"""
    fig = plt.figure(figsize=(12, 10))
    ax = fig.add_subplot(111, projection='3d')

    # 绘制起点和终点
    start = env.start
    goal = env.goal
    ax.scatter(start[0], start[1], start[2], c='green', s=300, marker='o', 
              label='Start', alpha=0.9, edgecolors='darkgreen', linewidth=2)
    ax.scatter(goal[0], goal[1], goal[2], c='red', s=400, marker='*', 
              label='Goal', alpha=0.9, edgecolors='darkred', linewidth=2)

    # 绘制静态障碍物
    for obs in episode_obstacles['static']:
        center = obs['center']
        radius = obs['radius']
        u = np.linspace(0, 2 * np.pi, 15)
        v = np.linspace(0, np.pi, 15)
        x = radius * np.outer(np.cos(u), np.sin(v)) + center[0]
        y = radius * np.outer(np.sin(u), np.sin(v)) + center[1]
        z = radius * np.outer(np.ones(np.size(u)), np.cos(v)) + center[2]
        ax.plot_surface(x, y, z, alpha=0.4, color='gray', edgecolor='black', linewidth=0.5)

    # 绘制动态障碍物（当前位置）
    if enable_dynamic and hasattr(env, 'dynamic_obstacles'):
        for obs in env.dynamic_obstacles:
            center = obs['center']
            radius = obs['radius']
            u = np.linspace(0, 2 * np.pi, 12)
            v = np.linspace(0, np.pi, 12)
            x = radius * np.outer(np.cos(u), np.sin(v)) + center[0]
            y = radius * np.outer(np.sin(u), np.sin(v)) + center[1]
            z = radius * np.outer(np.ones(np.size(u)), np.cos(v)) + center[2]
            ax.plot_surface(x, y, z, alpha=0.6, color='orange', edgecolor='red', linewidth=0.8)

    # 绘制轨迹
    if len(trajectory) > 1:
        positions = np.array(trajectory)
        ax.plot(positions[:, 0], positions[:, 1], positions[:, 2],
               color='blue', alpha=0.8, linewidth=2, linestyle='-')

    # 绘制当前位置
    current_pos = env.state[:3]
    ax.scatter(current_pos[0], current_pos[1], current_pos[2], 
              c='blue', s=150, marker='D', alpha=0.9, 
              edgecolors='darkblue', linewidth=2)

    # 设置坐标轴
    ax.set_xlabel('X (m)')
    ax.set_ylabel('Y (m)')
    ax.set_zlabel('Z (m)')
    ax.set_xlim(0, 100)
    ax.set_ylim(0, 100)
    ax.set_zlim(0, 100)
    ax.grid(True, alpha=0.3)
    ax.legend()

    # 设置标题
    goal_dist = np.linalg.norm(current_pos - goal)
    ax.set_title(f'Episode {episode+1} - Step {step} | Distance to Goal: {goal_dist:.1f}m', 
                fontsize=12, fontweight='bold')

    plt.tight_layout()

    # 保存帧
    frame_path = os.path.join(output_dir, f"episode_{episode+1}_step_{step:03d}.png")
    plt.savefig(frame_path, dpi=120, bbox_inches='tight')
    plt.close()

def generate_episode_summary(env, trajectory, episode, result_type, output_dir, episode_obstacles, enable_dynamic):
    """生成episode总结图"""
    fig = plt.figure(figsize=(14, 12))
    ax = fig.add_subplot(111, projection='3d')

    # 绘制环境
    start = env.start
    goal = env.goal
    ax.scatter(start[0], start[1], start[2], c='green', s=400, marker='o', 
              label='Start', alpha=0.9, edgecolors='darkgreen', linewidth=3)
    ax.scatter(goal[0], goal[1], goal[2], c='red', s=500, marker='*', 
              label='Goal', alpha=0.9, edgecolors='darkred', linewidth=3)

    # 绘制静态障碍物
    for obs in episode_obstacles['static']:
        center = obs['center']
        radius = obs['radius']
        u = np.linspace(0, 2 * np.pi, 20)
        v = np.linspace(0, np.pi, 20)
        x = radius * np.outer(np.cos(u), np.sin(v)) + center[0]
        y = radius * np.outer(np.sin(u), np.sin(v)) + center[1]
        z = radius * np.outer(np.ones(np.size(u)), np.cos(v)) + center[2]
        ax.plot_surface(x, y, z, alpha=0.5, color='gray', edgecolor='black', linewidth=0.8)

    # 绘制动态障碍物初始位置（半透明）
    if enable_dynamic and episode_obstacles['dynamic']:
        for obs in episode_obstacles['dynamic']:
            center = obs['initial_center']
            radius = obs['radius']
            u = np.linspace(0, 2 * np.pi, 15)
            v = np.linspace(0, np.pi, 15)
            x = radius * np.outer(np.cos(u), np.sin(v)) + center[0]
            y = radius * np.outer(np.sin(u), np.sin(v)) + center[1]
            z = radius * np.outer(np.ones(np.size(u)), np.cos(v)) + center[2]
            ax.plot_surface(x, y, z, alpha=0.3, color='orange', edgecolor='red', linewidth=0.5)

    # 绘制完整轨迹
    if len(trajectory) > 1:
        trajectory_array = np.array(trajectory)
        
        # 根据结果类型设置颜色
        if result_type == 'SUCCESS':
            color, alpha, linewidth = 'green', 0.9, 3
        elif result_type == 'COLLISION':
            color, alpha, linewidth = 'red', 0.8, 2.5
        else:
            color, alpha, linewidth = 'orange', 0.7, 2
            
        ax.plot(trajectory_array[:, 0], trajectory_array[:, 1], trajectory_array[:, 2],
               color=color, alpha=alpha, linewidth=linewidth, label=f'Path ({result_type})')

        # 标记最终位置
        final_pos = trajectory_array[-1]
        ax.scatter(final_pos[0], final_pos[1], final_pos[2], 
                  c=color, s=200, marker='s', alpha=0.8, edgecolors='black', linewidth=2)

    # 设置坐标轴
    ax.set_xlabel('X (m)', fontsize=12)
    ax.set_ylabel('Y (m)', fontsize=12)
    ax.set_zlabel('Z (m)', fontsize=12)
    ax.set_xlim(0, 100)
    ax.set_ylim(0, 100)
    ax.set_zlim(0, 100)
    ax.grid(True, alpha=0.3)

    # 计算统计信息
    path_length = 0
    if len(trajectory) > 1:
        path_length = np.sum(np.linalg.norm(np.diff(np.array(trajectory), axis=0), axis=1))
    
    direct_dist = np.linalg.norm(goal - start)
    efficiency = direct_dist / path_length if path_length > 0 else 0

    # 设置标题
    ax.set_title(f'Episode {episode+1} Summary - {result_type}\n'
                f'Steps: {len(trajectory)} | Path: {path_length:.1f}m | Efficiency: {efficiency:.2%}', 
                fontsize=14, fontweight='bold')

    ax.legend(fontsize=10)
    ax.view_init(elev=25, azim=45)

    plt.tight_layout()

    # 保存总结图
    summary_path = os.path.join(output_dir, f"episode_{episode+1}_summary.png")
    plt.savefig(summary_path, dpi=150, bbox_inches='tight')
    plt.close()

def generate_final_summary(test_results, output_dir, num_episodes):
    """生成最终测试总结"""
    # 计算统计指标
    success_rate = test_results['success_count'] / num_episodes
    collision_rate = test_results['collision_count'] / num_episodes
    timeout_rate = test_results['timeout_count'] / num_episodes
    
    avg_reward = np.mean(test_results['episode_rewards'])
    avg_steps = np.mean(test_results['episode_steps'])
    avg_time = np.mean(test_results['completion_times'])
    avg_path = np.mean(test_results['path_lengths']) if test_results['path_lengths'] else 0

    # 打印结果
    print(f"\n📊 测试结果总结:")
    print("=" * 60)
    print(f"测试Episodes: {num_episodes}")
    print(f"成功率: {success_rate:.3f} ({test_results['success_count']}/{num_episodes})")
    print(f"碰撞率: {collision_rate:.3f} ({test_results['collision_count']}/{num_episodes})")
    print(f"超时率: {timeout_rate:.3f} ({test_results['timeout_count']}/{num_episodes})")
    print(f"平均奖励: {avg_reward:.2f}")
    print(f"平均步数: {avg_steps:.1f}")
    print(f"平均完成时间: {avg_time:.2f}s")
    print(f"平均路径长度: {avg_path:.1f}m")

    # 保存结果到JSON
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_summary = {
        "test_summary": {
            "timestamp": timestamp,
            "num_episodes": num_episodes,
            "success_rate": success_rate,
            "collision_rate": collision_rate,
            "timeout_rate": timeout_rate
        },
        "performance_metrics": {
            "average_reward": avg_reward,
            "average_steps": avg_steps,
            "average_time": avg_time,
            "average_path_length": avg_path
        }
    }

    results_path = os.path.join(output_dir, f"test_results_{timestamp}.json")
    with open(results_path, 'w') as f:
        json.dump(results_summary, f, indent=2)

    print(f"\n✅ 静态测试完成!")
    print(f"📁 输出目录: {output_dir}")
    print(f"📊 结果文件: {results_path}")

def find_latest_model():
    """查找最新的模型文件"""
    model_files = glob.glob('training_outputs/dwa_rl_model_*.pth')
    if not model_files:
        model_files = glob.glob('training_outputs/stabilized_td3_model_*.pth')

    if model_files:
        return max(model_files, key=os.path.getctime)
    else:
        return None

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='DWA-RL静态测试脚本')
    parser.add_argument('--model', type=str, help='模型文件路径')
    parser.add_argument('--episodes', type=int, default=3, help='测试episodes数量')
    parser.add_argument('--no-dynamic', action='store_true', help='禁用动态障碍物')
    parser.add_argument('--no-frames', action='store_true', help='禁用帧序列保存')

    args = parser.parse_args()

    # 确定模型路径
    if args.model:
        model_path = args.model
    else:
        model_path = find_latest_model()
        if model_path:
            print(f"🔍 使用最新模型: {model_path}")
        else:
            print("❌ 未找到训练好的模型文件")
            exit(1)

    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        exit(1)

    # 运行测试
    test_results = test_dwa_rl_static(
        model_path=model_path,
        num_test_episodes=args.episodes,
        enable_dynamic_obstacles=not args.no_dynamic,
        save_frames=not args.no_frames
    )
