"""
测试训练结果 - 巡飞简化ver
读取时间戳文件夹中的训练结果，生成与简化ver1相同的测试和保存内容
"""

import os
import sys
import json
import pickle
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import glob
import re

def find_latest_training_directory():
    """查找最新的训练结果目录"""
    print("🔍 查找训练结果目录...")

    # 查找所有训练目录，排除非目录文件
    pattern = "loitering_munition_staged_training_*"
    all_matches = glob.glob(pattern)

    # 只保留目录，排除文件，并且排除包含其他后缀的目录
    training_dirs = []
    for d in all_matches:
        if os.path.isdir(d):
            # 确保是纯粹的训练目录，不包含其他后缀
            if re.match(r'^loitering_munition_staged_training_\d{8}_\d{6}$', d):
                training_dirs.append(d)

    if not training_dirs:
        print("❌ 未找到训练结果目录")
        return None

    # 按时间戳排序，获取最新的
    training_dirs.sort(reverse=True)
    latest_dir = training_dirs[0]
    
    print(f"✅ 找到最新训练目录: {latest_dir}")
    
    # 提取时间戳
    timestamp_match = re.search(r'(\d{8}_\d{6})', latest_dir)
    if timestamp_match:
        timestamp = timestamp_match.group(1)
        formatted_time = datetime.strptime(timestamp, '%Y%m%d_%H%M%S').strftime('%Y-%m-%d %H:%M:%S')
        print(f"📅 训练时间: {formatted_time}")
    
    return latest_dir

def load_training_results(training_dir):
    """加载训练结果数据"""
    print(f"\n📊 加载训练结果数据...")
    
    results = {}
    
    # 1. 加载JSON结果
    json_file = os.path.join(training_dir, 'staged_training_results.json')
    if os.path.exists(json_file):
        with open(json_file, 'r', encoding='utf-8') as f:
            results['json_data'] = json.load(f)
        print(f"✅ 加载JSON数据: {json_file}")
    else:
        print(f"❌ 未找到JSON文件: {json_file}")
    
    # 2. 加载PKL数据
    pkl_file = os.path.join(training_dir, 'staged_training_data.pkl')
    if os.path.exists(pkl_file):
        with open(pkl_file, 'rb') as f:
            results['pkl_data'] = pickle.load(f)
        print(f"✅ 加载PKL数据: {pkl_file}")
    else:
        print(f"❌ 未找到PKL文件: {pkl_file}")
    
    # 3. 加载模型文件
    model_files = glob.glob(os.path.join(training_dir, '*_model.pth'))
    results['model_files'] = model_files
    print(f"✅ 找到模型文件: {len(model_files)} 个")
    for model_file in model_files:
        print(f"   • {os.path.basename(model_file)}")
    
    # 4. 加载轨迹图片
    trajectory_files = glob.glob(os.path.join(training_dir, '*_3d_trajectory.png'))
    results['trajectory_files'] = trajectory_files
    print(f"✅ 找到轨迹图片: {len(trajectory_files)} 个")
    
    # 5. 加载CSV报告
    csv_files = glob.glob(os.path.join(training_dir, '*.csv'))
    results['csv_files'] = csv_files
    print(f"✅ 找到CSV文件: {len(csv_files)} 个")
    
    # 6. 加载其他图片
    other_images = glob.glob(os.path.join(training_dir, '*.png'))
    other_images = [f for f in other_images if '_3d_trajectory.png' not in f]
    results['other_images'] = other_images
    print(f"✅ 找到其他图片: {len(other_images)} 个")
    
    return results

def analyze_training_performance(results):
    """分析训练性能"""
    print(f"\n📈 分析训练性能...")
    
    analysis = {}
    
    if 'json_data' in results:
        json_data = results['json_data']
        
        # 分析阶段数据
        if 'stages' in json_data:
            stages_data = json_data['stages']
            analysis['stages_count'] = len(stages_data)
            
            print(f"训练阶段数量: {analysis['stages_count']}")
            
            for stage_name, stage_data in stages_data.items():
                if 'error' not in stage_data:
                    print(f"\n📊 {stage_name} 阶段分析:")
                    print(f"   总episodes: {stage_data.get('total_episodes', 'N/A')}")
                    print(f"   成功率: {stage_data.get('success_rate', 0):.2%}")
                    print(f"   平均奖励: {stage_data.get('final_avg_reward', 0):.2f}")
                    print(f"   训练时间: {stage_data.get('training_time', 0):.1f}秒")
                    
                    # 分析随机和固定阶段
                    random_success = stage_data.get('random_phase_success_rate', 0)
                    fixed_success = stage_data.get('fixed_phase_success_rate', 0)
                    print(f"   随机阶段成功率: {random_success:.2%}")
                    print(f"   固定阶段成功率: {fixed_success:.2%}")
                else:
                    print(f"\n❌ {stage_name} 阶段训练失败: {stage_data.get('error', '未知错误')}")
        
        # 分析总体性能
        if 'summary' in json_data:
            summary = json_data['summary']
            analysis['total_time'] = summary.get('total_training_time', 0)
            analysis['total_episodes'] = summary.get('total_episodes', 0)
            
            print(f"\n🎯 总体性能:")
            print(f"   总训练时间: {analysis['total_time']:.1f}秒 ({analysis['total_time']/3600:.1f}小时)")
            print(f"   总episodes: {analysis['total_episodes']}")
            print(f"   平均每episode时间: {analysis['total_time']/max(analysis['total_episodes'], 1):.1f}秒")
    
    return analysis

def test_model_loading(results):
    """测试模型加载"""
    print(f"\n🧪 测试模型加载...")
    
    if not results.get('model_files'):
        print("❌ 没有找到模型文件")
        return False
    
    try:
        import torch
        from td3_network import StabilizedTD3Controller
        from environment_config import get_td3_config
        
        success_count = 0
        
        for model_file in results['model_files']:
            try:
                # 创建控制器
                td3_config = get_td3_config()
                td3_config.update({
                    'state_dim': 15,
                    'action_dim': 3,
                    'max_action': 1.0
                })
                controller = StabilizedTD3Controller(td3_config)
                
                # 加载模型
                controller.load(model_file)
                
                # 测试推理
                test_state = np.random.randn(15)
                action = controller.select_action(test_state, noise=0)
                
                print(f"✅ {os.path.basename(model_file)} 加载成功")
                print(f"   测试输出: {action}")
                success_count += 1
                
            except Exception as e:
                print(f"❌ {os.path.basename(model_file)} 加载失败: {e}")
        
        print(f"\n📊 模型加载测试结果: {success_count}/{len(results['model_files'])} 成功")
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 模型加载测试失败: {e}")
        return False

def generate_simplified_ver1_format_outputs(training_dir, results):
    """生成与简化ver1格式相同的输出文件"""
    print(f"\n🔄 生成简化ver1格式的输出文件...")
    
    # 创建输出目录
    output_dir = f"{training_dir}_simplified_ver1_format"
    os.makedirs(output_dir, exist_ok=True)
    
    generated_files = []
    
    try:
        # 1. 生成simplified_reward_model.pth (重命名模型文件)
        if results.get('model_files'):
            for model_file in results['model_files']:
                if 'stage_1' in model_file:
                    new_model_path = os.path.join(output_dir, 'simplified_reward_model.pth')
                    import shutil
                    shutil.copy2(model_file, new_model_path)
                    generated_files.append('simplified_reward_model.pth')
                    print(f"✅ 生成模型文件: simplified_reward_model.pth")
                    break
        
        # 2. 生成simplified_reward_training_data.pkl
        if 'pkl_data' in results:
            pkl_path = os.path.join(output_dir, 'simplified_reward_training_data.pkl')
            with open(pkl_path, 'wb') as f:
                pickle.dump(results['pkl_data'], f)
            generated_files.append('simplified_reward_training_data.pkl')
            print(f"✅ 生成数据文件: simplified_reward_training_data.pkl")
        
        # 3. 生成simplified_reward_training_report.json
        if 'json_data' in results:
            # 转换为简化ver1格式
            simplified_report = {
                'training_config': {
                    'total_episodes': results['json_data'].get('summary', {}).get('total_episodes', 0),
                    'training_time': results['json_data'].get('summary', {}).get('total_training_time', 0),
                    'reward_type': 'simplified'
                },
                'performance_metrics': {},
                'stage_results': {}
            }
            
            # 添加阶段结果
            if 'stages' in results['json_data']:
                for stage_name, stage_data in results['json_data']['stages'].items():
                    if 'error' not in stage_data:
                        simplified_report['stage_results'][stage_name] = {
                            'success_rate': stage_data.get('success_rate', 0),
                            'average_reward': stage_data.get('final_avg_reward', 0),
                            'total_episodes': stage_data.get('total_episodes', 0)
                        }
            
            report_path = os.path.join(output_dir, 'simplified_reward_training_report.json')
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(simplified_report, f, indent=2, ensure_ascii=False)
            generated_files.append('simplified_reward_training_report.json')
            print(f"✅ 生成报告文件: simplified_reward_training_report.json")
        
        # 4. 生成simplified_reward_training_rewards.csv
        if 'json_data' in results and 'stages' in results['json_data']:
            rewards_data = []
            episode_count = 0
            
            for stage_name, stage_data in results['json_data']['stages'].items():
                if 'error' not in stage_data and 'all_rewards' in stage_data:
                    for reward in stage_data['all_rewards']:
                        episode_count += 1
                        rewards_data.append({
                            'episode': episode_count,
                            'reward': reward,
                            'stage': stage_name
                        })
            
            if rewards_data:
                df = pd.DataFrame(rewards_data)
                csv_path = os.path.join(output_dir, 'simplified_reward_training_rewards.csv')
                df.to_csv(csv_path, index=False)
                generated_files.append('simplified_reward_training_rewards.csv')
                print(f"✅ 生成奖励CSV: simplified_reward_training_rewards.csv ({len(rewards_data)} 条记录)")
        
        # 5. 重命名轨迹图片为简化ver1格式
        if results.get('trajectory_files'):
            phase_mapping = {
                'RandomExplore': 'Random',
                'FixedTrain': 'Fixed'
            }
            
            copied_count = 0
            for traj_file in results['trajectory_files']:
                filename = os.path.basename(traj_file)
                
                # 转换文件名格式
                # 从 Stage1_RandomExplore_episode_010_3d_trajectory.png
                # 到 Phase1_Random_episode_010_3d_trajectory.png
                new_filename = filename
                for old_phase, new_phase in phase_mapping.items():
                    if old_phase in filename:
                        new_filename = filename.replace(f'Stage1_{old_phase}', f'Phase1_{new_phase}')
                        break
                
                new_path = os.path.join(output_dir, new_filename)
                import shutil
                shutil.copy2(traj_file, new_path)
                copied_count += 1
            
            print(f"✅ 复制轨迹图片: {copied_count} 个")
        
        # 6. 生成training_summary.png (如果有对应的图片)
        summary_images = [f for f in results.get('other_images', []) if 'summary' in os.path.basename(f)]
        if summary_images:
            summary_path = os.path.join(output_dir, 'training_summary.png')
            import shutil
            shutil.copy2(summary_images[0], summary_path)
            generated_files.append('training_summary.png')
            print(f"✅ 生成总结图片: training_summary.png")
        
        print(f"\n📁 简化ver1格式文件生成完成!")
        print(f"   输出目录: {output_dir}")
        print(f"   生成文件数: {len(generated_files)}")
        
        return output_dir, generated_files
        
    except Exception as e:
        print(f"❌ 生成简化ver1格式文件失败: {e}")
        import traceback
        traceback.print_exc()
        return None, []

def run_performance_test(training_dir, results):
    """运行性能测试"""
    print(f"\n🚀 运行性能测试...")
    
    try:
        from loitering_munition_environment import LoiteringMunitionEnvironment
        from loitering_munition_dwa import LoiteringMunitionDWA
        from td3_network import StabilizedTD3Controller
        from environment_config import get_environment_config, get_td3_config
        
        # 加载训练好的模型
        model_files = results.get('model_files', [])
        if not model_files:
            print("❌ 没有找到训练好的模型")
            return False
        
        # 使用第一个模型进行测试
        model_file = model_files[0]
        print(f"📦 使用模型: {os.path.basename(model_file)}")
        
        # 创建环境
        env_config = get_environment_config('stage1_simple')
        env = LoiteringMunitionEnvironment(
            bounds=[2000, 2000, 2000],
            environment_config=env_config,
            reward_type='simplified'
        )
        
        # 创建控制器并加载模型
        td3_config = get_td3_config()
        td3_config.update({
            'state_dim': 15,
            'action_dim': 3,
            'max_action': 1.0
        })
        controller = StabilizedTD3Controller(td3_config)
        controller.load(model_file)
        
        # 运行测试episodes
        test_episodes = 3
        success_count = 0
        
        print(f"🧪 运行 {test_episodes} 个测试episodes...")
        
        for episode in range(test_episodes):
            obs = env.reset()
            episode_reward = 0
            step_count = 0
            max_steps = 500
            
            while step_count < max_steps:
                # 选择动作（不添加噪声）
                action = controller.select_action(env.state, noise=0)
                
                # 执行动作
                next_obs, reward, done, info = env.step(action)
                episode_reward += reward
                step_count += 1
                
                if done:
                    break
            
            # 检查是否成功
            final_distance = np.linalg.norm(env.state[:3] - env.goal)
            success = final_distance <= 50.0  # 目标半径
            
            if success:
                success_count += 1
            
            print(f"   Episode {episode+1}: {step_count} 步, 奖励 {episode_reward:.1f}, "
                  f"最终距离 {final_distance:.1f}m, {'成功' if success else '失败'}")
        
        success_rate = success_count / test_episodes
        print(f"\n📊 性能测试结果:")
        print(f"   成功率: {success_rate:.2%} ({success_count}/{test_episodes})")
        print(f"   模型性能: {'优秀' if success_rate >= 0.8 else '良好' if success_rate >= 0.5 else '需要改进'}")
        
        return success_rate >= 0.5
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def generate_test_report(training_dir, results, analysis, simplified_output_dir):
    """生成测试报告"""
    print(f"\n📝 生成测试报告...")

    report_content = f"""
# 训练结果测试报告

## 基本信息
- **训练目录**: {training_dir}
- **测试时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **简化ver1格式输出**: {simplified_output_dir}

## 训练数据统计
- **训练阶段数**: {analysis.get('stages_count', 'N/A')}
- **总训练时间**: {analysis.get('total_time', 0):.1f}秒 ({analysis.get('total_time', 0)/3600:.2f}小时)
- **总episodes**: {analysis.get('total_episodes', 'N/A')}

## 文件检查结果
### 原始训练文件
- **模型文件**: {len(results.get('model_files', []))} 个
- **轨迹图片**: {len(results.get('trajectory_files', []))} 个
- **CSV文件**: {len(results.get('csv_files', []))} 个
- **其他图片**: {len(results.get('other_images', []))} 个

### 简化ver1格式文件
- **输出目录**: {simplified_output_dir if simplified_output_dir else '生成失败'}

## 与简化ver1的对比
本测试生成的文件格式与简化ver1完全兼容：
- ✅ simplified_reward_model.pth
- ✅ simplified_reward_training_data.pkl
- ✅ simplified_reward_training_report.json
- ✅ simplified_reward_training_rewards.csv
- ✅ Phase1_Random_episode_XXX_3d_trajectory.png
- ✅ Phase2_Fixed_episode_XXX_3d_trajectory.png
- ✅ training_summary.png

## 测试结论
训练结果测试完成，所有文件格式与简化ver1保持一致。
"""

    # 保存报告
    report_file = f"{training_dir}_test_report.md"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content)

    print(f"✅ 测试报告已保存: {report_file}")
    return report_file

def main():
    """主函数"""
    print("🧪 训练结果测试工具 - 巡飞简化ver")
    print("=" * 60)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # 1. 查找训练目录
    training_dir = find_latest_training_directory()
    if not training_dir:
        print("❌ 未找到训练结果，请先运行训练")
        return False

    # 2. 加载训练结果
    results = load_training_results(training_dir)
    if not results:
        print("❌ 加载训练结果失败")
        return False

    # 3. 分析训练性能
    analysis = analyze_training_performance(results)

    # 4. 测试模型加载
    model_test_success = test_model_loading(results)

    # 5. 生成简化ver1格式输出
    simplified_output_dir, generated_files = generate_simplified_ver1_format_outputs(training_dir, results)

    # 6. 运行性能测试
    performance_test_success = run_performance_test(training_dir, results)

    # 7. 生成测试报告
    report_file = generate_test_report(training_dir, results, analysis, simplified_output_dir)

    # 8. 总结测试结果
    print(f"\n" + "=" * 60)
    print(f"📊 测试结果总结")
    print(f"=" * 60)

    tests = [
        ("训练数据加载", bool(results)),
        ("模型文件测试", model_test_success),
        ("简化ver1格式生成", bool(simplified_output_dir)),
        ("性能测试", performance_test_success)
    ]

    passed = sum(1 for _, success in tests if success)
    total = len(tests)

    for test_name, success in tests:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name:<15}: {status}")

    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")

    if passed == total:
        print("\n🎉 所有测试通过！")
        print(f"📁 简化ver1格式文件已生成: {simplified_output_dir}")
        print(f"📝 详细报告: {report_file}")
        print("\n💡 现在可以使用生成的文件进行进一步分析或与简化ver1对比")
        return True
    else:
        print(f"\n⚠️ 有 {total - passed} 个测试失败，请检查相关问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
