# 简化奖励函数训练系统 - 文件夹结构

## 📁 核心文件结构

```
简化ver/
├── 🚀 主要运行文件
│   ├── run_simplified_training.py          # 快速启动脚本（推荐使用）
│   ├── train_simplified_reward.py          # 主训练脚本
│   └── test_dwa_rl.py                      # 测试脚本
│
├── 🔧 核心代码文件
│   ├── dwa_rl_core.py                      # 环境和控制器核心代码
│   ├── simple_environment.py              # 基础环境定义
│   └── td3_dwa_rl_architecture.py         # TD3算法架构
│
├── 🛠️ 工具文件
│   ├── fix_font_display.py                # 字体显示修复工具
│   └── font_config.py                     # 字体配置文件
│
├── 📚 文档说明
│   ├── README_简化奖励函数训练.md          # 主要说明文档
│   ├── 简化奖励函数训练说明.md             # 详细使用指南
│   ├── 字体显示修复指南.md                # 字体问题解决方案
│   └── 文件夹结构说明.md                  # 本文件
│
└── 📊 训练输出示例
    └── simplified_reward_training_*/       # 训练结果目录
        ├── episode_*_3d_trajectory.png     # 3D轨迹图
        ├── training_summary.png            # 训练总结图
        ├── simplified_reward_model.pth     # 训练好的模型
        ├── simplified_reward_training_report.json  # 详细报告
        └── simplified_reward_training_rewards.csv  # 奖励数据
```

## 🎯 文件功能说明

### 主要运行文件
- **run_simplified_training.py**: 最简单的启动方式，提供交互式配置
- **train_simplified_reward.py**: 完整的训练脚本，支持命令行参数
- **test_dwa_rl.py**: 用于测试训练好的模型

### 核心代码文件
- **dwa_rl_core.py**: 包含简化奖励函数的环境和TD3控制器
- **simple_environment.py**: 基础的3D无人机环境
- **td3_dwa_rl_architecture.py**: TD3深度强化学习算法实现

### 工具文件
- **fix_font_display.py**: 检测和修复matplotlib中文字体显示问题
- **font_config.py**: 自动生成的字体配置文件

## 🚀 使用方法

### 快速开始
```bash
cd 简化ver
python run_simplified_training.py
```

### 自定义训练
```bash
python train_simplified_reward.py --episodes 200 --viz-interval 10
```

### 字体问题修复
```bash
python fix_font_display.py
```

## 📊 输出说明

每次训练会创建一个新的输出目录，包含：
- 训练好的模型文件
- 定期生成的3D轨迹图
- 训练总结图表
- 详细的训练数据和报告

## 🔧 系统特性

- ✅ 专用简化奖励函数设计
- ✅ 自动3D轨迹可视化
- ✅ 中文字体自动检测和配置
- ✅ 完整的训练数据记录
- ✅ 用户友好的交互界面

## 📝 注意事项

1. 所有不相关的文件已被清理
2. 保留了一个训练输出示例供参考
3. 系统会自动处理字体显示问题
4. 支持Windows、macOS、Linux系统

---

**现在您有一个干净、专注的简化奖励函数训练系统！** 🎉
