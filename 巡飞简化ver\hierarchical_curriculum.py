"""
分层课程学习策略 - 巡飞弹渐进式安全强化学习
"""

import numpy as np
import torch
import torch.nn as nn
from typing import Dict, List, Tuple, Optional, Callable
from enum import Enum
import json
import os

class CurriculumLevel(Enum):
    """课程学习层级"""
    BASIC_NAVIGATION = 1      # 基础导航
    OBSTACLE_AVOIDANCE = 2    # 障碍物规避
    COMPLEX_ENVIRONMENT = 3   # 复杂环境
    DYNAMIC_THREATS = 4       # 动态威胁
    MISSION_OPTIMIZATION = 5  # 任务优化

class CurriculumStage:
    """课程学习阶段"""
    
    def __init__(self, level: CurriculumLevel, name: str, difficulty: float):
        self.level = level
        self.name = name
        self.difficulty = difficulty  # 0-1难度系数
        self.completion_criteria = {}
        self.environment_config = {}
        self.training_config = {}
        self.prerequisites = []
        
    def set_completion_criteria(self, criteria: Dict):
        """设置完成标准"""
        self.completion_criteria = criteria
    
    def set_environment_config(self, config: Dict):
        """设置环境配置"""
        self.environment_config = config
    
    def set_training_config(self, config: Dict):
        """设置训练配置"""
        self.training_config = config
    
    def add_prerequisite(self, stage_name: str):
        """添加前置条件"""
        self.prerequisites.append(stage_name)

class HierarchicalCurriculumLearning:
    """分层课程学习策略"""
    
    def __init__(self):
        self.stages = {}
        self.current_stage = None
        self.stage_history = []
        self.performance_tracker = {}
        
        # 初始化课程阶段
        self._initialize_curriculum_stages()
    
    def _initialize_curriculum_stages(self):
        """初始化课程学习阶段"""
        
        # 阶段1：基础导航
        stage1 = CurriculumStage(
            CurriculumLevel.BASIC_NAVIGATION,
            "基础导航训练",
            0.1
        )
        stage1.set_completion_criteria({
            'success_rate': 0.8,      # 成功率80%
            'avg_reward': 50.0,       # 平均奖励50
            'episodes': 100,          # 最少100个episode
            'consecutive_success': 10  # 连续成功10次
        })
        stage1.set_environment_config({
            'obstacle_count': (0, 1),     # 0-1个障碍物
            'obstacle_radius': (30, 50),  # 小障碍物
            'environment_size': [1000, 1000, 100],  # 小环境
            'goal_distance': (200, 400)   # 短距离目标
        })
        stage1.set_training_config({
            'episodes': 200,
            'max_steps': 1000,
            'learning_rate': 3e-4,
            'exploration_noise': 0.3
        })
        self.stages['stage1'] = stage1
        
        # 阶段2：障碍物规避
        stage2 = CurriculumStage(
            CurriculumLevel.OBSTACLE_AVOIDANCE,
            "障碍物规避训练",
            0.3
        )
        stage2.set_completion_criteria({
            'success_rate': 0.75,
            'avg_reward': 40.0,
            'episodes': 150,
            'collision_rate': 0.1,    # 碰撞率低于10%
            'consecutive_success': 8
        })
        stage2.set_environment_config({
            'obstacle_count': (2, 4),
            'obstacle_radius': (50, 100),
            'environment_size': [1500, 1500, 150],
            'goal_distance': (300, 600),
            'obstacle_placement': 'path_blocking'  # 路径阻挡型
        })
        stage2.set_training_config({
            'episodes': 300,
            'max_steps': 1500,
            'learning_rate': 3e-4,
            'exploration_noise': 0.25
        })
        stage2.add_prerequisite('stage1')
        self.stages['stage2'] = stage2
        
        # 阶段3：复杂环境
        stage3 = CurriculumStage(
            CurriculumLevel.COMPLEX_ENVIRONMENT,
            "复杂环境训练",
            0.6
        )
        stage3.set_completion_criteria({
            'success_rate': 0.7,
            'avg_reward': 35.0,
            'episodes': 200,
            'collision_rate': 0.08,
            'path_efficiency': 0.6,   # 路径效率60%
            'consecutive_success': 6
        })
        stage3.set_environment_config({
            'obstacle_count': (5, 8),
            'obstacle_radius': (80, 150),
            'environment_size': [2000, 2000, 200],
            'goal_distance': (500, 1000),
            'obstacle_placement': 'mixed',  # 混合布局
            'dynamic_obstacles': True,      # 动态障碍物
            'obstacle_movement': 'linear'   # 线性运动
        })
        stage3.set_training_config({
            'episodes': 500,
            'max_steps': 2000,
            'learning_rate': 2e-4,
            'exploration_noise': 0.2
        })
        stage3.add_prerequisite('stage2')
        self.stages['stage3'] = stage3
        
        # 阶段4：动态威胁
        stage4 = CurriculumStage(
            CurriculumLevel.DYNAMIC_THREATS,
            "动态威胁规避训练",
            0.8
        )
        stage4.set_completion_criteria({
            'success_rate': 0.65,
            'avg_reward': 30.0,
            'episodes': 250,
            'collision_rate': 0.06,
            'threat_avoidance_rate': 0.8,  # 威胁规避率80%
            'path_efficiency': 0.55,
            'consecutive_success': 5
        })
        stage4.set_environment_config({
            'obstacle_count': (6, 10),
            'obstacle_radius': (100, 200),
            'environment_size': [2500, 2500, 250],
            'goal_distance': (700, 1200),
            'dynamic_obstacles': True,
            'obstacle_movement': 'intelligent',  # 智能运动
            'threat_detection': True,            # 威胁检测
            'interference_level': 0.3           # 干扰水平
        })
        stage4.set_training_config({
            'episodes': 800,
            'max_steps': 2500,
            'learning_rate': 1e-4,
            'exploration_noise': 0.15
        })
        stage4.add_prerequisite('stage3')
        self.stages['stage4'] = stage4
        
        # 阶段5：任务优化
        stage5 = CurriculumStage(
            CurriculumLevel.MISSION_OPTIMIZATION,
            "任务优化训练",
            1.0
        )
        stage5.set_completion_criteria({
            'success_rate': 0.6,
            'avg_reward': 25.0,
            'episodes': 300,
            'collision_rate': 0.05,
            'threat_avoidance_rate': 0.85,
            'path_efficiency': 0.7,
            'fuel_efficiency': 0.8,    # 燃料效率80%
            'time_efficiency': 0.75,   # 时间效率75%
            'consecutive_success': 4
        })
        stage5.set_environment_config({
            'obstacle_count': (8, 12),
            'obstacle_radius': (120, 250),
            'environment_size': [3000, 3000, 300],
            'goal_distance': (800, 1500),
            'dynamic_obstacles': True,
            'obstacle_movement': 'adaptive',     # 自适应运动
            'threat_detection': True,
            'interference_level': 0.5,
            'fuel_constraint': True,             # 燃料约束
            'time_constraint': True,             # 时间约束
            'multi_objective': True              # 多目标优化
        })
        stage5.set_training_config({
            'episodes': 1000,
            'max_steps': 3000,
            'learning_rate': 5e-5,
            'exploration_noise': 0.1
        })
        stage5.add_prerequisite('stage4')
        self.stages['stage5'] = stage5
    
    def get_current_stage(self):
        """获取当前阶段"""
        return self.current_stage
    
    def get_next_stage(self):
        """获取下一个阶段"""
        if self.current_stage is None:
            return 'stage1'
        
        current_index = int(self.current_stage.replace('stage', ''))
        next_index = current_index + 1
        next_stage = f'stage{next_index}'
        
        if next_stage in self.stages:
            return next_stage
        return None
    
    def can_progress_to_stage(self, stage_name: str) -> bool:
        """检查是否可以进入指定阶段"""
        if stage_name not in self.stages:
            return False
        
        stage = self.stages[stage_name]
        
        # 检查前置条件
        for prereq in stage.prerequisites:
            if prereq not in self.performance_tracker:
                return False
            if not self.performance_tracker[prereq].get('completed', False):
                return False
        
        return True
    
    def evaluate_stage_completion(self, stage_name: str, performance_metrics: Dict) -> bool:
        """评估阶段完成情况"""
        if stage_name not in self.stages:
            return False
        
        stage = self.stages[stage_name]
        criteria = stage.completion_criteria
        
        # 检查各项完成标准
        for criterion, required_value in criteria.items():
            if criterion not in performance_metrics:
                return False
            
            actual_value = performance_metrics[criterion]
            
            # 根据标准类型进行判断
            if criterion in ['success_rate', 'path_efficiency', 'threat_avoidance_rate', 
                           'fuel_efficiency', 'time_efficiency']:
                if actual_value < required_value:
                    return False
            elif criterion in ['avg_reward']:
                if actual_value < required_value:
                    return False
            elif criterion in ['collision_rate']:
                if actual_value > required_value:
                    return False
            elif criterion in ['episodes', 'consecutive_success']:
                if actual_value < required_value:
                    return False
        
        return True
    
    def start_stage(self, stage_name: str):
        """开始指定阶段"""
        if not self.can_progress_to_stage(stage_name):
            raise ValueError(f"无法进入阶段 {stage_name}，前置条件不满足")
        
        self.current_stage = stage_name
        stage = self.stages[stage_name]
        
        # 记录阶段开始
        self.stage_history.append({
            'stage': stage_name,
            'start_time': len(self.stage_history),
            'difficulty': stage.difficulty
        })
        
        print(f"🚀 开始阶段: {stage.name} (难度: {stage.difficulty:.1f})")
        return stage
    
    def update_performance(self, stage_name: str, metrics: Dict):
        """更新性能指标"""
        if stage_name not in self.performance_tracker:
            self.performance_tracker[stage_name] = {
                'metrics': [],
                'completed': False,
                'completion_time': None
            }
        
        self.performance_tracker[stage_name]['metrics'].append(metrics)
        
        # 检查是否完成
        if self.evaluate_stage_completion(stage_name, metrics):
            self.performance_tracker[stage_name]['completed'] = True
            self.performance_tracker[stage_name]['completion_time'] = len(
                self.performance_tracker[stage_name]['metrics']
            )
            print(f"✅ 阶段 {stage_name} 完成!")
    
    def get_stage_config(self, stage_name: str) -> Dict:
        """获取阶段配置"""
        if stage_name not in self.stages:
            return {}
        
        stage = self.stages[stage_name]
        return {
            'environment': stage.environment_config,
            'training': stage.training_config,
            'completion_criteria': stage.completion_criteria
        }
    
    def get_curriculum_progress(self) -> Dict:
        """获取课程学习进度"""
        progress = {
            'total_stages': len(self.stages),
            'completed_stages': 0,
            'current_stage': self.current_stage,
            'stage_details': {}
        }
        
        for stage_name, stage in self.stages.items():
            stage_info = {
                'name': stage.name,
                'difficulty': stage.difficulty,
                'completed': self.performance_tracker.get(stage_name, {}).get('completed', False),
                'prerequisites': stage.prerequisites
            }
            
            if stage_name in self.performance_tracker:
                stage_info['metrics'] = self.performance_tracker[stage_name]['metrics']
                stage_info['completion_time'] = self.performance_tracker[stage_name]['completion_time']
            
            progress['stage_details'][stage_name] = stage_info
            
            if stage_info['completed']:
                progress['completed_stages'] += 1
        
        progress['completion_rate'] = progress['completed_stages'] / progress['total_stages']
        return progress
    
    def save_curriculum_state(self, filepath: str):
        """保存课程学习状态"""
        state = {
            'current_stage': self.current_stage,
            'stage_history': self.stage_history,
            'performance_tracker': self.performance_tracker
        }
        
        with open(filepath, 'w') as f:
            json.dump(state, f, indent=2)
    
    def load_curriculum_state(self, filepath: str):
        """加载课程学习状态"""
        if os.path.exists(filepath):
            with open(filepath, 'r') as f:
                state = json.load(f)
            
            self.current_stage = state.get('current_stage')
            self.stage_history = state.get('stage_history', [])
            self.performance_tracker = state.get('performance_tracker', {})

class AdaptiveCurriculumLearning(HierarchicalCurriculumLearning):
    """自适应课程学习"""
    
    def __init__(self):
        super().__init__()
        self.adaptation_threshold = 0.1
        self.performance_window = 50
    
    def adapt_curriculum(self, recent_performance: List[Dict]):
        """根据最近性能自适应调整课程"""
        if len(recent_performance) < self.performance_window:
            return
        
        # 分析性能趋势
        success_rates = [p.get('success_rate', 0) for p in recent_performance]
        avg_rewards = [p.get('avg_reward', 0) for p in recent_performance]
        
        # 计算性能趋势
        success_trend = np.polyfit(range(len(success_rates)), success_rates, 1)[0]
        reward_trend = np.polyfit(range(len(avg_rewards)), avg_rewards, 1)[0]
        
        # 根据趋势调整难度
        if success_trend > self.adaptation_threshold and reward_trend > 0:
            # 性能提升，可以增加难度
            self._increase_difficulty()
        elif success_trend < -self.adaptation_threshold or reward_trend < 0:
            # 性能下降，需要降低难度
            self._decrease_difficulty()
    
    def _increase_difficulty(self):
        """增加难度"""
        if self.current_stage:
            stage = self.stages[self.current_stage]
            # 增加障碍物数量或大小
            if 'obstacle_count' in stage.environment_config:
                current_range = stage.environment_config['obstacle_count']
                stage.environment_config['obstacle_count'] = (
                    current_range[0] + 1, current_range[1] + 1
                )
            print("📈 增加训练难度")
    
    def _decrease_difficulty(self):
        """降低难度"""
        if self.current_stage:
            stage = self.stages[self.current_stage]
            # 减少障碍物数量或大小
            if 'obstacle_count' in stage.environment_config:
                current_range = stage.environment_config['obstacle_count']
                stage.environment_config['obstacle_count'] = (
                    max(0, current_range[0] - 1), max(1, current_range[1] - 1)
                )
            print("📉 降低训练难度")

# 使用示例
if __name__ == "__main__":
    # 创建分层课程学习策略
    curriculum = HierarchicalCurriculumLearning()
    
    # 开始第一阶段
    stage1 = curriculum.start_stage('stage1')
    print(f"阶段配置: {curriculum.get_stage_config('stage1')}")
    
    # 模拟性能更新
    performance_metrics = {
        'success_rate': 0.85,
        'avg_reward': 55.0,
        'episodes': 120,
        'consecutive_success': 12
    }
    
    curriculum.update_performance('stage1', performance_metrics)
    
    # 检查是否可以进入下一阶段
    if curriculum.can_progress_to_stage('stage2'):
        stage2 = curriculum.start_stage('stage2')
        print(f"进入阶段2: {stage2.name}")
    
    # 获取课程进度
    progress = curriculum.get_curriculum_progress()
    print(f"课程进度: {progress['completion_rate']:.1%}")
    
    # 保存状态
    curriculum.save_curriculum_state('curriculum_state.json')
