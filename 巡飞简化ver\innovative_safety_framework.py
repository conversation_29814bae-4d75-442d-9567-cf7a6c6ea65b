"""
多层级安全约束架构 - 巡飞弹创新安全强化学习框架
"""

import numpy as np
import torch
import torch.nn as nn
from enum import Enum
from typing import List, Dict, Tuple, Optional

class SafetyLevel(Enum):
    """安全约束层级枚举"""
    PHYSICAL = 1      # 物理约束层（速度、加速度等）
    ENVIRONMENTAL = 2 # 环境约束层（障碍物、边界等）
    MISSION = 3       # 任务约束层（燃料、时间等）
    DYNAMIC = 4       # 动态约束层（威胁、干扰等）

class MultiLevelSafetyConstraint:
    """多层级安全约束架构"""
    
    def __init__(self):
        self.constraint_layers = {
            SafetyLevel.PHYSICAL: PhysicalConstraintLayer(),
            SafetyLevel.ENVIRONMENTAL: EnvironmentalConstraintLayer(),
            SafetyLevel.MISSION: MissionConstraintLayer(),
            SafetyLevel.DYNAMIC: DynamicConstraintLayer()
        }
        
        # 约束优先级权重
        self.priority_weights = {
            SafetyLevel.PHYSICAL: 1.0,      # 最高优先级
            SafetyLevel.ENVIRONMENTAL: 0.8,
            SafetyLevel.MISSION: 0.6,
            SafetyLevel.DYNAMIC: 0.4        # 最低优先级
        }
    
    def generate_safe_actions(self, state, all_actions, context=None):
        """生成多层级安全动作集合"""
        safe_actions = all_actions.copy()
        constraint_violations = {}
        
        # 逐层级应用约束
        for level in SafetyLevel:
            layer = self.constraint_layers[level]
            safe_actions, violations = layer.apply_constraints(
                state, safe_actions, context
            )
            constraint_violations[level] = violations
            
            # 如果某层级没有安全动作，返回空集合
            if len(safe_actions) == 0:
                print(f"⚠️ {level.name}层级约束过滤后无安全动作")
                return [], constraint_violations
        
        return safe_actions, constraint_violations

class PhysicalConstraintLayer:
    """物理约束层 - 速度、加速度、角度限制"""
    
    def __init__(self):
        self.V_min = 15.0    # 最小速度
        self.V_max = 60.0    # 最大速度
        self.a_T_max = 8.0   # 最大切向加速度
        self.a_N_max = 39.24 # 最大法向加速度
        self.gamma_max = np.pi/3  # 最大倾斜角
    
    def apply_constraints(self, state, actions, context=None):
        """应用物理约束"""
        violations = []
        safe_actions = []
        
        for action in actions:
            a_T, a_N, mu = action
            
            # 检查加速度约束
            if abs(a_T) > self.a_T_max or abs(a_N) > self.a_N_max:
                violations.append(f"加速度超限: a_T={a_T:.2f}, a_N={a_N:.2f}")
                continue
            
            # 检查角度约束
            if abs(mu) > self.gamma_max:
                violations.append(f"倾斜角超限: mu={mu:.2f}")
                continue
            
            # 预测下一状态并检查速度约束
            next_state = self._predict_next_state(state, action)
            V_next = next_state[3]
            
            if V_next < self.V_min or V_next > self.V_max:
                violations.append(f"速度约束违反: V={V_next:.2f}")
                continue
            
            safe_actions.append(action)
        
        return safe_actions, violations
    
    def _predict_next_state(self, state, action):
        """预测下一状态"""
        x, y, z, V, gamma, psi = state
        a_T, a_N, mu = action
        dt = 0.1
        g = 9.81
        
        # 简化的运动学预测
        V_next = V + (a_T - g * np.sin(gamma)) * dt
        gamma_next = gamma + (a_N * np.cos(mu) - g * np.cos(gamma)) / V * dt
        psi_next = psi + (a_N * np.sin(mu)) / (V * np.cos(gamma)) * dt
        
        return np.array([x, y, z, V_next, gamma_next, psi_next])

class EnvironmentalConstraintLayer:
    """环境约束层 - 障碍物、边界限制"""
    
    def __init__(self):
        self.d_safe = 5.0  # 安全距离
        self.predict_time = 2.0  # 预测时间窗口
    
    def apply_constraints(self, state, actions, context=None):
        """应用环境约束"""
        violations = []
        safe_actions = []
        
        obstacles = context.get('obstacles', []) if context else []
        bounds = context.get('bounds', [2000, 2000, 200]) if context else [2000, 2000, 200]
        
        for action in actions:
            # 预测轨迹
            trajectory = self._predict_trajectory(state, action)
            
            # 检查障碍物碰撞
            if self._check_collision(trajectory, obstacles):
                violations.append("障碍物碰撞预测")
                continue
            
            # 检查边界约束
            if self._check_boundary_violation(trajectory, bounds):
                violations.append("边界约束违反")
                continue
            
            safe_actions.append(action)
        
        return safe_actions, violations
    
    def _predict_trajectory(self, state, action, steps=20):
        """预测轨迹"""
        trajectory = []
        current_state = state.copy()
        
        for _ in range(steps):
            current_state = self._predict_next_state(current_state, action)
            trajectory.append(current_state[:3])  # 只取位置信息
        
        return np.array(trajectory)
    
    def _predict_next_state(self, state, action):
        """预测下一状态"""
        x, y, z, V, gamma, psi = state
        a_T, a_N, mu = action
        dt = 0.1
        g = 9.81
        
        # 运动学更新
        V_next = V + (a_T - g * np.sin(gamma)) * dt
        gamma_next = gamma + (a_N * np.cos(mu) - g * np.cos(gamma)) / V * dt
        psi_next = psi + (a_N * np.sin(mu)) / (V * np.cos(gamma)) * dt
        
        # 位置更新
        x_next = x + V * np.cos(gamma) * np.cos(psi) * dt
        y_next = y + V * np.cos(gamma) * np.sin(psi) * dt
        z_next = z + V * np.sin(gamma) * dt
        
        return np.array([x_next, y_next, z_next, V_next, gamma_next, psi_next])
    
    def _check_collision(self, trajectory, obstacles):
        """检查碰撞"""
        for point in trajectory:
            for obstacle in obstacles:
                distance = np.linalg.norm(point - obstacle['center'])
                if distance < obstacle['radius'] + self.d_safe:
                    return True
        return False
    
    def _check_boundary_violation(self, trajectory, bounds):
        """检查边界违反"""
        for point in trajectory:
            if (point[0] < 0 or point[0] > bounds[0] or
                point[1] < 0 or point[1] > bounds[1] or
                point[2] < 0 or point[2] > bounds[2]):
                return True
        return False

class MissionConstraintLayer:
    """任务约束层 - 燃料、时间、任务目标"""
    
    def __init__(self):
        self.max_fuel = 1000.0  # 最大燃料
        self.max_time = 2000    # 最大时间步
        self.min_progress = 0.1  # 最小进度要求
    
    def apply_constraints(self, state, actions, context=None):
        """应用任务约束"""
        violations = []
        safe_actions = []
        
        current_fuel = context.get('fuel', self.max_fuel) if context else self.max_fuel
        current_time = context.get('time', 0) if context else 0
        initial_goal_dist = context.get('initial_goal_dist', 1000) if context else 1000
        
        for action in actions:
            # 检查燃料消耗
            fuel_cost = self._estimate_fuel_cost(action)
            if current_fuel - fuel_cost < 0:
                violations.append("燃料不足")
                continue
            
            # 检查时间约束
            if current_time >= self.max_time:
                violations.append("时间超限")
                continue
            
            # 检查进度要求
            if not self._check_progress_requirement(state, action, initial_goal_dist):
                violations.append("进度不足")
                continue
            
            safe_actions.append(action)
        
        return safe_actions, violations
    
    def _estimate_fuel_cost(self, action):
        """估算燃料消耗"""
        a_T, a_N, mu = action
        # 简化的燃料消耗模型
        return np.sqrt(a_T**2 + a_N**2) * 0.1
    
    def _check_progress_requirement(self, state, action, initial_goal_dist):
        """检查进度要求"""
        # 简化的进度检查
        return True  # 这里可以根据具体需求实现

class DynamicConstraintLayer:
    """动态约束层 - 威胁、干扰、动态环境"""
    
    def __init__(self):
        self.threat_detection_range = 300.0  # 威胁检测范围
        self.interference_threshold = 0.5    # 干扰阈值
    
    def apply_constraints(self, state, actions, context=None):
        """应用动态约束"""
        violations = []
        safe_actions = []
        
        threats = context.get('threats', []) if context else []
        interference_level = context.get('interference', 0.0) if context else 0.0
        
        for action in actions:
            # 检查威胁规避
            if self._check_threat_exposure(state, action, threats):
                violations.append("威胁暴露")
                continue
            
            # 检查干扰影响
            if interference_level > self.interference_threshold:
                if not self._is_robust_action(action, interference_level):
                    violations.append("干扰敏感")
                    continue
            
            safe_actions.append(action)
        
        return safe_actions, violations
    
    def _check_threat_exposure(self, state, action, threats):
        """检查威胁暴露"""
        # 简化的威胁检查
        return False
    
    def _is_robust_action(self, action, interference_level):
        """检查动作的鲁棒性"""
        # 简化的鲁棒性检查
        return True

class AdaptiveSafetyController:
    """自适应安全控制器"""
    
    def __init__(self):
        self.safety_framework = MultiLevelSafetyConstraint()
        self.adaptation_history = []
        self.performance_metrics = {}
    
    def adapt_constraints(self, performance_history, violation_history):
        """根据历史性能自适应调整约束"""
        # 分析约束违反模式
        violation_patterns = self._analyze_violations(violation_history)
        
        # 调整约束参数
        self._adjust_constraint_parameters(violation_patterns)
        
        # 更新优先级权重
        self._update_priority_weights(performance_history)
    
    def _analyze_violations(self, violation_history):
        """分析约束违反模式"""
        patterns = {}
        for level in SafetyLevel:
            patterns[level] = {
                'frequency': 0,
                'severity': 0,
                'context': []
            }
        
        for violation in violation_history:
            level = violation.get('level')
            if level in patterns:
                patterns[level]['frequency'] += 1
                patterns[level]['severity'] += violation.get('severity', 1)
                patterns[level]['context'].append(violation.get('context', {}))
        
        return patterns
    
    def _adjust_constraint_parameters(self, violation_patterns):
        """调整约束参数"""
        # 根据违反模式调整各层约束参数
        for level, pattern in violation_patterns.items():
            if pattern['frequency'] > 10:  # 频繁违反
                self._tighten_constraints(level)
            elif pattern['frequency'] < 2:  # 很少违反
                self._relax_constraints(level)
    
    def _update_priority_weights(self, performance_history):
        """更新优先级权重"""
        # 根据性能历史调整权重
        pass
    
    def _tighten_constraints(self, level):
        """收紧约束"""
        pass
    
    def _relax_constraints(self, level):
        """放松约束"""
        pass

# 使用示例
if __name__ == "__main__":
    # 创建多层级安全约束架构
    safety_framework = MultiLevelSafetyConstraint()
    
    # 示例状态和动作
    state = np.array([100, 100, 100, 25, 0, 0])
    all_actions = [
        np.array([2.0, 5.0, 0.1]),
        np.array([-1.0, 3.0, 0.2]),
        np.array([0.0, 10.0, 0.5])
    ]
    
    context = {
        'obstacles': [{'center': np.array([200, 200, 200]), 'radius': 50}],
        'bounds': [2000, 2000, 200],
        'fuel': 800,
        'time': 100,
        'initial_goal_dist': 1000
    }
    
    # 生成安全动作
    safe_actions, violations = safety_framework.generate_safe_actions(
        state, all_actions, context
    )
    
    print(f"安全动作数量: {len(safe_actions)}")
    print(f"约束违反: {violations}")
