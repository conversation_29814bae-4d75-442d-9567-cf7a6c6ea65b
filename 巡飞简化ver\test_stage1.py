"""
测试阶段1模型 - 简单环境基础训练
成功率: 98.3% | 环境: 简单静态障碍物
"""

import subprocess
import sys
import os
from datetime import datetime

def main():
    print("🎬 阶段1模型测试 - 简单环境基础训练")
    print("=" * 50)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("📊 预期成功率: 98.3%")
    print("🌍 测试环境: 简单静态障碍物")
    print("⚙️  测试参数: 最大步数=1500, 帧率=12fps")
    print("=" * 50)
    
    print("🚀 开始生成阶段1的GIF动画...")

    # 获取脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    gif_generator_path = os.path.join(script_dir, 'test_training_gif_generator.py')

    # 调用完整的GIF生成器
    cmd = [sys.executable, gif_generator_path, '--stage', '1', '--steps', '1500', '--fps', '12']
    print(f"🔧 执行命令: {' '.join(cmd)}")

    try:
        # 直接运行，显示实时输出
        result = subprocess.run(cmd)
        if result.returncode == 0:
            print("\n✅ 阶段1 GIF生成完成!")
            print("📁 请查看生成的文件:")
            print("   • results/gifs/stage1/loitering_munition_stage1_*.gif")
            print("   • results/analysis/loitering_munition_analysis_*.png")
            print("   • results/analysis/loitering_munition_constraints_*.png")
        else:
            print("\n❌ 阶段1 GIF生成失败")
    except Exception as e:
        print(f"\n❌ 执行出错: {e}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n👋 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序运行出现错误: {e}")
