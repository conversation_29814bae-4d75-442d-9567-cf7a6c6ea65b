# Results 目录结构说明

本目录用于统一存储所有训练和测试生成的文件。

## 目录结构

```
results/
├── training/           # 训练相关文件
│   ├── models/        # 训练好的模型文件 (.pth)
│   ├── data/          # 训练数据和统计信息 (.pkl, .json)
│   ├── plots/         # 训练过程图表 (.png)
│   └── logs/          # 训练日志文件
├── gifs/              # 生成的动画文件
│   ├── stage1/        # 阶段1测试动画
│   ├── stage2/        # 阶段2测试动画
│   └── stage3/        # 阶段3测试动画
├── models/            # 最终模型文件
├── logs/              # 系统日志
└── analysis/          # 分析结果文件

```

## 文件命名规范

### 训练文件
- 模型文件: `stage{N}_model_{timestamp}.pth`
- 训练数据: `stage{N}_training_data_{timestamp}.pkl`
- 训练报告: `stage{N}_training_report_{timestamp}.json`
- 轨迹图: `Stage{N}_{phase}_episode_{num}_3d_trajectory.png`

### GIF动画文件
- 成功案例: `loitering_munition_stage{N}_SUCCESS_{timestamp}.gif`
- 失败案例: `loitering_munition_stage{N}_FAILED_{timestamp}.gif`

### 日志文件
- 训练日志: `training_{timestamp}.log`
- 测试日志: `test_stage{N}_{timestamp}.log`

## 使用说明

所有训练和测试脚本会自动将生成的文件保存到相应的子目录中，无需手动管理文件位置。
