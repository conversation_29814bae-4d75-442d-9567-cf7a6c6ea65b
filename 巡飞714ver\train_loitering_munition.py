"""
巡飞弹分阶段训练主程序
集成六自由度环境、三项奖励函数、分阶段训练
"""

import numpy as np
import torch
import time
import os
import json
from datetime import datetime
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from mpl_toolkits.mplot3d import Axes3D

from loitering_munition_env import LoiteringMunitionEnvironment
from loitering_munition_dwa import LoiteringMunitionDWA
from scenario_config import get_scenario_config
from td3_network import TD3Agent, ReplayBuffer, map_action_to_control

class LoiteringMunitionTrainer:
    """巡飞弹分阶段训练器"""
    
    def __init__(self, seed=42):
        self.seed = seed
        np.random.seed(seed)
        torch.manual_seed(seed)
        
        # 创建输出目录
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.output_dir = f'loitering_munition_training_{self.timestamp}'
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 训练配置
        self.training_stages = {
            'stage1': {'episodes': 500, 'description': '基础作战场景'},
            'stage2': {'episodes': 1000, 'description': '复杂静态场景'},
            'stage3': {'episodes': 500, 'description': '动态威胁场景'}
        }
        
        print("🚁 巡飞弹分阶段训练系统")
        print("=" * 60)
        print(f"📅 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📁 输出目录: {self.output_dir}")
        print(f"🎲 随机种子: {seed}")
        print()
    
    def train_all_stages(self):
        """执行完整的三阶段训练"""
        print("🎯 开始巡飞弹分阶段训练")
        print("=" * 60)
        
        # 创建TD3智能体
        agent = TD3Agent(state_dim=15, action_dim=3, max_action=1.0)
        replay_buffer = ReplayBuffer(capacity=100000)
        
        # 创建DWA控制器（用于生成安全动作集）
        dwa = LoiteringMunitionDWA()
        
        all_results = {}
        total_start_time = time.time()
        
        # 逐阶段训练
        for stage_name, stage_config in self.training_stages.items():
            print(f"\n🚀 开始 {stage_name}: {stage_config['description']}")
            print("-" * 50)
            
            # 获取场景配置
            scenario_config = get_scenario_config(stage_name)
            
            # 创建环境
            env = LoiteringMunitionEnvironment(
                bounds=scenario_config['environment_bounds'],
                fixed_scenario_config=scenario_config
            )
            
            # 训练该阶段
            stage_results = self._train_stage(
                agent, replay_buffer, dwa, env,
                stage_config['episodes'], stage_name
            )
            
            all_results[stage_name] = stage_results
            
            # 保存阶段模型
            model_path = f"{self.output_dir}/{stage_name}_model.pth"
            agent.save(model_path)
            print(f"💾 {stage_name} 模型已保存: {model_path}")
        
        # 计算总体结果
        total_time = time.time() - total_start_time
        total_episodes = sum(stage['episodes'] for stage in all_results.values())
        overall_success_rate = np.mean([stage['success_rate'] for stage in all_results.values()])
        
        all_results['summary'] = {
            'total_training_time': total_time,
            'total_episodes': total_episodes,
            'overall_success_rate': overall_success_rate
        }
        
        # 保存训练结果
        self._save_results(all_results)
        
        # 生成训练报告
        self._generate_report(all_results)

        # 生成可视化图表
        self._generate_visualizations(all_results)

        print(f"\n🎉 训练完成!")
        print(f"📊 总体成功率: {overall_success_rate:.1%}")
        print(f"⏱️ 总训练时间: {total_time:.1f}秒")
        print(f"📈 可视化图表已保存到: {self.output_dir}/visualizations/")

        return all_results, agent
    
    def _train_stage(self, agent, replay_buffer, dwa, env, num_episodes, stage_name):
        """训练单个阶段"""
        stage_results = {
            'episodes': num_episodes,
            'episode_rewards': [],
            'episode_steps': [],
            'success_count': 0,
            'collision_count': 0,
            'timeout_count': 0
        }
        
        start_time = time.time()
        
        for episode in range(num_episodes):
            # 重置环境
            state = env.reset(verbose=(episode == 0))
            episode_reward = 0
            episode_steps = 0
            
            while True:
                # 选择动作 - 使用改进的DWA（延长DWA引导期）
                if replay_buffer.size() < 2000:
                    # 初期使用改进的DWA生成安全动作
                    safe_controls = dwa.generate_safe_control_set(
                        env.state, env.obstacles + env.dynamic_obstacles, env.goal, max_actions=20
                    )
                    if safe_controls:
                        # 随机选择一个安全控制输入（前期探索）
                        control = safe_controls[np.random.randint(min(len(safe_controls), 10))]
                        # 将控制输入归一化到[-1, 1]
                        action = np.array([
                            control[0] / 8.0,      # a_T
                            control[1] / 39.24,    # a_N
                            control[2] / (np.pi/2) # mu
                        ])
                    else:
                        # 如果没有安全动作，使用紧急制动
                        action = np.array([-0.5, 0.0, 0.0])  # 减速，无转向
                else:
                    # 使用TD3选择动作（增加探索噪声）
                    action = agent.select_action(state, noise=0.2)
                
                # 映射到控制输入
                control = map_action_to_control(action)
                
                # 执行动作
                next_state, reward, done, info = env.step(control)
                
                # 存储经验
                replay_buffer.add(state, action, reward, next_state, done)
                
                # 训练网络
                if replay_buffer.size() > 1000:
                    agent.train(replay_buffer)
                
                state = next_state
                episode_reward += reward
                episode_steps += 1
                
                if done:
                    # 统计结果
                    if info.get('success'):
                        stage_results['success_count'] += 1
                    elif info.get('collision'):
                        stage_results['collision_count'] += 1
                    elif info.get('timeout'):
                        stage_results['timeout_count'] += 1
                    break
            
            stage_results['episode_rewards'].append(episode_reward)
            stage_results['episode_steps'].append(episode_steps)
            
            # 打印进度
            if episode % 100 == 0 or episode == num_episodes - 1:
                avg_reward = np.mean(stage_results['episode_rewards'][-100:])
                success_rate = stage_results['success_count'] / (episode + 1)
                print(f"  Episode {episode:4d}: 平均奖励={avg_reward:7.2f}, "
                      f"成功率={success_rate:.1%}, 步数={episode_steps:3d}")
        
        # 计算阶段统计
        stage_results['training_time'] = time.time() - start_time
        stage_results['success_rate'] = stage_results['success_count'] / num_episodes
        stage_results['avg_reward'] = np.mean(stage_results['episode_rewards'])
        stage_results['avg_steps'] = np.mean(stage_results['episode_steps'])
        
        print(f"✅ {stage_name} 完成:")
        print(f"   成功率: {stage_results['success_rate']:.1%}")
        print(f"   平均奖励: {stage_results['avg_reward']:.2f}")
        print(f"   平均步数: {stage_results['avg_steps']:.1f}")
        print(f"   训练时间: {stage_results['training_time']:.1f}秒")
        
        return stage_results
    
    def _save_results(self, results):
        """保存训练结果"""
        results_file = f"{self.output_dir}/training_results.json"
        
        # 转换numpy数组为列表以便JSON序列化
        json_results = {}
        for stage, data in results.items():
            if isinstance(data, dict):
                json_results[stage] = {}
                for key, value in data.items():
                    if isinstance(value, np.ndarray):
                        json_results[stage][key] = value.tolist()
                    elif isinstance(value, (np.integer, np.floating)):
                        json_results[stage][key] = float(value)
                    else:
                        json_results[stage][key] = value
            else:
                json_results[stage] = data
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(json_results, f, indent=2, ensure_ascii=False)
        
        print(f"📄 训练结果已保存: {results_file}")
    
    def _generate_report(self, results):
        """生成训练报告"""
        report_file = f"{self.output_dir}/training_report.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("巡飞弹分阶段训练报告\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"训练时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"随机种子: {self.seed}\n\n")
            
            f.write("训练配置:\n")
            f.write("- 环境: 巡飞弹六自由度运动学环境\n")
            f.write("- 奖励函数: 三项奖励函数（距离+效率+安全）\n")
            f.write("- 网络架构: TD3（15维状态，3维控制）\n")
            f.write("- 训练策略: 分阶段渐进式训练\n\n")
            
            f.write("各阶段结果:\n")
            for stage_name, stage_data in results.items():
                if stage_name == 'summary':
                    continue
                f.write(f"\n{stage_name}:\n")
                f.write(f"  训练轮数: {stage_data['episodes']}\n")
                f.write(f"  成功率: {stage_data['success_rate']:.1%}\n")
                f.write(f"  平均奖励: {stage_data['avg_reward']:.2f}\n")
                f.write(f"  平均步数: {stage_data['avg_steps']:.1f}\n")
                f.write(f"  训练时间: {stage_data['training_time']:.1f}秒\n")
            
            if 'summary' in results:
                summary = results['summary']
                f.write(f"\n总体结果:\n")
                f.write(f"  总训练时间: {summary['total_training_time']:.1f}秒\n")
                f.write(f"  总训练轮数: {summary['total_episodes']}\n")
                f.write(f"  总体成功率: {summary['overall_success_rate']:.1%}\n")
        
        print(f"📝 训练报告已保存: {report_file}")

    def _generate_visualizations(self, results):
        """生成训练可视化图表"""
        # 创建可视化目录
        viz_dir = f"{self.output_dir}/visualizations"
        os.makedirs(viz_dir, exist_ok=True)

        print("\n📈 生成训练可视化图表...")

        # 1. 训练曲线图
        self._plot_training_curves(results, viz_dir)

        # 2. 成功率对比图
        self._plot_success_rates(results, viz_dir)

        # 3. 各阶段性能对比
        self._plot_stage_comparison(results, viz_dir)

        # 4. 奖励分布图
        self._plot_reward_distribution(results, viz_dir)

        print(f"   ✅ 训练曲线图: {viz_dir}/training_curves.png")
        print(f"   ✅ 成功率图: {viz_dir}/success_rates.png")
        print(f"   ✅ 阶段对比图: {viz_dir}/stage_comparison.png")
        print(f"   ✅ 奖励分布图: {viz_dir}/reward_distribution.png")

    def _plot_training_curves(self, results, viz_dir):
        """绘制训练曲线"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('巡飞弹分阶段训练曲线', fontsize=16, fontweight='bold')

        stages = ['stage1', 'stage2', 'stage3']
        colors = ['blue', 'green', 'red']

        # 奖励曲线
        ax1 = axes[0, 0]
        for i, stage in enumerate(stages):
            if stage in results:
                rewards = results[stage]['episode_rewards']
                # 计算滑动平均
                window = min(50, len(rewards) // 10)
                if window > 1:
                    smoothed = np.convolve(rewards, np.ones(window)/window, mode='valid')
                    ax1.plot(range(window-1, len(rewards)), smoothed,
                            color=colors[i], label=f'{stage} (平滑)', linewidth=2)
                ax1.plot(rewards, color=colors[i], alpha=0.3, linewidth=0.5)

        ax1.set_title('Episode奖励变化')
        ax1.set_xlabel('Episode')
        ax1.set_ylabel('奖励')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 步数曲线
        ax2 = axes[0, 1]
        for i, stage in enumerate(stages):
            if stage in results:
                steps = results[stage]['episode_steps']
                window = min(50, len(steps) // 10)
                if window > 1:
                    smoothed = np.convolve(steps, np.ones(window)/window, mode='valid')
                    ax2.plot(range(window-1, len(steps)), smoothed,
                            color=colors[i], label=f'{stage} (平滑)', linewidth=2)
                ax2.plot(steps, color=colors[i], alpha=0.3, linewidth=0.5)

        ax2.set_title('Episode步数变化')
        ax2.set_xlabel('Episode')
        ax2.set_ylabel('步数')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 成功率曲线
        ax3 = axes[1, 0]
        for i, stage in enumerate(stages):
            if stage in results:
                episodes = results[stage]['episodes']
                success_count = results[stage]['success_count']
                # 计算累积成功率
                cumulative_success = []
                success_so_far = 0
                for ep in range(episodes):
                    if ep < len(results[stage]['episode_rewards']):
                        # 简化：假设正奖励表示成功
                        if results[stage]['episode_rewards'][ep] > 50:
                            success_so_far += 1
                    cumulative_success.append(success_so_far / (ep + 1) * 100)

                ax3.plot(cumulative_success, color=colors[i], label=stage, linewidth=2)

        ax3.set_title('累积成功率变化')
        ax3.set_xlabel('Episode')
        ax3.set_ylabel('成功率 (%)')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 训练时间对比
        ax4 = axes[1, 1]
        stage_names = []
        training_times = []
        for stage in stages:
            if stage in results:
                stage_names.append(stage)
                training_times.append(results[stage]['training_time'] / 60)  # 转换为分钟

        bars = ax4.bar(stage_names, training_times, color=colors[:len(stage_names)])
        ax4.set_title('各阶段训练时间')
        ax4.set_ylabel('时间 (分钟)')

        # 在柱状图上添加数值标签
        for bar, time in zip(bars, training_times):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{time:.1f}min', ha='center', va='bottom')

        plt.tight_layout()
        plt.savefig(f"{viz_dir}/training_curves.png", dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_success_rates(self, results, viz_dir):
        """绘制成功率对比图"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        fig.suptitle('巡飞弹训练成功率分析', fontsize=14, fontweight='bold')

        stages = ['stage1', 'stage2', 'stage3']
        success_rates = []
        collision_rates = []
        timeout_rates = []

        for stage in stages:
            if stage in results:
                total = results[stage]['episodes']
                success = results[stage]['success_count']
                collision = results[stage]['collision_count']
                timeout = results[stage]['timeout_count']

                success_rates.append(success / total * 100)
                collision_rates.append(collision / total * 100)
                timeout_rates.append(timeout / total * 100)

        # 成功率柱状图
        x = np.arange(len(stages))
        width = 0.25

        ax1.bar(x - width, success_rates, width, label='成功', color='green', alpha=0.8)
        ax1.bar(x, collision_rates, width, label='碰撞', color='red', alpha=0.8)
        ax1.bar(x + width, timeout_rates, width, label='超时', color='orange', alpha=0.8)

        ax1.set_title('各阶段结果分布')
        ax1.set_ylabel('百分比 (%)')
        ax1.set_xticks(x)
        ax1.set_xticklabels(stages)
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 添加数值标签
        for i, (success, collision, timeout) in enumerate(zip(success_rates, collision_rates, timeout_rates)):
            ax1.text(i - width, success + 1, f'{success:.1f}%', ha='center', va='bottom', fontsize=9)
            ax1.text(i, collision + 1, f'{collision:.1f}%', ha='center', va='bottom', fontsize=9)
            ax1.text(i + width, timeout + 1, f'{timeout:.1f}%', ha='center', va='bottom', fontsize=9)

        # 成功率趋势图
        ax2.plot(stages, success_rates, marker='o', linewidth=3, markersize=8, color='green')
        ax2.fill_between(stages, success_rates, alpha=0.3, color='green')
        ax2.set_title('成功率变化趋势')
        ax2.set_ylabel('成功率 (%)')
        ax2.grid(True, alpha=0.3)

        # 添加数值标签
        for i, rate in enumerate(success_rates):
            ax2.text(i, rate + 2, f'{rate:.1f}%', ha='center', va='bottom', fontweight='bold')

        plt.tight_layout()
        plt.savefig(f"{viz_dir}/success_rates.png", dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_stage_comparison(self, results, viz_dir):
        """绘制各阶段性能对比雷达图"""
        fig, ax = plt.subplots(figsize=(10, 8), subplot_kw=dict(projection='polar'))

        # 性能指标
        metrics = ['成功率', '平均奖励\n(归一化)', '平均步数\n(归一化)', '训练效率\n(归一化)']

        stages = ['stage1', 'stage2', 'stage3']
        colors = ['blue', 'green', 'red']

        # 计算归一化指标
        all_success_rates = []
        all_avg_rewards = []
        all_avg_steps = []
        all_training_efficiency = []

        for stage in stages:
            if stage in results:
                success_rate = results[stage]['success_rate'] * 100
                avg_reward = results[stage]['avg_reward']
                avg_steps = results[stage]['avg_steps']
                training_time = results[stage]['training_time']
                episodes = results[stage]['episodes']
                training_efficiency = episodes / training_time * 60  # episodes per minute

                all_success_rates.append(success_rate)
                all_avg_rewards.append(avg_reward)
                all_avg_steps.append(avg_steps)
                all_training_efficiency.append(training_efficiency)

        # 归一化到0-100范围
        def normalize_metric(values, reverse=False):
            min_val, max_val = min(values), max(values)
            if max_val == min_val:
                return [50] * len(values)
            normalized = [(v - min_val) / (max_val - min_val) * 100 for v in values]
            return [100 - n for n in normalized] if reverse else normalized

        norm_success = all_success_rates  # 已经是百分比
        norm_rewards = normalize_metric(all_avg_rewards)  # 奖励越高越好
        norm_steps = normalize_metric(all_avg_steps, reverse=True)  # 步数越少越好
        norm_efficiency = normalize_metric(all_training_efficiency)  # 效率越高越好

        # 角度设置
        angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
        angles += angles[:1]  # 闭合图形

        # 绘制每个阶段
        for i, stage in enumerate(stages):
            if stage in results:
                values = [norm_success[i], norm_rewards[i], norm_steps[i], norm_efficiency[i]]
                values += values[:1]  # 闭合图形

                ax.plot(angles, values, 'o-', linewidth=2, label=stage, color=colors[i])
                ax.fill(angles, values, alpha=0.25, color=colors[i])

        # 设置标签
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(metrics)
        ax.set_ylim(0, 100)
        ax.set_title('各阶段性能对比雷达图', size=16, fontweight='bold', pad=20)
        ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        ax.grid(True)

        plt.tight_layout()
        plt.savefig(f"{viz_dir}/stage_comparison.png", dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_reward_distribution(self, results, viz_dir):
        """绘制奖励分布图"""
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        fig.suptitle('奖励分布分析', fontsize=16, fontweight='bold')

        stages = ['stage1', 'stage2', 'stage3']
        colors = ['blue', 'green', 'red']

        # 奖励直方图
        ax1 = axes[0, 0]
        for i, stage in enumerate(stages):
            if stage in results:
                rewards = results[stage]['episode_rewards']
                ax1.hist(rewards, bins=30, alpha=0.6, label=stage, color=colors[i])

        ax1.set_title('奖励分布直方图')
        ax1.set_xlabel('奖励值')
        ax1.set_ylabel('频次')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 奖励箱线图
        ax2 = axes[0, 1]
        reward_data = []
        stage_labels = []
        for stage in stages:
            if stage in results:
                reward_data.append(results[stage]['episode_rewards'])
                stage_labels.append(stage)

        box_plot = ax2.boxplot(reward_data, labels=stage_labels, patch_artist=True)
        for patch, color in zip(box_plot['boxes'], colors[:len(reward_data)]):
            patch.set_facecolor(color)
            patch.set_alpha(0.6)

        ax2.set_title('奖励分布箱线图')
        ax2.set_ylabel('奖励值')
        ax2.grid(True, alpha=0.3)

        # 学习进度图
        ax3 = axes[1, 0]
        for i, stage in enumerate(stages):
            if stage in results:
                rewards = results[stage]['episode_rewards']
                # 计算学习进度（最后25%的平均奖励 vs 前25%的平均奖励）
                n = len(rewards)
                early_avg = np.mean(rewards[:n//4]) if n >= 4 else np.mean(rewards)
                late_avg = np.mean(rewards[-n//4:]) if n >= 4 else np.mean(rewards)
                improvement = late_avg - early_avg

                ax3.bar(stage, improvement, color=colors[i], alpha=0.8)
                ax3.text(i, improvement + (abs(improvement) * 0.1),
                        f'{improvement:.1f}', ha='center', va='bottom', fontweight='bold')

        ax3.set_title('学习改进程度')
        ax3.set_ylabel('奖励改进值')
        ax3.grid(True, alpha=0.3)

        # 收敛性分析
        ax4 = axes[1, 1]
        for i, stage in enumerate(stages):
            if stage in results:
                rewards = results[stage]['episode_rewards']
                # 计算奖励方差的滑动窗口
                window = min(100, len(rewards) // 5)
                if window > 1:
                    variances = []
                    for j in range(window, len(rewards)):
                        var = np.var(rewards[j-window:j])
                        variances.append(var)
                    ax4.plot(range(window, len(rewards)), variances,
                            color=colors[i], label=stage, linewidth=2)

        ax4.set_title('训练稳定性（奖励方差）')
        ax4.set_xlabel('Episode')
        ax4.set_ylabel('奖励方差')
        ax4.legend()
        ax4.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f"{viz_dir}/reward_distribution.png", dpi=300, bbox_inches='tight')
        plt.close()

def main():
    """主函数"""
    print("🚁 巡飞弹分阶段训练系统")
    print("集成: 六自由度环境 + 三项奖励函数 + 分阶段训练")
    print("=" * 60)
    
    # 创建训练器
    trainer = LoiteringMunitionTrainer(seed=42)
    
    # 执行训练
    results, agent = trainer.train_all_stages()
    
    print("\n🎉 训练完成! 可以使用训练好的模型进行测试。")

if __name__ == "__main__":
    main()
