{"experiment_info": {"reward_type": "simplified", "total_episodes": 300, "timestamp": "20250718_022622", "training_time_seconds": 3185.7017183303833, "visualization_interval": 10, "visualization_count": 31}, "performance_metrics": {"total_episodes": 300, "random_episodes": 200, "fixed_episodes": 100, "success_rate": 0.93, "collision_rate": 0.0, "timeout_rate": 0.0, "avg_episode_reward": -460.53775791156136, "std_episode_reward": 303.3913215280817, "reward_cv": 0.6587762160998382, "avg_episode_steps": 322.8466666666667, "std_episode_steps": 40.08736071243515, "total_training_time": 3185.7017183303833, "avg_episode_time": 10.619005727767945, "final_50_success_rate": 0.9, "learning_improvement": -0.462896639294259, "convergence_episode": 43, "best_episode_reward": -322.84395510073034, "worst_episode_reward": -2125.6829619352975, "visualization_count": 31, "total_global_steps": 96854, "max_complexity_score": 160, "phase1_success_rate": 0.93, "phase2_success_rate": 0.93, "phase1_avg_reward": -448.8776297076451, "phase2_avg_reward": -483.8580143193938, "phase1_improvement": -0.07964877484810089, "phase2_improvement": -0.4059259443790372, "most_complex_scenario": {"start": [10.0, 10.0, 10.0], "goal": [80.0, 80.0, 80.0], "obstacles": [{"center": [30.0, 30.0, 30.0], "radius": 6.329252042027914}, {"center": [50.0, 20.0, 40.0], "radius": 4.034405830621255}, {"center": [40.0, 60.0, 50.0], "radius": 4.511950539441817}, {"center": [60.0, 40.0, 30.0], "radius": 5.454809326607254}, {"center": [70.0, 70.0, 60.0], "radius": 4.669032366229378}], "complexity_score": 160, "bounds": [100, 100, 100]}, "phase_transitions": [{"phase": "random_to_fixed", "episode": 200, "global_step_count": 64473, "selected_scenario": {"episode": 63, "complexity_score": 160, "scenario_data": {"start": [10.0, 10.0, 10.0], "goal": [80.0, 80.0, 80.0], "obstacles": [{"center": [30.0, 30.0, 30.0], "radius": 6.329252042027914}, {"center": [50.0, 20.0, 40.0], "radius": 4.034405830621255}, {"center": [40.0, 60.0, 50.0], "radius": 4.511950539441817}, {"center": [60.0, 40.0, 30.0], "radius": 5.454809326607254}, {"center": [70.0, 70.0, 60.0], "radius": 4.669032366229378}], "complexity_score": 160, "bounds": [100, 100, 100]}, "episode_data": {"episode_reward": -2049.0909854073707, "episode_steps": 500, "episode_result": "timeout"}}, "phase1_success_rate": 0.93}]}, "episode_rewards": [-361.250752537995, -357.79663905315044, -422.9647013425672, -368.80911651858526, -326.6470409802364, -365.5899647768707, -423.98276195906897, -407.46497914330797, -448.7923840580488, -324.005880077977, -373.31714194666046, -427.91755727334373, -349.6560279851667, -503.4816050068978, -353.0996884903289, -345.94873853164455, -460.2325869933878, -349.2257498227202, -357.176600963606, -442.36483827922586, -556.3677422535789, -395.8387698625001, -361.9180876148669, -390.03277752051696, -411.28558100291013, -421.0089324365797, -471.2825533689082, -403.4244122052937, -408.284566278136, -402.3971161510364, -352.52410899816084, -434.52828225328915, -358.79283130731994, -392.88587914513704, -375.15493382864213, -530.161416392795, -507.47225178947497, -329.0812230653758, -350.37561041928126, -404.19507438210553, -335.75202735145257, -2060.3991566910763, -449.60784021165125, -409.62602721448275, -471.40761613070606, -388.6404205331577, -640.992652011924, -424.1524527342292, -430.2253155558177, -411.29759082450903, -429.67877639956896, -382.6284695340255, -375.842491152596, -368.6566966056763, -375.9424029487424, -451.13126899765984, -404.48585657445017, -356.85382743629526, -396.3091271357333, -454.0568554638602, -390.3706404959958, -341.4407234601807, -2024.8011050353896, -2049.0909854073707, -345.153928200449, -510.7376814099986, -543.9552224761968, -637.3106277295853, -405.28445951640356, -371.0144064482044, -373.7008057418639, -401.3080841689895, -527.8173560682833, -445.04956312773675, -388.0085426237162, -357.630553073626, -387.1590845068182, -368.6878075819896, -333.569411894203, -360.7214703923443, -614.0114175257866, -414.8303991580882, -369.62315637515144, -350.896785081136, -527.3346861320875, -462.3205412548929, -382.09989061723275, -360.670679597384, -344.3662908811454, -408.774064910613, -385.9142706750238, -411.626715473354, -374.11696143347973, -708.8171610150613, -369.77917341822064, -421.82136062957625, -355.4123356952631, -469.8344613373956, -369.47079539673086, -424.23390589052633, -386.80728448849453, -443.99443332913654, -592.3063266424947, -330.83746221940333, -376.2146572030506, -465.6059988675472, -348.6177353930454, -337.01207863754894, -380.9754434267122, -407.93619208526343, -387.39122621495585, -342.7122863107079, -1987.7850410912572, -364.45439513915795, -361.77709203757763, -427.995675481175, -434.3487462737752, -359.2155343674601, -463.17319091028446, -395.5977118521256, -2045.2622876711641, -413.8109041850511, -478.7395847483165, -363.36068430815203, -389.12196561279313, -354.4159734597882, -379.059602914001, -345.6747562608797, -392.4747080023689, -423.2917700151953, -347.0849375010995, -370.3839678116278, -385.3182617355072, -333.90151413437326, -571.3903789348572, -397.68875870895096, -358.47939654835096, -407.68462835560047, -350.78651626702367, -345.1623475375299, -338.72174492311336, -422.72864048358497, -349.6585979195007, -379.7500078242501, -381.661825291431, -350.0605500816005, -340.4133234342811, -380.91575272996465, -361.429042743596, -392.82051841746204, -351.0206510408842, -404.9062302944447, -390.47290034046347, -362.05559589315885, -525.9114945651605, -411.0268274385438, -621.2651083184753, -354.36633347179264, -363.77759905371187, -439.0178123795714, -361.9015823206502, -396.46387282942527, -363.13301191133763, -386.3835240316257, -366.1197659840701, -346.38055027641576, -355.4335773319876, -385.23712848363414, -444.50463029263005, -361.25545873085883, -376.70121729898545, -587.861817816712, -340.66078975973215, -404.9850978777386, -341.25319135709725, -876.1776090665658, -419.01473354190773, -412.90145556665584, -376.0464810945929, -381.5133298032835, -362.246489897471, -761.2678320057213, -380.4443318869959, -357.0035189908723, -468.94903955682287, -343.2729338980669, -329.2203476597875, -637.0864908231063, -458.61126212442616, -388.27921034558256, -406.254521899448, -393.38965966034124, -417.67714112684473, -372.4578784261778, -380.9308150395069, -374.66043477961153, -384.0601846895409, -441.8760711877443, -330.18636364053003, -400.6992858039051, -342.67932205696553, -436.96666012504716, -379.4426780154984, -378.430942813916, -352.6338884255757, -457.0361000383057, -333.8854191134037, -364.01455717160013, -446.9444654167696, -427.4739442307666, -372.5252888786704, -341.5880500056758, -440.25992996784396, -418.80251709738536, -325.7177952760357, -399.6035294501982, -336.65739800994794, -410.86507388699334, -422.13414214805994, -696.9065348098969, -388.5250550087028, -450.5323510076166, -366.3168333702045, -387.4922040290981, -367.7447210307918, -512.5543638342409, -414.3258642727186, -353.7008927309294, -424.55292692551734, -336.0765467602367, -413.4388724384967, -381.2135687102666, -434.9253323349327, -364.2674466229693, -398.5547552979701, -356.3271269584249, -338.99875406709975, -396.5293556362054, -383.2741111270115, -407.10654793763473, -356.13254646078036, -2005.5151374095624, -357.17096476850975, -2067.3866359040526, -393.6509904077341, -350.98117252189286, -409.0829999296696, -350.97949365021225, -496.6478951857647, -386.76499857071786, -400.7603508570169, -362.17931944772744, -419.76100270518054, -427.08202906843997, -413.5421923769278, -355.28589926221053, -401.7370875570254, -416.83087277684626, -364.0681727583106, -322.84395510073034, -397.56335939918495, -401.7327944633281, -481.8858277911978, -374.87684506150913, -417.2384482219395, -444.3638803875101, -377.99752586357164, -2008.0947996204593, -498.03620384960857, -344.82419487468815, -471.85458658112066, -657.4356903822938, -369.76113296935, -374.04951962926754, -389.91563117005666, -373.6501693259305, -457.4021340128611, -370.9004819, -399.41142898481945, -389.0729602771489, -350.89268009590893, -434.17464738699573, -394.0200871887544, -443.27085442344446, -363.25440996449925, -448.2674736727429, -360.5598589871451, -372.91058987605265, -568.4410845743373, -382.51953698349126, -449.22639541853164, -384.5648689603493, -325.5285203086813, -404.4580788905565, -2095.552804783937, -359.4218802611964, -378.4821367734999, -388.0205576631892, -337.05480526599547, -2125.6829619352975], "episode_steps": [305, 305, 327, 298, 298, 322, 314, 316, 331, 297, 309, 327, 298, 328, 299, 306, 379, 297, 302, 342, 355, 303, 311, 312, 316, 308, 319, 321, 312, 314, 323, 318, 295, 310, 312, 350, 317, 296, 295, 300, 299, 500, 326, 311, 330, 298, 388, 323, 325, 310, 311, 336, 327, 308, 317, 322, 316, 298, 323, 325, 313, 299, 500, 500, 294, 337, 365, 392, 315, 322, 307, 306, 340, 319, 306, 299, 309, 304, 296, 307, 384, 315, 300, 293, 330, 320, 314, 306, 295, 311, 303, 311, 313, 403, 326, 322, 300, 317, 305, 339, 329, 356, 370, 307, 314, 321, 295, 306, 315, 307, 310, 298, 500, 320, 301, 312, 307, 310, 340, 316, 500, 315, 344, 299, 296, 313, 345, 295, 338, 340, 297, 304, 307, 293, 368, 321, 314, 318, 302, 297, 301, 341, 309, 304, 309, 295, 316, 307, 302, 333, 310, 320, 315, 302, 350, 314, 387, 298, 311, 319, 305, 303, 299, 302, 294, 288, 313, 310, 341, 310, 314, 394, 299, 311, 306, 432, 319, 309, 306, 303, 298, 471, 306, 302, 318, 299, 293, 394, 323, 306, 333, 311, 319, 301, 307, 302, 302, 329, 293, 308, 304, 341, 311, 304, 304, 318, 307, 297, 318, 318, 318, 311, 333, 299, 295, 305, 310, 307, 308, 408, 322, 325, 299, 310, 302, 356, 318, 294, 324, 307, 315, 305, 341, 305, 309, 303, 307, 307, 307, 306, 296, 500, 296, 500, 306, 295, 314, 307, 328, 320, 313, 295, 328, 311, 330, 301, 314, 319, 306, 305, 309, 319, 319, 304, 318, 327, 310, 500, 350, 309, 317, 372, 294, 316, 321, 334, 323, 308, 315, 302, 299, 337, 305, 323, 301, 318, 309, 299, 382, 307, 325, 313, 303, 308, 500, 304, 310, 303, 306, 500], "success_episodes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 64, 65, 66, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 99, 100, 101, 103, 104, 105, 106, 107, 108, 109, 110, 111, 113, 114, 115, 116, 117, 118, 119, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 172, 173, 174, 175, 176, 177, 178, 179, 180, 182, 183, 184, 185, 186, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 242, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 268, 269, 270, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 289, 290, 291, 292, 293, 295, 296, 297, 298], "collision_episodes": [], "timeout_episodes": [], "visualization_episodes": [0, 9, 19, 29, 39, 49, 59, 69, 79, 89, 99, 109, 119, 129, 139, 149, 159, 169, 179, 189, 199, 209, 219, 229, 239, 249, 259, 269, 279, 289, 299]}