"""
修复版GIF生成器
使用原训练/测试视角，完全解决字体显示问题
"""

import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.animation as animation
import os
import glob
from datetime import datetime

# 设置matplotlib使用英文字体，避免中文字体问题
plt.rcParams['font.family'] = ['DejaVu Sans', 'Arial', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

from dwa_rl_core import StabilizedEnvironment, load_trained_model, td3_config

def generate_fixed_gif(model_path, max_steps=300, fps=12, enable_dynamic_obstacles=True):
    """生成修复版导航GIF"""
    print("🎬 Fixed GIF Generator")
    print("=" * 50)
    print(f"Model: {os.path.basename(model_path)}")
    print(f"Steps: {max_steps} | FPS: {fps}")
    print(f"Dynamic Obstacles: {'ON' if enable_dynamic_obstacles else 'OFF'}")
    print(f"Using Original Training/Testing Camera Angle")
    print("=" * 50)

    # 加载模型和环境
    controller = load_trained_model(model_path, td3_config)
    env = StabilizedEnvironment(enable_dynamic_obstacles=enable_dynamic_obstacles)
    
    # 收集导航数据
    nav_data = collect_navigation_data(env, controller, max_steps)
    
    # 创建GIF
    gif_path = create_fixed_gif(env, nav_data, fps, enable_dynamic_obstacles)
    
    return gif_path

def collect_navigation_data(env, controller, max_steps):
    """收集导航数据"""
    state = env.reset()
    full_state = np.concatenate([env.state, state[6:]])
    
    data = {
        'positions': [],
        'velocities': [],
        'accelerations': [],
        'goal_distances': [],
        'rewards': [],
        'dynamic_obs_positions': []
    }
    
    prev_velocity = np.zeros(3)
    dt = 0.1
    
    print("📊 Collecting navigation data...")
    print(f"🎯 Target: {env.goal}")
    print(f"🚁 Start: {env.state[:3]}")
    initial_goal_dist = np.linalg.norm(env.state[:3] - env.goal)
    print(f"📏 Initial distance to goal: {initial_goal_dist:.1f}m")

    for step in range(max_steps):
        current_pos = env.state[:3].copy()
        current_vel = env.state[3:6].copy()
        acceleration = (current_vel - prev_velocity) / dt
        goal_dist = np.linalg.norm(current_pos - env.goal)

        # 记录数据
        data['positions'].append(current_pos)
        data['velocities'].append(current_vel)
        data['accelerations'].append(acceleration)
        data['goal_distances'].append(goal_dist)

        # 记录动态障碍物位置
        if hasattr(env, 'dynamic_obstacles'):
            dyn_pos = [obs['center'].copy() for obs in env.dynamic_obstacles]
            data['dynamic_obs_positions'].append(dyn_pos)
        else:
            data['dynamic_obs_positions'].append([])

        # 获取动作并执行
        all_obstacles = env.obstacles.copy()
        if hasattr(env, 'dynamic_obstacles'):
            all_obstacles.extend(env.dynamic_obstacles)

        action, _, _ = controller.get_action_with_quality(
            full_state, env.goal, all_obstacles, add_noise=True
        )

        next_state, reward, done, env_info = env.step(action)
        next_full_state = np.concatenate([env.state, next_state[6:]])

        data['rewards'].append(reward)

        # 更详细的进度报告
        if step % 10 == 0 or goal_dist < 5.0:
            vel_mag = np.linalg.norm(current_vel)
            acc_mag = np.linalg.norm(acceleration)
            progress = (initial_goal_dist - goal_dist) / initial_goal_dist * 100
            print(f"  Step {step:3d}: Goal={goal_dist:5.1f}m | Progress={progress:5.1f}% | Vel={vel_mag:.1f}m/s | Acc={acc_mag:.1f}m/s²")

        # 检查是否到达目标
        if done:
            if goal_dist < 2.0:  # 成功到达
                print(f"🎉 SUCCESS! Reached goal at step {step} (distance: {goal_dist:.1f}m)")
            else:
                print(f"⚠️  Episode ended at step {step} (reason: {env_info.get('reason', 'unknown')})")
            break

        # 检查是否接近目标
        if goal_dist < 1.0:
            print(f"🎯 Very close to goal! Distance: {goal_dist:.2f}m")

        full_state = next_full_state
        prev_velocity = current_vel
    
    print(f"📈 Collected {len(data['positions'])} navigation steps")
    return data

def create_fixed_gif(env, data, fps, enable_dynamic):
    """创建修复版GIF动画"""
    print("🎨 Creating fixed GIF animation...")
    
    # 设置图形 - 使用与训练/测试一致的尺寸
    fig = plt.figure(figsize=(12, 10))
    ax = fig.add_subplot(111, projection='3d')
    
    # 绘制静态环境
    setup_original_environment(ax, env)
    
    # 初始化动画元素
    trajectory_line, = ax.plot([], [], [], 'b-', linewidth=3, alpha=0.9, label='UAV Path')
    uav_marker = ax.scatter([], [], [], c='blue', s=150, marker='D', 
                           edgecolors='navy', linewidth=2, label='UAV')
    
    # 信息显示 - 使用简洁的英文格式
    info_text = ax.text2D(0.02, 0.98, '', transform=ax.transAxes, fontsize=10,
                         verticalalignment='top', fontweight='bold',
                         bbox=dict(boxstyle='round,pad=0.4', facecolor='lightcyan', alpha=0.9),
                         family='monospace')  # 使用等宽字体确保对齐
    
    # 动态障碍物容器
    dynamic_surfaces = []
    
    def animate_frame(frame):
        """动画帧更新函数"""
        if frame >= len(data['positions']):
            return trajectory_line, uav_marker, info_text
        
        # 更新轨迹
        positions = np.array(data['positions'][:frame+1])
        if len(positions) > 1:
            trajectory_line.set_data_3d(positions[:, 0], positions[:, 1], positions[:, 2])
        
        # 更新无人机位置
        current_pos = data['positions'][frame]
        uav_marker._offsets3d = ([current_pos[0]], [current_pos[1]], [current_pos[2]])
        
        # 更新动态障碍物
        nonlocal dynamic_surfaces
        for surface in dynamic_surfaces:
            surface.remove()
        dynamic_surfaces.clear()
        
        if enable_dynamic and frame < len(data['dynamic_obs_positions']):
            dyn_positions = data['dynamic_obs_positions'][frame]
            for i, pos in enumerate(dyn_positions):
                if hasattr(env, 'dynamic_obstacles') and i < len(env.dynamic_obstacles):
                    radius = env.dynamic_obstacles[i]['radius']
                    # 使用较少的点数提高性能
                    u = np.linspace(0, 2 * np.pi, 8)
                    v = np.linspace(0, np.pi, 8)
                    x = radius * np.outer(np.cos(u), np.sin(v)) + pos[0]
                    y = radius * np.outer(np.sin(u), np.sin(v)) + pos[1]
                    z = radius * np.outer(np.ones(np.size(u)), np.cos(v)) + pos[2]
                    surface = ax.plot_surface(x, y, z, alpha=0.7, color='orange', 
                                            edgecolor='red', linewidth=0.5)
                    dynamic_surfaces.append(surface)
        
        # 更新信息显示 - 使用简洁的英文格式
        velocity = data['velocities'][frame]
        acceleration = data['accelerations'][frame]
        goal_dist = data['goal_distances'][frame]
        reward = data['rewards'][frame] if frame < len(data['rewards']) else 0
        
        vel_mag = np.linalg.norm(velocity)
        acc_mag = np.linalg.norm(acceleration)
        
        # 简洁的信息显示格式
        info_content = f"""Step: {frame + 1:3d}/{len(data['positions'])}
Pos: [{current_pos[0]:5.1f}, {current_pos[1]:5.1f}, {current_pos[2]:5.1f}]

Velocity: {vel_mag:5.2f} m/s
  Vx: {velocity[0]:+6.2f}  Vy: {velocity[1]:+6.2f}  Vz: {velocity[2]:+6.2f}

Acceleration: {acc_mag:5.2f} m/s²
  Ax: {acceleration[0]:+6.2f}  Ay: {acceleration[1]:+6.2f}  Az: {acceleration[2]:+6.2f}

Goal Distance: {goal_dist:6.1f} m
Reward: {reward:8.1f}"""
        
        info_text.set_text(info_content)
        
        # 更新标题
        ax.set_title(f'DWA-RL Navigation | Step {frame + 1} | Vel: {vel_mag:.1f}m/s | Acc: {acc_mag:.1f}m/s²', 
                    fontsize=12, fontweight='bold')
        
        return trajectory_line, uav_marker, info_text
    
    # 创建动画
    print(f"🎨 Creating animation with {len(data['positions'])} frames...")
    anim = animation.FuncAnimation(fig, animate_frame, frames=len(data['positions']), 
                                 interval=1000//fps, blit=False, repeat=True)
    
    # 保存GIF
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    gif_filename = f"fixed_navigation_{timestamp}.gif"
    
    print(f"💾 Saving GIF: {gif_filename}")
    
    # 使用优化的设置保存
    writer = animation.PillowWriter(fps=fps)
    anim.save(gif_filename, writer=writer, dpi=100)
    
    plt.close()
    
    # 打印统计信息
    print_navigation_stats(data, gif_filename)
    
    return gif_filename

def setup_original_environment(ax, env):
    """设置原始训练/测试环境视角"""
    # 绘制起点和终点 - 使用与训练/测试一致的样式
    start, goal = env.start, env.goal
    ax.scatter(start[0], start[1], start[2], c='green', s=200, marker='o', 
              label='Start', alpha=0.8, edgecolors='darkgreen', linewidth=2)
    ax.scatter(goal[0], goal[1], goal[2], c='red', s=200, marker='*', 
              label='Goal', alpha=0.8, edgecolors='darkred', linewidth=2)
    
    # 绘制静态障碍物 - 使用与训练/测试一致的样式
    for obs in env.obstacles:
        center = obs['center']
        radius = obs['radius']
        u = np.linspace(0, 2 * np.pi, 15)
        v = np.linspace(0, np.pi, 15)
        x = radius * np.outer(np.cos(u), np.sin(v)) + center[0]
        y = radius * np.outer(np.sin(u), np.sin(v)) + center[1]
        z = radius * np.outer(np.ones(np.size(u)), np.cos(v)) + center[2]
        ax.plot_surface(x, y, z, alpha=0.4, color='gray', 
                       edgecolor='black', linewidth=0.5)
    
    # 设置坐标轴 - 与训练/测试一致
    ax.set_xlabel('X (m)', fontsize=12)
    ax.set_ylabel('Y (m)', fontsize=12)
    ax.set_zlabel('Z (m)', fontsize=12)
    ax.set_xlim(0, 100)
    ax.set_ylim(0, 100)
    ax.set_zlim(0, 100)
    ax.grid(True, alpha=0.3)
    ax.legend(loc='upper right', fontsize=10)
    
    # 使用原始训练/测试的默认视角 (matplotlib默认)
    # 不设置view_init，使用matplotlib默认视角

def print_navigation_stats(data, gif_filename):
    """打印导航统计信息"""
    positions = np.array(data['positions'])
    velocities = np.array(data['velocities'])
    accelerations = np.array(data['accelerations'])
    
    if len(positions) > 1:
        path_length = np.sum(np.linalg.norm(np.diff(positions, axis=0), axis=1))
        avg_vel = np.mean([np.linalg.norm(v) for v in velocities])
        max_vel = np.max([np.linalg.norm(v) for v in velocities])
        avg_acc = np.mean([np.linalg.norm(a) for a in accelerations])
        max_acc = np.max([np.linalg.norm(a) for a in accelerations])
        
        initial_dist = data['goal_distances'][0]
        final_dist = data['goal_distances'][-1]
        improvement = initial_dist - final_dist
        
        print(f"\n📊 Navigation Statistics:")
        print("=" * 40)
        print(f"Steps: {len(positions)}")
        print(f"Path Length: {path_length:.1f}m")
        print(f"Velocity - Avg: {avg_vel:.2f}m/s | Max: {max_vel:.2f}m/s")
        print(f"Acceleration - Avg: {avg_acc:.2f}m/s² | Max: {max_acc:.2f}m/s²")
        print(f"Goal Distance - Initial: {initial_dist:.1f}m | Final: {final_dist:.1f}m")
        print(f"Distance Improvement: {improvement:.1f}m")
        print(f"GIF File: {gif_filename}")
        print("=" * 40)

def find_latest_model():
    """查找最新的模型文件"""
    model_files = glob.glob('training_outputs/dwa_rl_model_*.pth')
    if not model_files:
        model_files = glob.glob('training_outputs/stabilized_td3_model_*.pth')
    
    if model_files:
        return max(model_files, key=os.path.getctime)
    else:
        return None

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Fixed GIF Generator - Original Camera Angle')
    parser.add_argument('--model', type=str, help='Model file path')
    parser.add_argument('--steps', type=int, default=300, help='Max steps (default 300)')
    parser.add_argument('--fps', type=int, default=12, help='Frame rate (default 12)')
    parser.add_argument('--no-dynamic', action='store_true', help='Disable dynamic obstacles')
    
    args = parser.parse_args()
    
    # 确定模型路径
    if args.model:
        model_path = args.model
    else:
        model_path = find_latest_model()
        if model_path:
            print(f"🔍 Using latest model: {os.path.basename(model_path)}")
        else:
            print("❌ No trained model found")
            exit(1)
    
    if not os.path.exists(model_path):
        print(f"❌ Model file not found: {model_path}")
        exit(1)
    
    # 生成GIF
    gif_path = generate_fixed_gif(
        model_path=model_path,
        max_steps=args.steps,
        fps=args.fps,
        enable_dynamic_obstacles=not args.no_dynamic
    )
    
    print(f"\n🎉 Fixed GIF generation completed!")
    print(f"📁 File: {gif_path}")
    print(f"🎬 Using original training/testing camera angle!")
    print(f"✅ Font display issues resolved!")
