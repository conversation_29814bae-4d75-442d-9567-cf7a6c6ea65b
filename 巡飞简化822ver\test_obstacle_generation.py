"""
测试新的障碍物生成逻辑
验证障碍物是否能有效阻挡起点到终点的直线路径
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

from loitering_munition_environment import LoiteringMunitionEnvironment
from environment_config import get_environment_config

def test_obstacle_generation():
    """测试障碍物生成"""
    print("🧪 测试新的障碍物生成逻辑")
    print("=" * 50)
    
    # 测试三个阶段的环境配置
    stages = ["stage1_simple", "stage2_complex", "stage3_dynamic"]
    
    for stage_name in stages:
        print(f"\n📍 测试 {stage_name}")
        print("-" * 30)
        
        # 获取环境配置
        env_config = get_environment_config(stage_name)
        
        # 创建环境
        env = LoiteringMunitionEnvironment(
            bounds=[2000, 2000, 2000],
            environment_config=env_config,
            reward_type='simplified'
        )
        
        # 生成多个场景进行测试
        blocking_ratios = []
        for i in range(5):
            print(f"  场景 {i+1}:")
            env.reset()
            
            # 计算阻挡率
            blocking_ratio = env._validate_path_blocking()
            blocking_ratios.append(blocking_ratio)
            
            print(f"    障碍物数量: 静态 {len(env.obstacles)}, 动态 {len(env.dynamic_obstacles)}")
            print(f"    路径阻挡率: {blocking_ratio:.2%}")
        
        avg_blocking = np.mean(blocking_ratios)
        print(f"  平均阻挡率: {avg_blocking:.2%}")
        
        if avg_blocking >= 0.3:
            print(f"  ✅ {stage_name} 障碍物生成有效")
        else:
            print(f"  ⚠️  {stage_name} 障碍物生成可能需要调整")

def visualize_obstacle_layout(stage_name="stage1_simple"):
    """可视化障碍物布局"""
    print(f"\n🎨 可视化 {stage_name} 障碍物布局")
    
    # 获取环境配置
    env_config = get_environment_config(stage_name)
    
    # 创建环境
    env = LoiteringMunitionEnvironment(
        bounds=[2000, 2000, 2000],
        environment_config=env_config,
        reward_type='simplified'
    )
    
    # 生成场景
    env.reset()
    
    # 创建3D图
    fig = plt.figure(figsize=(12, 10))
    ax = fig.add_subplot(111, projection='3d')
    
    # 绘制起点和终点
    start = env.start
    goal = env.goal
    ax.scatter(start[0], start[1], start[2], color='green', s=200, marker='o',
              label='Start', edgecolors='black', linewidth=2)
    ax.scatter(goal[0], goal[1], goal[2], color='red', s=200, marker='*',
              label='Target', edgecolors='black', linewidth=2)
    
    # 绘制直线路径
    ax.plot([start[0], goal[0]], [start[1], goal[1]], [start[2], goal[2]],
           'r--', linewidth=3, alpha=0.7, label='Direct Path')
    
    # 绘制静态障碍物
    for i, obs in enumerate(env.obstacles):
        u = np.linspace(0, 2 * np.pi, 20)
        v = np.linspace(0, np.pi, 20)
        
        x = obs['center'][0] + obs['radius'] * np.outer(np.cos(u), np.sin(v))
        y = obs['center'][1] + obs['radius'] * np.outer(np.sin(u), np.sin(v))
        z = obs['center'][2] + obs['radius'] * np.outer(np.ones(np.size(u)), np.cos(v))
        
        ax.plot_surface(x, y, z, alpha=0.3, color='orange')
        
        # 添加标签
        center = obs['center']
        ax.text(center[0], center[1], center[2] + obs['radius'] + 50,
               f'S{i+1}', fontsize=10, ha='center')
    
    # 绘制动态障碍物
    for i, obs in enumerate(env.dynamic_obstacles):
        u = np.linspace(0, 2 * np.pi, 20)
        v = np.linspace(0, np.pi, 20)
        
        x = obs['center'][0] + obs['radius'] * np.outer(np.cos(u), np.sin(v))
        y = obs['center'][1] + obs['radius'] * np.outer(np.sin(u), np.sin(v))
        z = obs['center'][2] + obs['radius'] * np.outer(np.ones(np.size(u)), np.cos(v))
        
        ax.plot_surface(x, y, z, alpha=0.4, color='red')
        
        # 添加标签
        center = obs['center']
        ax.text(center[0], center[1], center[2] + obs['radius'] + 50,
               f'D{i+1}', fontsize=10, ha='center')
    
    # 设置坐标轴
    ax.set_xlim(0, 2000)
    ax.set_ylim(0, 2000)
    ax.set_zlim(0, 2000)
    ax.set_xlabel('X (m)')
    ax.set_ylabel('Y (m)')
    ax.set_zlabel('Z (m)')
    
    # 计算阻挡率
    blocking_ratio = env._validate_path_blocking()
    
    ax.set_title(f'{stage_name} Obstacle Layout\n'
                f'Static: {len(env.obstacles)}, Dynamic: {len(env.dynamic_obstacles)}\n'
                f'Path Blocking Ratio: {blocking_ratio:.2%}')
    
    ax.legend()
    plt.tight_layout()
    
    # 保存图片
    filename = f'{stage_name}_obstacle_layout.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"  📊 布局图已保存: {filename}")
    plt.show()

def main():
    """主函数"""
    try:
        # 测试障碍物生成
        test_obstacle_generation()
        
        # 可视化每个阶段的布局
        for stage in ["stage1_simple", "stage2_complex", "stage3_dynamic"]:
            visualize_obstacle_layout(stage)
        
        print("\n🎉 测试完成!")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
