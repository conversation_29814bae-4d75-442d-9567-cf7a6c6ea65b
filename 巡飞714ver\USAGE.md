# 巡飞弹系统使用指南

## 🚀 快速开始

### 1. 环境准备
```bash
# 安装依赖库
pip install numpy torch matplotlib

# 或使用requirements.txt
pip install -r requirements.txt
```

### 2. 系统测试
```bash
# 快速功能测试
python test_system.py --mode quick

# 预期输出：
# ⚡ 快速系统测试
# 1. 测试环境创建... ✅
# 2. 测试网络创建... ✅  
# 3. 测试环境步进... ✅
# 🎉 快速测试通过！
```

### 3. 开始训练
```bash
# 方式1：使用一键运行脚本
python run.py --mode train

# 方式2：直接运行训练脚本
python train_loitering_munition.py
```

### 4. 测试结果
```bash
# 测试最新训练的模型
python test_system.py --mode all --model-dir loitering_munition_training_XXXXXX

# 测试单个阶段
python test_system.py --mode single --model loitering_munition_training_XXXXXX/stage3_model.pth --stage stage3

### 5. 生成可视化分析
```bash
# 生成综合分析报告（包含所有图表）
python visualize_results.py --results-dir loitering_munition_training_XXXXXX --mode comprehensive

# 生成特定阶段的3D轨迹
python visualize_results.py --results-dir loitering_munition_training_XXXXXX --mode trajectory --stage stage3

# 生成场景布局图
python visualize_results.py --results-dir loitering_munition_training_XXXXXX --mode layout --stage stage3
```

## 📁 文件说明

### 核心文件
- `loitering_munition_env.py` - 巡飞弹六自由度环境
- `loitering_munition_dwa.py` - DWA安全控制器
- `scenario_config.py` - 分阶段场景配置
- `td3_network.py` - TD3网络架构
- `train_loitering_munition.py` - 主训练脚本
- `test_system.py` - 系统测试脚本

### 辅助文件
- `run.py` - 一键运行脚本
- `visualize_results.py` - 训练结果可视化脚本
- `copy_paper.py` - 论文文件复制工具
- `requirements.txt` - 依赖库列表
- `README.md` - 系统概述
- `USAGE.md` - 本使用指南

## 🎯 训练配置

### 分阶段设置
```python
training_stages = {
    'stage1': {'episodes': 500, 'description': '基础作战场景'},
    'stage2': {'episodes': 1000, 'description': '复杂静态场景'},
    'stage3': {'episodes': 500, 'description': '动态威胁场景'}
}
```

### 网络参数
```python
TD3Agent(
    state_dim=15,      # 15维状态空间
    action_dim=3,      # 3维控制输入
    max_action=1.0,    # 动作范围[-1,1]
    lr=3e-4           # 学习率
)
```

### 环境参数
```python
LoiteringMunitionEnvironment(
    bounds=[2000, 2000, 200],  # 2km×2km×200m作战空间
    V_min=15.0,                # 最小速度15m/s
    V_max=60.0,                # 最大速度60m/s
    a_T_max=8.0,               # 最大切向加速度8m/s²
    a_N_max=39.24              # 最大法向加速度39.24m/s²(4g)
)
```

## 📊 预期结果

### 训练时间
- 总训练时间：2-4小时（取决于硬件配置）
- 阶段1：约30-60分钟
- 阶段2：约60-120分钟
- 阶段3：约30-60分钟

### 性能指标
| 阶段 | 成功率 | 平均奖励 | 平均步数 |
|------|--------|----------|----------|
| Stage1 | >80% | >-50 | <800 |
| Stage2 | >70% | >-80 | <1200 |
| Stage3 | >60% | >-120 | <1500 |

### 输出文件
```
loitering_munition_training_YYYYMMDD_HHMMSS/
├── stage1_model.pth                    # 阶段1训练模型
├── stage2_model.pth                    # 阶段2训练模型
├── stage3_model.pth                    # 阶段3训练模型
├── training_results.json               # 详细训练数据
├── training_report.txt                 # 训练总结报告
├── visualizations/                     # 自动生成的可视化图表
│   ├── training_curves.png             # 训练曲线图
│   ├── success_rates.png               # 成功率分析图
│   ├── stage_comparison.png            # 阶段对比雷达图
│   └── reward_distribution.png         # 奖励分布分析图
└── comprehensive_analysis/             # 详细分析图表（手动生成）
    ├── scenario_layout_stage1.png      # 阶段1场景布局图
    ├── scenario_layout_stage2.png      # 阶段2场景布局图
    ├── scenario_layout_stage3.png      # 阶段3场景布局图
    ├── 3d_trajectory_stage1.png        # 阶段1 3D飞行轨迹
    ├── 3d_trajectory_stage2.png        # 阶段2 3D飞行轨迹
    └── 3d_trajectory_stage3.png        # 阶段3 3D飞行轨迹
```

## 🔧 高级用法

### 自定义训练参数
```python
# 修改 train_loitering_munition.py 中的参数
trainer = LoiteringMunitionTrainer(seed=42)

# 修改训练轮数
trainer.training_stages['stage1']['episodes'] = 1000

# 修改网络参数
agent = TD3Agent(state_dim=15, action_dim=3, lr=1e-4)
```

### 自定义场景配置
```python
# 修改 scenario_config.py 中的障碍物配置
def _generate_stage1_obstacles(self):
    obstacles = [
        {'center': np.array([x, y, z]), 'radius': r, 'type': 'custom'}
        # 添加更多障碍物...
    ]
    return obstacles
```

### 自定义奖励函数
```python
# 修改 loitering_munition_env.py 中的奖励计算
def _calculate_simplified_reward(self):
    # 距离奖励
    distance_reward = -goal_dist / 50.0
    
    # 效率奖励  
    efficiency_reward = -0.1
    
    # 安全奖励
    danger_penalty = -(3.0 - min_obs_dist) * 2.0 if min_obs_dist < 3.0 else 0
    
    # 自定义奖励组件
    custom_reward = your_custom_function()
    
    return distance_reward + efficiency_reward + danger_penalty + custom_reward
```

## 🐛 故障排除

### 常见问题

1. **导入错误**
```bash
ModuleNotFoundError: No module named 'torch'
```
解决：`pip install torch`

2. **内存不足**
```bash
RuntimeError: CUDA out of memory
```
解决：减少batch_size或使用CPU训练

3. **训练不收敛**
- 检查奖励函数设计
- 调整学习率
- 增加训练轮数

4. **测试失败**
- 确保模型文件存在
- 检查文件路径
- 验证模型兼容性

### 调试模式
```python
# 在代码中添加调试信息
import logging
logging.basicConfig(level=logging.DEBUG)

# 打印详细状态信息
print(f"State: {env.state}")
print(f"Action: {action}")
print(f"Reward: {reward}")
```

## 📞 技术支持

### 系统要求
- Python 3.7+
- NumPy 1.21+
- PyTorch 1.9+
- 内存：8GB+ 推荐
- 存储：2GB+ 可用空间

### 性能优化
- 使用GPU加速训练
- 调整batch_size适应内存
- 使用多进程数据加载
- 定期保存检查点

### 扩展开发
- 添加新的障碍物类型
- 实现新的运动模式
- 集成其他RL算法
- 添加可视化功能

这个系统完全实现了论文中的技术方案，可以直接用于学术研究和工程验证。
