\documentclass[11pt]{article}
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
\usepackage{CJKutf8}
\usepackage{amsmath,amssymb}
\usepackage{graphicx}
\usepackage{booktabs}
\usepackage{array}
\usepackage{geometry}
\geometry{a4paper, margin=2cm}

\title{DWA-RL Framework Technical Specifications}
\author{Technical Specifications Document}
\date{\today}

\begin{document}
\begin{CJK}{UTF8}{gbsn}

\maketitle

\section{System Architecture Technical Parameters}

\subsection{Kinematic Model Parameters}

\begin{table}[h]
\centering
\caption{UAV Kinematic Constraint Parameters}
\begin{tabular}{|l|c|c|l|}
\hline
\textbf{Parameter} & \textbf{Value} & \textbf{Unit} & \textbf{Description} \\
\hline
Max velocity components & [3, 3, 3] & m/s & x, y, z direction velocity limits \\
Max total velocity & 5.196 & m/s & $\sqrt{3^2+3^2+3^2}$ \\
Max acceleration components & [5, 5, 5] & m/s² & x, y, z direction acceleration limits \\
Max total acceleration & 8.660 & m/s² & $\sqrt{5^2+5^2+5^2}$ \\
Min safety distance & 1.5 & m & Obstacle safety margin \\
Control time step & 0.1 & s & Discretization time interval \\
\hline
\end{tabular}
\end{table}

\subsection{状态空间定义}

状态向量维度：14维
\begin{align}
\mathbf{s} = [&\underbrace{x, y, z}_{\text{位置}}, \underbrace{v_x, v_y, v_z}_{\text{速度}}, \underbrace{g_x, g_y, g_z}_{\text{目标方向}}, \\
&\underbrace{d_{goal}}_{\text{目标距离}}, \underbrace{d_{obs}}_{\text{障碍物距离}}, \underbrace{n_{static}}_{\text{静态障碍物数}}, \underbrace{n_{dynamic}}_{\text{动态障碍物数}}, \underbrace{flag_{dynamic}}_{\text{动态标志}}]^T
\end{align}

状态归一化方案：
\begin{itemize}
\item 位置：除以100.0（环境尺寸）
\item 速度：除以5.0（最大速度）
\item 目标方向：单位向量归一化
\item 距离：目标距离除以100.0，障碍物距离除以20.0
\item 数量：静态障碍物数除以20.0，动态障碍物数除以10.0
\end{itemize}

\subsection{动作空间设计}

动作表示：3维连续速度向量
\begin{equation}
\mathbf{a} = [v_x, v_y, v_z]^T \in \mathbb{R}^3
\end{equation}

动作约束：
\begin{align}
|v_i| &\leq v_{i,max}, \quad i \in \{x, y, z\} \\
|\mathbf{v}| &\leq v_{max}
\end{align}

\section{DWA算法技术细节}

\subsection{动态窗口计算}

动态窗口定义：
\begin{equation}
DW = \{(v_x, v_y, v_z) | v_i \in [v_{s,i}, v_{d,i}]\}
\end{equation}

其中：
\begin{align}
v_{s,i} &= \max(0, v_{current,i} - a_{max,i} \cdot \Delta t) \\
v_{d,i} &= \min(v_{max,i}, v_{current,i} + a_{max,i} \cdot \Delta t)
\end{align}

\subsection{轨迹预测模型}

预测时间窗口：2.0秒
预测步数：20步（每步0.1秒）

轨迹计算：
\begin{equation}
\mathbf{p}(t) = \mathbf{p}_0 + \mathbf{v} \cdot t, \quad t \in [0, T_{predict}]
\end{equation}

\subsection{安全性评估函数}

碰撞检测：
\begin{equation}
Safe(\mathbf{v}) = \min_{i,t} \{|\mathbf{p}(t) - \mathbf{obs}_i| - r_i\} \geq d_{safe}
\end{equation}

评估指标：
\begin{align}
Score_{heading} &= \frac{\mathbf{v} \cdot (\mathbf{g} - \mathbf{p})}{|\mathbf{v}||\mathbf{g} - \mathbf{p}|} \\
Score_{velocity} &= \frac{|\mathbf{v}|}{v_{max}} \\
Score_{distance} &= \frac{1}{|\mathbf{p}_{final} - \mathbf{g}| + \epsilon}
\end{align}

综合评分：
\begin{equation}
Score_{total} = w_1 \cdot Score_{heading} + w_2 \cdot Score_{velocity} + w_3 \cdot Score_{distance}
\end{equation}

权重设置：$w_1 = 0.5, w_2 = 0.3, w_3 = 0.2$

\section{TD3网络架构}

\subsection{Actor网络结构}

\begin{table}[h]
\centering
\caption{Actor网络层次结构}
\begin{tabular}{|l|c|c|l|}
\hline
\textbf{层名称} & \textbf{输入维度} & \textbf{输出维度} & \textbf{激活函数} \\
\hline
状态编码器1 & 14 & 256 & ReLU \\
LayerNorm1 & 256 & 256 & - \\
状态编码器2 & 256 & 256 & ReLU \\
LayerNorm2 & 256 & 256 & - \\
Dropout & 256 & 256 & - \\
动作编码器1 & 3 & 128 & ReLU \\
LayerNorm3 & 128 & 128 & - \\
动作编码器2 & 128 & 128 & ReLU \\
LayerNorm4 & 128 & 128 & - \\
多头注意力 & 384 & 384 & - \\
输出层 & 384 & 20 & Softmax \\
\hline
\end{tabular}
\end{table}

注意力机制参数：
\begin{itemize}
\item 嵌入维度：384（256+128）
\item 注意力头数：8
\item Dropout率：0.1
\end{itemize}

\subsection{Critic网络结构}

双重Q网络，每个网络结构：

\begin{table}[h]
\centering
\caption{Critic网络层次结构}
\begin{tabular}{|l|c|c|l|}
\hline
\textbf{层名称} & \textbf{输入维度} & \textbf{输出维度} & \textbf{激活函数} \\
\hline
输入层 & 17 & 256 & ReLU \\
LayerNorm1 & 256 & 256 & - \\
隐藏层1 & 256 & 256 & ReLU \\
LayerNorm2 & 256 & 256 & - \\
隐藏层2 & 256 & 128 & ReLU \\
LayerNorm3 & 128 & 128 & - \\
输出层 & 128 & 1 & Linear \\
\hline
\end{tabular}
\end{table}

输入组成：状态(14维) + 动作(3维) = 17维

\section{训练超参数}

\subsection{优化器参数}

\begin{table}[h]
\centering
\caption{训练超参数设置}
\begin{tabular}{|l|c|l|}
\hline
\textbf{参数} & \textbf{数值} & \textbf{说明} \\
\hline
Actor学习率 & 0.0003 & Adam优化器 \\
Critic学习率 & 0.001 & Adam优化器 \\
批大小 & 256 & 小批量梯度下降 \\
经验回放容量 & 100,000 & 循环缓冲区 \\
折扣因子 & 0.99 & 未来奖励折扣 \\
软更新系数 & 0.005 & 目标网络更新率 \\
策略噪声 & 0.2 & 目标策略平滑 \\
噪声裁剪 & 0.5 & 噪声限制范围 \\
策略更新频率 & 2 & 延迟更新间隔 \\
\hline
\end{tabular}
\end{table}

\subsection{奖励函数权重}

\begin{table}[h]
\centering
\caption{奖励函数组件权重}
\begin{tabular}{|l|c|c|l|}
\hline
\textbf{奖励组件} & \textbf{权重} & \textbf{范围} & \textbf{说明} \\
\hline
距离改善奖励 & 3.0 & [-5, 5] & 向目标靠近的奖励 \\
速度奖励 & 0.3 & [0, 0.3] & 鼓励适当速度 \\
安全奖励 & 0.1 & [0, 0.1] & 保持安全距离 \\
方向奖励 & 0.2 & [-0.2, 0.2] & 朝向目标方向 \\
生存奖励 & 0.1 & 0.1 & 基础生存奖励 \\
时间惩罚 & -0.001 & -0.001 & 时间效率惩罚 \\
成功奖励 & 50.0 & 50.0 & 到达目标 \\
碰撞惩罚 & -50.0 & -50.0 & 碰撞惩罚 \\
\hline
\end{tabular}
\end{table}

\section{分阶段训练配置}

\subsection{环境复杂度设置}

\begin{table}[h]
\centering
\caption{训练阶段环境配置}
\begin{tabular}{|l|c|c|c|c|}
\hline
\textbf{阶段} & \textbf{静态障碍物} & \textbf{动态障碍物} & \textbf{训练轮数} & \textbf{目标} \\
\hline
阶段1 & 3-5个 & 0个 & 500 & 基础避障 \\
阶段2 & 15-20个 & 0个 & 1000 & 复杂静态导航 \\
阶段3 & 15-20个 & 2-4个 & 500 & 动态环境适应 \\
\hline
\end{tabular}
\end{table}

\subsection{动态障碍物运动模式}

\textbf{线性运动}：
\begin{equation}
\mathbf{p}(t) = \mathbf{p}_0 + \mathbf{v}_{linear} \cdot t
\end{equation}
速度范围：[1, 3] m/s

\textbf{圆周运动}：
\begin{align}
x(t) &= x_c + r \cos(\omega t + \phi) \\
y(t) &= y_c + r \sin(\omega t + \phi) \\
z(t) &= z_c + 3 \sin(0.5\omega t)
\end{align}
参数范围：$r \in [5, 15]$ m，$\omega \in [0.1, 0.5]$ rad/s

\textbf{振荡运动}：
\begin{equation}
\mathbf{p}(t) = \mathbf{p}_0 + \mathbf{A} \sin(\boldsymbol{\omega} t + \boldsymbol{\phi})
\end{equation}
振幅范围：$A_i \in [2, 8]$ m，频率范围：$\omega_i \in [0.1, 0.3]$ rad/s

\section{性能指标定义}

\subsection{成功率计算}

\begin{equation}
Success\_Rate = \frac{N_{success}}{N_{total}} \times 100\%
\end{equation}

成功条件：$|\mathbf{p}_{final} - \mathbf{g}| < 5.0$ m

\subsection{约束违反统计}

\begin{align}
Velocity\_Violation &= \sum_{t} \mathbb{I}(|\mathbf{v}(t)| > v_{max}) \\
Acceleration\_Violation &= \sum_{t} \mathbb{I}(|\mathbf{a}(t)| > a_{max}) \\
Collision\_Count &= \sum_{t} \mathbb{I}(d_{obs}(t) < 0)
\end{align}

\subsection{效率指标}

\begin{align}
Path\_Efficiency &= \frac{|\mathbf{g} - \mathbf{p}_0|}{L_{actual}} \\
Time\_Efficiency &= \frac{T_{optimal}}{T_{actual}}
\end{align}

其中$L_{actual}$为实际路径长度，$T_{optimal}$为理论最短时间。

\end{CJK}
\end{document}
