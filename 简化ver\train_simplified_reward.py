"""
专用简化奖励函数训练脚本
从对比训练代码中抽离出简化奖励函数，单独训练200次
训练过程中隔几个episode生成一次三维轨迹图
"""

import numpy as np
import matplotlib
matplotlib.use('TkAgg')  # Force TkAgg backend
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import json
import os
import time
import argparse
from datetime import datetime
from collections import deque

# 设置中文字体支持
import platform
if platform.system() == 'Windows':
    # Windows系统字体设置
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
elif platform.system() == 'Darwin':  # macOS
    plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'Helvetica', 'DejaVu Sans']
else:  # Linux
    plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei', 'DejaVu Sans']

plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

from dwa_rl_core import StabilizedEnvironment, StabilizedTD3Controller, td3_config

def setup_chinese_font():
    """设置中文字体支持"""
    try:
        # 尝试设置中文字体
        import matplotlib.font_manager as fm

        # 获取系统可用字体
        font_list = [f.name for f in fm.fontManager.ttflist]

        # Windows常用中文字体
        chinese_fonts = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi']

        # 查找可用的中文字体
        available_font = None
        for font in chinese_fonts:
            if font in font_list:
                available_font = font
                break

        if available_font:
            plt.rcParams['font.sans-serif'] = [available_font, 'DejaVu Sans']
            print(f"✅ 中文字体设置成功: {available_font}")
            return True
        else:
            print("⚠️ 未找到中文字体，将使用英文标签")
            return False

    except Exception as e:
        print(f"⚠️ 字体设置失败: {e}，将使用英文标签")
        return False

class SimplifiedRewardTrainer:
    """专用简化奖励函数训练器 - 支持两阶段训练"""

    def __init__(self, random_episodes=200, fixed_episodes=100, seed=42, visualization_interval=10):
        self.random_episodes = random_episodes  # 随机场景训练次数
        self.fixed_episodes = fixed_episodes    # 固定场景训练次数
        self.total_episodes = random_episodes + fixed_episodes  # 总训练次数
        self.seed = seed
        self.visualization_interval = visualization_interval
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 检测中文字体支持
        self.chinese_font_available = setup_chinese_font()

        # 创建输出目录
        self.output_dir = f'enhanced_training_{self.timestamp}'
        os.makedirs(self.output_dir, exist_ok=True)

        # 场景复杂度记录
        self.scenario_complexity = []
        self.most_complex_scenario = None

        print(f"🎯 增强型简化奖励函数训练")
        print(f"📅 训练时间: {self.timestamp}")
        print(f"🎯 总训练Episodes: {self.total_episodes}")
        print(f"  • 随机场景训练: {random_episodes} episodes")
        print(f"  • 固定场景训练: {fixed_episodes} episodes")
        print(f"📊 3D轨迹生成间隔: 每{visualization_interval}个episodes")
        print(f"📁 输出目录: {self.output_dir}")
        print("=" * 60)

    def evaluate_scenario_complexity(self, env, episode_data):
        """评估场景复杂度"""
        # 复杂度评估指标
        complexity_score = 0

        # 1. 障碍物数量和密度
        num_obstacles = len(env.obstacles)
        complexity_score += num_obstacles * 10

        # 2. 障碍物与路径的干扰程度
        start_to_goal_distance = np.linalg.norm(env.goal - env.start)
        min_obstacle_interference = float('inf')

        for obs in env.obstacles:
            # 计算障碍物到起点-终点直线的距离
            line_vec = env.goal - env.start
            point_vec = obs['center'] - env.start
            line_len = np.linalg.norm(line_vec)

            if line_len > 0:
                # 点到直线距离
                cross_product = np.cross(point_vec[:2], line_vec[:2])
                distance_to_line = abs(cross_product) / line_len

                # 考虑障碍物半径
                interference = max(0, obs['radius'] * 2 - distance_to_line)
                min_obstacle_interference = min(min_obstacle_interference, interference)

        if min_obstacle_interference != float('inf'):
            complexity_score += min_obstacle_interference * 20

        # 3. 训练表现指标（越难的场景表现越差）
        episode_reward = episode_data.get('episode_reward', 0)
        episode_steps = episode_data.get('episode_steps', 0)
        episode_result = episode_data.get('episode_result', 'timeout')

        # 奖励越低，复杂度越高
        if episode_reward < -300:
            complexity_score += 50
        elif episode_reward < -200:
            complexity_score += 30
        elif episode_reward < -100:
            complexity_score += 10

        # 步数越多，复杂度越高
        if episode_steps > 400:
            complexity_score += 30
        elif episode_steps > 300:
            complexity_score += 20
        elif episode_steps > 200:
            complexity_score += 10

        # 失败结果增加复杂度
        if episode_result == 'collision':
            complexity_score += 40
        elif episode_result == 'timeout':
            complexity_score += 30

        return complexity_score

    def save_scenario(self, env, complexity_score):
        """保存场景配置"""
        scenario_data = {
            'start': env.start.tolist(),
            'goal': env.goal.tolist(),
            'obstacles': [
                {
                    'center': obs['center'].tolist(),
                    'radius': obs['radius']
                }
                for obs in env.obstacles
            ],
            'complexity_score': complexity_score,
            'bounds': env.bounds
        }
        return scenario_data

    def load_scenario(self, env, scenario_data):
        """加载场景配置到环境"""
        env.start = np.array(scenario_data['start'], dtype=np.float64)
        env.goal = np.array(scenario_data['goal'], dtype=np.float64)
        env.obstacles = [
            {
                'center': np.array(obs['center'], dtype=np.float64),
                'radius': obs['radius']
            }
            for obs in scenario_data['obstacles']
        ]
        env.bounds = scenario_data['bounds']

        # 重置环境状态
        env.state = np.concatenate([env.start, [0.0, 0.0, 0.0]]).astype(np.float64)
        env.step_count = 0
    
    def train_simplified_reward_model(self):
        """使用简化奖励函数训练模型 - 两阶段训练"""
        print(f"\n🚀 开始增强型简化奖励函数训练")
        print(f"🎲 随机种子: {self.seed}")
        print("=" * 50)

        # 设置随机种子
        np.random.seed(self.seed)

        # 创建环境和控制器 - 强制使用简化奖励函数
        env = StabilizedEnvironment(reward_type='simplified')
        controller = StabilizedTD3Controller(td3_config)

        # 全局步数计数器（确保策略连续更新）
        global_step_count = 0
        
        # 训练统计
        training_data = {
            'reward_type': 'simplified',
            'training_phases': ['random_scenarios', 'fixed_scenario'],
            'random_episodes': self.random_episodes,
            'fixed_episodes': self.fixed_episodes,
            'total_episodes': self.total_episodes,
            'episodes': [],
            'episode_rewards': [],
            'episode_steps': [],
            'episode_step_rewards': [],
            'episode_trajectories': [],
            'success_episodes': [],
            'collision_episodes': [],
            'timeout_episodes': [],
            'success_count': 0,
            'collision_count': 0,
            'timeout_count': 0,
            'training_times': [],
            'q_values': [],
            'actor_losses': [],
            'critic_losses': [],
            'action_qualities': [],
            'visualization_episodes': [],  # 记录生成3D图的episodes
            'scenario_complexities': [],   # 场景复杂度记录
            'phase_transitions': [],       # 阶段转换记录
            'global_step_count': 0         # 全局步数计数
        }
        
        # 3D可视化设置
        plt.ion()  # 开启交互模式

        total_start_time = time.time()

        print(f"\n📍 第一阶段：随机场景训练 (Episodes 1-{self.random_episodes})")
        print("=" * 60)

        # 第一阶段：随机场景训练
        for episode in range(self.random_episodes):
            episode_start_time = time.time()
            
            # 重置环境
            state = env.reset()
            full_state = np.concatenate([env.state, state[6:]])
            
            episode_reward = 0
            step_count = 0
            episode_step_rewards = []
            trajectory = []
            episode_done = False
            episode_result = 'timeout'
            
            while step_count < 500 and not episode_done:
                # 记录轨迹
                trajectory.append(env.state[:3].copy())
                
                # 获取动作
                action, info, safe_actions = controller.get_action_with_quality(
                    full_state, env.goal, env.obstacles, add_noise=(episode > 10)
                )
                
                # 执行动作
                next_state, reward, done, env_info = env.step(action)
                next_full_state = np.concatenate([env.state, next_state[6:]])
                
                # 记录奖励和质量
                episode_step_rewards.append(reward)
                episode_reward += reward
                step_count += 1
                
                # 记录动作质量
                if 'quality_score' in info:
                    training_data['action_qualities'].append(info['quality_score'])
                
                # 存储经验
                controller.replay_buffer.add(
                    full_state.copy(),
                    action.copy(),
                    reward,
                    next_full_state.copy(),
                    done,
                    safe_actions,
                    env.goal.copy(),
                    [obs.copy() for obs in env.obstacles],
                    info.get('selected_idx', 0)
                )
                
                # 训练更新
                critic_loss, actor_loss = controller.immediate_update(batch_size=64)
                if critic_loss is not None:
                    training_data['critic_losses'].append(critic_loss)
                if actor_loss is not None:
                    training_data['actor_losses'].append(actor_loss)
                
                # 记录Q值
                if len(controller.q_values) > 0:
                    training_data['q_values'].append(controller.q_values[-1])
                
                if done:
                    episode_done = True
                    if env_info.get('success', False):
                        training_data['success_count'] += 1
                        training_data['success_episodes'].append(episode)
                        episode_result = 'success'
                    elif env_info.get('collision', False):
                        training_data['collision_count'] += 1
                        training_data['collision_episodes'].append(episode)
                        episode_result = 'collision'
                    break
                
                full_state = next_full_state
            
            if not episode_done:
                training_data['timeout_count'] += 1
                training_data['timeout_episodes'].append(episode)
                episode_result = 'timeout'
            
            episode_time = time.time() - episode_start_time

            # 更新全局步数计数
            global_step_count += step_count
            training_data['global_step_count'] = global_step_count

            # 评估场景复杂度
            episode_data = {
                'episode_reward': episode_reward,
                'episode_steps': step_count,
                'episode_result': episode_result
            }
            complexity_score = self.evaluate_scenario_complexity(env, episode_data)
            self.scenario_complexity.append({
                'episode': episode,
                'complexity_score': complexity_score,
                'scenario_data': self.save_scenario(env, complexity_score),
                'episode_data': episode_data
            })

            # 记录episode数据
            training_data['episodes'].append(episode)
            training_data['episode_rewards'].append(episode_reward)
            training_data['episode_steps'].append(step_count)
            training_data['episode_step_rewards'].append(episode_step_rewards)
            training_data['episode_trajectories'].append(trajectory)
            training_data['training_times'].append(episode_time)
            training_data['scenario_complexities'].append(complexity_score)

            # 生成3D轨迹图（每隔指定间隔）
            if (episode + 1) % self.visualization_interval == 0 or episode == 0:
                phase_name = "Phase1_Random"
                self.generate_3d_trajectory_plot(env, trajectory, episode, episode_result, training_data, phase_name)
                training_data['visualization_episodes'].append(episode)
            
            # 每5个episode报告一次
            if (episode + 1) % 5 == 0:
                recent_rewards = training_data['episode_rewards'][-5:]
                avg_reward = np.mean(recent_rewards)
                success_rate = training_data['success_count'] / (episode + 1)
                avg_steps = np.mean(training_data['episode_steps'][-5:])

                print(f"Episode {episode+1:3d}: Reward={avg_reward:7.1f}, "
                      f"Success={success_rate:.3f}, Steps={avg_steps:.1f}, "
                      f"Complexity={complexity_score:.1f}, Result={episode_result}")

        # 第一阶段结束，选择最复杂的场景
        print(f"\n📊 第一阶段完成！选择最复杂场景进行第二阶段训练...")

        # 找到复杂度最高的场景
        most_complex = max(self.scenario_complexity, key=lambda x: x['complexity_score'])
        self.most_complex_scenario = most_complex['scenario_data']

        print(f"🎯 选定最复杂场景:")
        print(f"  • Episode: {most_complex['episode'] + 1}")
        print(f"  • 复杂度分数: {most_complex['complexity_score']:.1f}")
        print(f"  • 障碍物数量: {len(most_complex['scenario_data']['obstacles'])}")
        print(f"  • 当时表现: {most_complex['episode_data']['episode_result']}")

        # 记录阶段转换
        training_data['phase_transitions'].append({
            'phase': 'random_to_fixed',
            'episode': episode + 1,
            'global_step_count': global_step_count,
            'selected_scenario': most_complex,
            'phase1_success_rate': training_data['success_count'] / self.random_episodes
        })

        print(f"\n📍 第二阶段：固定场景强化训练 (Episodes {self.random_episodes+1}-{self.total_episodes})")
        print("=" * 60)

        # 第二阶段：固定最复杂场景训练
        for episode in range(self.random_episodes, self.total_episodes):
            episode_start_time = time.time()

            # 加载固定场景
            self.load_scenario(env, self.most_complex_scenario)

            # 获取观察状态
            state = env._get_observation()
            full_state = np.concatenate([env.state, state[6:]])

            episode_reward = 0
            step_count = 0
            episode_step_rewards = []
            trajectory = []
            episode_done = False
            episode_result = 'timeout'

            while step_count < 500 and not episode_done:
                # 记录轨迹
                trajectory.append(env.state[:3].copy())

                # 获取动作（继续使用噪声进行探索）
                action, info, safe_actions = controller.get_action_with_quality(
                    full_state, env.goal, env.obstacles, add_noise=True  # 固定场景仍需探索
                )

                # 执行动作
                next_state, reward, done, env_info = env.step(action)
                next_full_state = np.concatenate([env.state, next_state[6:]])

                # 记录奖励和质量
                episode_step_rewards.append(reward)
                episode_reward += reward
                step_count += 1

                # 记录动作质量
                if 'quality_score' in info:
                    training_data['action_qualities'].append(info['quality_score'])

                # 存储经验（策略继续更新）
                controller.replay_buffer.add(
                    full_state.copy(),
                    action.copy(),
                    reward,
                    next_full_state.copy(),
                    done,
                    safe_actions,
                    env.goal.copy(),
                    [obs.copy() for obs in env.obstacles],
                    info.get('selected_idx', 0)
                )

                # 训练更新（策略连续传递）
                critic_loss, actor_loss = controller.immediate_update(batch_size=64)
                if critic_loss is not None:
                    training_data['critic_losses'].append(critic_loss)
                if actor_loss is not None:
                    training_data['actor_losses'].append(actor_loss)

                # 记录Q值
                if len(controller.q_values) > 0:
                    training_data['q_values'].append(controller.q_values[-1])

                if done:
                    episode_done = True
                    if env_info.get('success', False):
                        training_data['success_count'] += 1
                        training_data['success_episodes'].append(episode)
                        episode_result = 'success'
                    elif env_info.get('collision', False):
                        training_data['collision_count'] += 1
                        training_data['collision_episodes'].append(episode)
                        episode_result = 'collision'
                    break

                full_state = next_full_state

            if not episode_done:
                training_data['timeout_count'] += 1
                training_data['timeout_episodes'].append(episode)
                episode_result = 'timeout'

            episode_time = time.time() - episode_start_time

            # 更新全局步数计数
            global_step_count += step_count
            training_data['global_step_count'] = global_step_count

            # 记录episode数据
            training_data['episodes'].append(episode)
            training_data['episode_rewards'].append(episode_reward)
            training_data['episode_steps'].append(step_count)
            training_data['episode_step_rewards'].append(episode_step_rewards)
            training_data['episode_trajectories'].append(trajectory)
            training_data['training_times'].append(episode_time)
            training_data['scenario_complexities'].append(most_complex['complexity_score'])  # 固定场景复杂度

            # 生成3D轨迹图（每隔指定间隔）
            if (episode + 1) % self.visualization_interval == 0:
                phase_name = "Phase2_Fixed"
                self.generate_3d_trajectory_plot(env, trajectory, episode, episode_result, training_data, phase_name)
                training_data['visualization_episodes'].append(episode)

            # 每5个episode报告一次
            if (episode + 1) % 5 == 0:
                recent_rewards = training_data['episode_rewards'][-5:]
                avg_reward = np.mean(recent_rewards)
                success_rate = training_data['success_count'] / (episode + 1)
                avg_steps = np.mean(training_data['episode_steps'][-5:])
                phase2_episodes = episode - self.random_episodes + 1

                print(f"Episode {episode+1:3d} (Phase2-{phase2_episodes:2d}): Reward={avg_reward:7.1f}, "
                      f"Success={success_rate:.3f}, Steps={avg_steps:.1f}, Result={episode_result}")
        
        plt.ioff()  # 关闭交互模式
        
        total_training_time = time.time() - total_start_time
        
        # 计算最终统计
        final_stats = self.calculate_final_statistics(training_data, total_training_time)
        
        # 保存训练结果
        self.save_training_results(controller, training_data, final_stats)
        
        print(f"\n📊 增强型简化奖励函数训练完成!")
        print(f"✅ 总成功率: {final_stats['success_rate']:.3f}")
        print(f"📈 平均Episode奖励: {final_stats['avg_episode_reward']:.1f}")
        print(f"📉 奖励稳定性(CV): {final_stats['reward_cv']:.3f}")
        print(f"⏱️ 总训练时间: {final_stats['total_training_time']:.1f}秒")
        print(f"🔢 总全局步数: {final_stats['total_global_steps']}")
        print(f"🎯 最复杂场景分数: {final_stats['max_complexity_score']:.1f}")
        print(f"🎨 生成了{len(training_data['visualization_episodes'])}个3D轨迹图")

        # 分阶段统计
        phase1_success = len([ep for ep in training_data['success_episodes'] if ep < self.random_episodes])
        phase2_success = len([ep for ep in training_data['success_episodes'] if ep >= self.random_episodes])

        print(f"\n📊 分阶段统计:")
        print(f"  🎲 第一阶段(随机场景): 成功率 {phase1_success/self.random_episodes:.3f} ({phase1_success}/{self.random_episodes})")
        print(f"  🎯 第二阶段(固定场景): 成功率 {phase2_success/self.fixed_episodes:.3f} ({phase2_success}/{self.fixed_episodes})")
        
        return training_data, final_stats, controller

    def generate_3d_trajectory_plot(self, env, trajectory, episode, episode_result, training_data, phase_name=""):
        """生成3D轨迹图"""
        try:
            fig = plt.figure(figsize=(12, 10))
            ax = fig.add_subplot(111, projection='3d')

            # 绘制起点和终点
            start = env.start
            goal = env.goal
            ax.scatter(start[0], start[1], start[2], c='green', s=200, marker='o', label='Start', alpha=0.8)
            ax.scatter(goal[0], goal[1], goal[2], c='red', s=200, marker='*', label='Goal', alpha=0.8)

            # 绘制障碍物球体
            for i, obs in enumerate(env.obstacles):
                center = obs['center']
                radius = obs['radius']
                u = np.linspace(0, 2 * np.pi, 20)
                v = np.linspace(0, np.pi, 20)
                x = radius * np.outer(np.cos(u), np.sin(v)) + center[0]
                y = radius * np.outer(np.sin(u), np.sin(v)) + center[1]
                z = radius * np.outer(np.ones(np.size(u)), np.cos(v)) + center[2]
                ax.plot_surface(x, y, z, alpha=0.3, color='gray')

            # 绘制轨迹
            if trajectory and len(trajectory) > 1:
                positions = np.array(trajectory)

                # 根据结果设置轨迹样式
                if episode_result == 'success':
                    color = 'blue'
                    alpha = 0.8
                    linewidth = 3
                    linestyle = '-'
                    label = f'Episode {episode+1} - Success'
                elif episode_result == 'collision':
                    color = 'red'
                    alpha = 0.7
                    linewidth = 2
                    linestyle = '--'
                    label = f'Episode {episode+1} - Collision'
                else:  # timeout
                    color = 'orange'
                    alpha = 0.6
                    linewidth = 2
                    linestyle = ':'
                    label = f'Episode {episode+1} - Timeout'

                ax.plot(positions[:, 0], positions[:, 1], positions[:, 2],
                       color=color, alpha=alpha, linewidth=linewidth, linestyle=linestyle, label=label)

                # 标记轨迹起点和终点
                ax.scatter(positions[0, 0], positions[0, 1], positions[0, 2],
                          c='lightgreen', s=100, marker='o', alpha=0.8)
                ax.scatter(positions[-1, 0], positions[-1, 1], positions[-1, 2],
                          c='lightcoral', s=100, marker='x', alpha=0.8)

            # 设置图形属性
            ax.set_xlabel('X (m)')
            ax.set_ylabel('Y (m)')
            ax.set_zlabel('Z (m)')
            ax.set_xlim(0, 100)
            ax.set_ylim(0, 100)
            ax.set_zlim(0, 100)
            ax.grid(True, alpha=0.3)
            ax.legend()

            # 计算当前统计
            success_rate = training_data['success_count'] / (episode + 1)
            avg_reward = np.mean(training_data['episode_rewards'][-10:]) if len(training_data['episode_rewards']) >= 10 else np.mean(training_data['episode_rewards'])

            # 使用英文标题避免字体问题
            phase_info = f" ({phase_name})" if phase_name else ""
            global_steps = training_data.get('global_step_count', 0)

            if self.chinese_font_available:
                title = f'增强训练{phase_info} - Episode {episode+1}\n成功率: {success_rate:.2%} | 平均奖励: {avg_reward:.1f} | 全局步数: {global_steps} | 结果: {episode_result}'
            else:
                result_en = {'success': 'Success', 'collision': 'Collision', 'timeout': 'Timeout'}.get(episode_result, episode_result)
                title = f'Enhanced Training{phase_info} - Episode {episode+1}\nSuccess Rate: {success_rate:.2%} | Avg Reward: {avg_reward:.1f} | Global Steps: {global_steps} | Result: {result_en}'

            ax.set_title(title, fontsize=14)

            plt.tight_layout()

            # 保存图像
            phase_prefix = f"{phase_name}_" if phase_name else ""
            plot_filename = f'{phase_prefix}episode_{episode+1:03d}_3d_trajectory.png'
            plot_path = os.path.join(self.output_dir, plot_filename)
            fig.savefig(plot_path, dpi=300, bbox_inches='tight', facecolor='white')

            plt.close(fig)  # 关闭图形以释放内存

            print(f"  📊 3D轨迹图已保存: {plot_filename}")

        except Exception as e:
            print(f"  ⚠️ 3D轨迹图生成失败: {e}")

    def calculate_final_statistics(self, training_data, total_time):
        """计算最终统计指标"""
        episode_rewards = training_data['episode_rewards']
        episode_steps = training_data['episode_steps']

        # 分阶段统计
        phase1_rewards = episode_rewards[:self.random_episodes]
        phase2_rewards = episode_rewards[self.random_episodes:] if len(episode_rewards) > self.random_episodes else []

        phase1_success = len([ep for ep in training_data['success_episodes'] if ep < self.random_episodes])
        phase2_success = len([ep for ep in training_data['success_episodes'] if ep >= self.random_episodes])

        stats = {
            'total_episodes': len(episode_rewards),
            'random_episodes': self.random_episodes,
            'fixed_episodes': self.fixed_episodes,
            'success_rate': training_data['success_count'] / len(episode_rewards),
            'collision_rate': training_data['collision_count'] / len(episode_rewards),
            'timeout_rate': training_data['timeout_count'] / len(episode_rewards),
            'avg_episode_reward': np.mean(episode_rewards),
            'std_episode_reward': np.std(episode_rewards),
            'reward_cv': np.std(episode_rewards) / abs(np.mean(episode_rewards)) if np.mean(episode_rewards) != 0 else 0,
            'avg_episode_steps': np.mean(episode_steps),
            'std_episode_steps': np.std(episode_steps),
            'total_training_time': total_time,
            'avg_episode_time': total_time / len(episode_rewards),
            'final_50_success_rate': np.sum([1 for i in training_data['success_episodes'] if i >= len(episode_rewards)-50]) / min(50, len(episode_rewards)),
            'learning_improvement': self.calculate_learning_improvement(episode_rewards),
            'convergence_episode': self.find_convergence_point(episode_rewards),
            'best_episode_reward': np.max(episode_rewards),
            'worst_episode_reward': np.min(episode_rewards),
            'visualization_count': len(training_data['visualization_episodes']),
            'total_global_steps': training_data.get('global_step_count', 0),
            'max_complexity_score': max(training_data['scenario_complexities']) if training_data['scenario_complexities'] else 0,

            # 分阶段统计
            'phase1_success_rate': phase1_success / self.random_episodes if self.random_episodes > 0 else 0,
            'phase2_success_rate': phase2_success / self.fixed_episodes if self.fixed_episodes > 0 and len(phase2_rewards) > 0 else 0,
            'phase1_avg_reward': np.mean(phase1_rewards) if phase1_rewards else 0,
            'phase2_avg_reward': np.mean(phase2_rewards) if phase2_rewards else 0,
            'phase1_improvement': self.calculate_learning_improvement(phase1_rewards) if len(phase1_rewards) >= 20 else 0,
            'phase2_improvement': self.calculate_learning_improvement(phase2_rewards) if len(phase2_rewards) >= 20 else 0,

            # 场景信息
            'most_complex_scenario': self.most_complex_scenario,
            'phase_transitions': training_data.get('phase_transitions', [])
        }

        return stats

    def calculate_learning_improvement(self, episode_rewards):
        """计算学习改善程度"""
        if len(episode_rewards) < 20:
            return 0

        first_20 = np.mean(episode_rewards[:20])
        last_20 = np.mean(episode_rewards[-20:])

        if first_20 != 0:
            return (last_20 - first_20) / abs(first_20)
        else:
            return 0

    def find_convergence_point(self, episode_rewards, window=20, threshold=0.05):
        """寻找收敛点"""
        if len(episode_rewards) < window * 2:
            return len(episode_rewards)

        for i in range(window, len(episode_rewards) - window):
            current_window = episode_rewards[i:i+window]
            next_window = episode_rewards[i+window:i+2*window]

            if len(current_window) == window and len(next_window) == window:
                current_mean = np.mean(current_window)
                next_mean = np.mean(next_window)

                if current_mean != 0:
                    relative_change = abs(next_mean - current_mean) / abs(current_mean)
                    if relative_change < threshold:
                        return i + window

        return len(episode_rewards)

    def save_training_results(self, controller, training_data, final_stats):
        """保存训练结果"""
        # 保存模型
        model_path = os.path.join(self.output_dir, 'simplified_reward_model.pth')
        controller.save_model(model_path)

        # 保存训练数据
        import pickle
        data_path = os.path.join(self.output_dir, 'simplified_reward_training_data.pkl')
        with open(data_path, 'wb') as f:
            pickle.dump({
                'training_data': training_data,
                'final_stats': final_stats
            }, f)

        # 保存JSON报告
        json_path = os.path.join(self.output_dir, 'simplified_reward_training_report.json')

        # 准备JSON兼容的数据
        json_data = {
            'experiment_info': {
                'reward_type': 'simplified',
                'total_episodes': final_stats['total_episodes'],
                'timestamp': self.timestamp,
                'training_time_seconds': final_stats['total_training_time'],
                'visualization_interval': self.visualization_interval,
                'visualization_count': final_stats['visualization_count']
            },
            'performance_metrics': final_stats,
            'episode_rewards': training_data['episode_rewards'],
            'episode_steps': training_data['episode_steps'],
            'success_episodes': training_data['success_episodes'],
            'collision_episodes': training_data['collision_episodes'],
            'timeout_episodes': training_data['timeout_episodes'],
            'visualization_episodes': training_data['visualization_episodes']
        }

        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2, ensure_ascii=False)

        # 保存CSV格式的奖励数据
        csv_path = os.path.join(self.output_dir, 'simplified_reward_training_rewards.csv')
        with open(csv_path, 'w', encoding='utf-8') as f:
            f.write("Episode,Episode_Reward,Steps,Result\n")
            for i, (ep_reward, steps) in enumerate(zip(training_data['episode_rewards'], training_data['episode_steps'])):
                # 确定结果类型
                if i in training_data['success_episodes']:
                    result = 'success'
                elif i in training_data['collision_episodes']:
                    result = 'collision'
                else:
                    result = 'timeout'
                f.write(f"{i+1},{ep_reward:.3f},{steps},{result}\n")

        print(f"💾 简化奖励函数训练结果已保存:")
        print(f"  • 模型: {model_path}")
        print(f"  • 数据: {data_path}")
        print(f"  • 报告: {json_path}")
        print(f"  • CSV数据: {csv_path}")
        print(f"  • 3D轨迹图: {final_stats['visualization_count']}个PNG文件")

    def generate_summary_plot(self, training_data, final_stats):
        """生成分阶段训练总结图表"""
        try:
            # 创建两个独立的图表：第一阶段和第二阶段
            self.generate_phase_summary_plot(training_data, final_stats, phase=1)
            self.generate_phase_summary_plot(training_data, final_stats, phase=2)

            # 生成整体对比图
            self.generate_comparison_plot(training_data, final_stats)

        except Exception as e:
            print(f"⚠️ 训练总结图生成失败: {e}")

    def generate_phase_summary_plot(self, training_data, final_stats, phase=1):
        """生成单个阶段的训练总结图表"""
        try:
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))

            # 确定阶段数据范围
            if phase == 1:
                start_idx, end_idx = 0, self.random_episodes
                phase_name = "第一阶段(随机场景)" if self.chinese_font_available else "Phase 1 (Random Scenarios)"
                episodes_range = f"Episodes 1-{self.random_episodes}"
            else:
                start_idx, end_idx = self.random_episodes, len(training_data['episode_rewards'])
                phase_name = "第二阶段(固定场景)" if self.chinese_font_available else "Phase 2 (Fixed Scenario)"
                episodes_range = f"Episodes {self.random_episodes+1}-{len(training_data['episode_rewards'])}"

            # 提取阶段数据
            phase_rewards = training_data['episode_rewards'][start_idx:end_idx]
            phase_steps = training_data['episode_steps'][start_idx:end_idx]
            phase_episodes = list(range(start_idx, end_idx))

            # 阶段成功episodes
            phase_success_episodes = [ep for ep in training_data['success_episodes'] if start_idx <= ep < end_idx]
            phase_collision_episodes = [ep for ep in training_data['collision_episodes'] if start_idx <= ep < end_idx]
            phase_timeout_episodes = [ep for ep in training_data['timeout_episodes'] if start_idx <= ep < end_idx]

            # 根据字体支持选择标题
            if self.chinese_font_available:
                main_title = f'增强训练总结 - {phase_name}\n{episodes_range} ({len(phase_rewards)} Episodes)'
            else:
                main_title = f'Enhanced Training Summary - {phase_name}\n{episodes_range} ({len(phase_rewards)} Episodes)'

            fig.suptitle(main_title, fontsize=16, fontweight='bold')

            # 1. Episode奖励趋势
            axes[0, 0].plot(phase_episodes, phase_rewards, 'b-o', alpha=0.7, markersize=3)

            # 添加趋势线
            if len(phase_rewards) > 1:
                z = np.polyfit(range(len(phase_rewards)), phase_rewards, 1)
                p = np.poly1d(z)
                axes[0, 0].plot(phase_episodes, p(range(len(phase_rewards))),
                               "r--", alpha=0.8, label=f'趋势: {z[0]:.2f}x+{z[1]:.2f}' if self.chinese_font_available else f'Trend: {z[0]:.2f}x+{z[1]:.2f}')

            axes[0, 0].axhline(np.mean(phase_rewards), color='green', linestyle='--',
                              label=f'平均: {np.mean(phase_rewards):.1f}' if self.chinese_font_available else f'Mean: {np.mean(phase_rewards):.1f}')

            title1 = 'Episode奖励趋势' if self.chinese_font_available else 'Episode Reward Trend'
            xlabel1 = 'Episode'
            ylabel1 = '奖励' if self.chinese_font_available else 'Reward'

            axes[0, 0].set_title(title1)
            axes[0, 0].set_xlabel(xlabel1)
            axes[0, 0].set_ylabel(ylabel1)
            axes[0, 0].legend()
            axes[0, 0].grid(True, alpha=0.3)

            # 2. 成功率进展
            success_rates = []
            for i in range(len(phase_rewards)):
                success_count = len([ep for ep in phase_success_episodes if ep <= start_idx + i])
                success_rates.append(success_count / (i + 1))

            phase_success_rate = len(phase_success_episodes) / len(phase_rewards) if len(phase_rewards) > 0 else 0

            if self.chinese_font_available:
                final_label = f'阶段成功率: {phase_success_rate:.3f}'
                title2 = '成功率进展'
                xlabel2 = 'Episode'
                ylabel2 = '成功率'
            else:
                final_label = f'Phase Success Rate: {phase_success_rate:.3f}'
                title2 = 'Success Rate Progress'
                xlabel2 = 'Episode'
                ylabel2 = 'Success Rate'

            axes[0, 1].plot(phase_episodes, success_rates, 'g-', linewidth=2)
            axes[0, 1].axhline(phase_success_rate, color='red', linestyle='--', label=final_label)
            axes[0, 1].set_title(title2)
            axes[0, 1].set_xlabel(xlabel2)
            axes[0, 1].set_ylabel(ylabel2)
            axes[0, 1].set_ylim(0, 1)
            axes[0, 1].legend()
            axes[0, 1].grid(True, alpha=0.3)

            # 3. Episode步数分布
            if self.chinese_font_available:
                mean_label3 = f'平均: {np.mean(phase_steps):.1f}'
                title3 = 'Episode步数分布'
                xlabel3 = '步数'
                ylabel3 = '频次'
            else:
                mean_label3 = f'Mean: {np.mean(phase_steps):.1f}'
                title3 = 'Episode Steps Distribution'
                xlabel3 = 'Steps'
                ylabel3 = 'Frequency'

            axes[1, 0].hist(phase_steps, bins=20, alpha=0.7, color='orange', edgecolor='black')
            axes[1, 0].axvline(np.mean(phase_steps), color='red', linestyle='--', label=mean_label3)
            axes[1, 0].set_title(title3)
            axes[1, 0].set_xlabel(xlabel3)
            axes[1, 0].set_ylabel(ylabel3)
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)

            # 4. Episode结果分布
            success_count = len(phase_success_episodes)
            collision_count = len(phase_collision_episodes)
            timeout_count = len(phase_timeout_episodes)

            if self.chinese_font_available:
                labels = ['成功', '碰撞', '超时']
                title4 = 'Episode结果分布'
            else:
                labels = ['Success', 'Collision', 'Timeout']
                title4 = 'Episode Results Distribution'

            sizes = [success_count, collision_count, timeout_count]
            colors = ['green', 'red', 'orange']

            # 只显示非零的部分
            non_zero_sizes = []
            non_zero_labels = []
            non_zero_colors = []

            for i, size in enumerate(sizes):
                if size > 0:
                    non_zero_sizes.append(size)
                    non_zero_labels.append(labels[i])
                    non_zero_colors.append(colors[i])

            if non_zero_sizes:
                wedges, texts, autotexts = axes[1, 1].pie(non_zero_sizes, labels=non_zero_labels,
                                                         colors=non_zero_colors, autopct='%1.1f%%', startangle=90)
                # 设置百分比文字颜色为白色
                for autotext in autotexts:
                    autotext.set_color('white')
                    autotext.set_fontweight('bold')

            axes[1, 1].set_title(title4)

            plt.tight_layout()

            # 保存图片
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            phase_name_en = "phase1_random" if phase == 1 else "phase2_fixed"
            filename = f'training_summary_{phase_name_en}_{timestamp}.png'
            plt.savefig(filename, dpi=300, bbox_inches='tight')
            print(f"📊 {phase_name}训练总结图已保存: {filename}")

            plt.show()

        except Exception as e:
            print(f"⚠️ 训练总结图生成失败: {e}")

    def generate_comparison_plot(self, training_data, final_stats):
        """生成两个阶段的对比图"""
        try:
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))

            # 根据字体支持选择标题
            if self.chinese_font_available:
                main_title = f'训练阶段对比 - 总计 {self.total_episodes} Episodes'
            else:
                main_title = f'Training Phase Comparison - Total {self.total_episodes} Episodes'

            fig.suptitle(main_title, fontsize=16, fontweight='bold')

            # 分割数据
            phase1_rewards = training_data['episode_rewards'][:self.random_episodes]
            phase2_rewards = training_data['episode_rewards'][self.random_episodes:]
            phase1_episodes = list(range(0, self.random_episodes))
            phase2_episodes = list(range(self.random_episodes, len(training_data['episode_rewards'])))

            # 1. 奖励对比
            axes[0, 0].plot(phase1_episodes, phase1_rewards, 'b-', alpha=0.7, label='第一阶段(随机场景)' if self.chinese_font_available else 'Phase 1 (Random)')
            axes[0, 0].plot(phase2_episodes, phase2_rewards, 'r-', alpha=0.7, label='第二阶段(固定场景)' if self.chinese_font_available else 'Phase 2 (Fixed)')
            axes[0, 0].axvline(self.random_episodes, color='gray', linestyle='--', alpha=0.5, label='阶段分界' if self.chinese_font_available else 'Phase Boundary')
            axes[0, 0].set_title('Episode奖励对比' if self.chinese_font_available else 'Episode Reward Comparison')
            axes[0, 0].set_xlabel('Episode')
            axes[0, 0].set_ylabel('奖励' if self.chinese_font_available else 'Reward')
            axes[0, 0].legend()
            axes[0, 0].grid(True, alpha=0.3)

            # 2. 成功率对比
            phase1_success = [ep for ep in training_data['success_episodes'] if ep < self.random_episodes]
            phase2_success = [ep for ep in training_data['success_episodes'] if ep >= self.random_episodes]

            phase1_success_rate = len(phase1_success) / self.random_episodes if self.random_episodes > 0 else 0
            phase2_success_rate = len(phase2_success) / (len(training_data['episode_rewards']) - self.random_episodes) if len(training_data['episode_rewards']) > self.random_episodes else 0

            phases = ['第一阶段\n(随机场景)' if self.chinese_font_available else 'Phase 1\n(Random)',
                     '第二阶段\n(固定场景)' if self.chinese_font_available else 'Phase 2\n(Fixed)']
            success_rates = [phase1_success_rate, phase2_success_rate]

            bars = axes[0, 1].bar(phases, success_rates, color=['blue', 'red'], alpha=0.7)
            axes[0, 1].set_title('阶段成功率对比' if self.chinese_font_available else 'Phase Success Rate Comparison')
            axes[0, 1].set_ylabel('成功率' if self.chinese_font_available else 'Success Rate')
            axes[0, 1].set_ylim(0, 1)

            # 添加数值标签
            for bar, rate in zip(bars, success_rates):
                height = bar.get_height()
                axes[0, 1].text(bar.get_x() + bar.get_width()/2., height + 0.01,
                               f'{rate:.3f}', ha='center', va='bottom')

            # 3. 平均奖励对比
            phase1_avg_reward = np.mean(phase1_rewards) if len(phase1_rewards) > 0 else 0
            phase2_avg_reward = np.mean(phase2_rewards) if len(phase2_rewards) > 0 else 0

            avg_rewards = [phase1_avg_reward, phase2_avg_reward]
            bars2 = axes[1, 0].bar(phases, avg_rewards, color=['blue', 'red'], alpha=0.7)
            axes[1, 0].set_title('阶段平均奖励对比' if self.chinese_font_available else 'Phase Average Reward Comparison')
            axes[1, 0].set_ylabel('平均奖励' if self.chinese_font_available else 'Average Reward')

            # 添加数值标签
            for bar, reward in zip(bars2, avg_rewards):
                height = bar.get_height()
                axes[1, 0].text(bar.get_x() + bar.get_width()/2., height + abs(height)*0.01,
                               f'{reward:.1f}', ha='center', va='bottom')

            # 4. 整体统计对比
            phase1_collision = len([ep for ep in training_data['collision_episodes'] if ep < self.random_episodes])
            phase1_timeout = len([ep for ep in training_data['timeout_episodes'] if ep < self.random_episodes])

            phase2_collision = len([ep for ep in training_data['collision_episodes'] if ep >= self.random_episodes])
            phase2_timeout = len([ep for ep in training_data['timeout_episodes'] if ep >= self.random_episodes])

            categories = ['成功' if self.chinese_font_available else 'Success',
                         '碰撞' if self.chinese_font_available else 'Collision',
                         '超时' if self.chinese_font_available else 'Timeout']

            phase1_counts = [len(phase1_success), phase1_collision, phase1_timeout]
            phase2_counts = [len(phase2_success), phase2_collision, phase2_timeout]

            x = np.arange(len(categories))
            width = 0.35

            bars3 = axes[1, 1].bar(x - width/2, phase1_counts, width, label='第一阶段' if self.chinese_font_available else 'Phase 1', color='blue', alpha=0.7)
            bars4 = axes[1, 1].bar(x + width/2, phase2_counts, width, label='第二阶段' if self.chinese_font_available else 'Phase 2', color='red', alpha=0.7)

            axes[1, 1].set_title('阶段结果统计对比' if self.chinese_font_available else 'Phase Result Statistics Comparison')
            axes[1, 1].set_ylabel('Episode数量' if self.chinese_font_available else 'Episode Count')
            axes[1, 1].set_xticks(x)
            axes[1, 1].set_xticklabels(categories)
            axes[1, 1].legend()

            # 添加数值标签
            for bars in [bars3, bars4]:
                for bar in bars:
                    height = bar.get_height()
                    if height > 0:
                        axes[1, 1].text(bar.get_x() + bar.get_width()/2., height + 0.5,
                                       f'{int(height)}', ha='center', va='bottom')

            plt.tight_layout()

            # 保存对比图
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f'training_comparison_{timestamp}.png'
            plt.savefig(filename, dpi=300, bbox_inches='tight')
            print(f"📊 训练对比图已保存: {filename}")

            plt.show()

        except Exception as e:
            print(f"⚠️ 训练对比图生成失败: {e}")

    def run_training(self):
        """运行完整的训练流程"""
        print("🎯 开始增强型简化奖励函数训练")
        viz_episodes = list(range(1, self.total_episodes+1, self.visualization_interval))
        print(f"📊 将在Episodes {viz_episodes} 生成3D轨迹图")
        print("=" * 60)

        # 执行训练
        training_data, final_stats, controller = self.train_simplified_reward_model()

        # 生成训练总结图
        self.generate_summary_plot(training_data, final_stats)

        return {
            'training_data': training_data,
            'final_stats': final_stats,
            'controller': controller,
            'output_dir': self.output_dir
        }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='增强型简化奖励函数训练脚本')
    parser.add_argument('--random-episodes', type=int, default=200, help='随机场景训练episodes数量 (默认: 200)')
    parser.add_argument('--fixed-episodes', type=int, default=100, help='固定场景训练episodes数量 (默认: 100)')
    parser.add_argument('--seed', type=int, default=42, help='随机种子 (默认: 42)')
    parser.add_argument('--viz-interval', type=int, default=10, help='3D轨迹图生成间隔 (默认: 每10个episodes)')

    args = parser.parse_args()

    print("🎯 增强型简化奖励函数训练脚本")
    print("=" * 60)
    print("📋 训练配置:")
    print(f"  • 随机场景训练: {args.random_episodes} episodes")
    print(f"  • 固定场景训练: {args.fixed_episodes} episodes")
    print(f"  • 总训练Episodes: {args.random_episodes + args.fixed_episodes}")
    print(f"  • 随机种子: {args.seed}")
    print(f"  • 3D轨迹图间隔: 每{args.viz_interval}个episodes")
    print(f"  • 奖励函数: 简化版本 (论文风格)")
    print(f"  • 策略连续性: 全程不间断更新")
    print("=" * 60)

    # 创建训练器
    trainer = SimplifiedRewardTrainer(
        random_episodes=args.random_episodes,
        fixed_episodes=args.fixed_episodes,
        seed=args.seed,
        visualization_interval=args.viz_interval
    )

    # 运行训练
    results = trainer.run_training()

    # 打印最终结果
    final_stats = results['final_stats']
    print(f"\n🎉 增强型简化奖励函数训练完成!")
    print("=" * 60)
    print(f"📊 总体统计:")
    print(f"  • 总Episodes: {final_stats['total_episodes']} (随机{final_stats['random_episodes']} + 固定{final_stats['fixed_episodes']})")
    print(f"  • 总成功率: {final_stats['success_rate']:.3f} ({final_stats['total_episodes'] * final_stats['success_rate']:.0f}/{final_stats['total_episodes']})")
    print(f"  • 总全局步数: {final_stats['total_global_steps']}")
    print(f"  • 平均Episode奖励: {final_stats['avg_episode_reward']:.2f}")
    print(f"  • 奖励变异系数: {final_stats['reward_cv']:.3f}")
    print(f"  • 训练时间: {final_stats['total_training_time']:.1f}秒")

    print(f"\n📊 分阶段表现:")
    print(f"  🎲 第一阶段(随机场景):")
    print(f"    • 成功率: {final_stats['phase1_success_rate']:.3f}")
    print(f"    • 平均奖励: {final_stats['phase1_avg_reward']:.2f}")
    print(f"    • 学习改善: {final_stats['phase1_improvement']:+.3f}")

    print(f"  🎯 第二阶段(固定场景):")
    print(f"    • 成功率: {final_stats['phase2_success_rate']:.3f}")
    print(f"    • 平均奖励: {final_stats['phase2_avg_reward']:.2f}")
    print(f"    • 学习改善: {final_stats['phase2_improvement']:+.3f}")

    print(f"\n🎯 场景信息:")
    print(f"  • 最高复杂度分数: {final_stats['max_complexity_score']:.1f}")
    if final_stats['most_complex_scenario']:
        print(f"  • 固定场景障碍物数: {len(final_stats['most_complex_scenario']['obstacles'])}")

    print(f"\n📁 所有结果已保存到: {results['output_dir']}")
    print(f"📊 包含:")
    print(f"  • 训练好的模型文件 (策略连续更新{final_stats['total_global_steps']}步)")
    print(f"  • 详细训练数据和报告")
    print(f"  • {final_stats['visualization_count']}个3D轨迹图 (含阶段标识)")
    print(f"  • 两阶段训练总结图表")
    print(f"  • 场景复杂度分析数据")

    return results


if __name__ == "__main__":
    main()
