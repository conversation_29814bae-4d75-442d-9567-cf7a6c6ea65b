"""
测试实际DWA行为
"""

import numpy as np
from loitering_munition_dwa import LoiteringMunitionDWA

def test_actual_dwa_behavior():
    """测试实际DWA行为"""
    print("🔧 测试实际DWA行为")
    print("=" * 50)
    
    # 创建DWA实例
    dwa = LoiteringMunitionDWA()
    
    # 设置测试场景
    current_state = np.array([200, 200, 200, 25, 0.0, 0.0])  # [x,y,z,V,gamma,psi]
    goal = np.array([1800, 1800, 1800])
    obstacles = []  # 无障碍物
    
    print(f"起点: {current_state[:3]}")
    print(f"目标: {goal}")
    print(f"初始距离: {np.linalg.norm(goal - current_state[:3]):.2f}m")
    
    # 计算目标方向
    goal_direction = (goal - current_state[:3]) / np.linalg.norm(goal - current_state[:3])
    print(f"目标方向: {goal_direction}")
    
    try:
        # 生成安全控制
        safe_controls = dwa.generate_safe_control_set(
            current_state, obstacles, goal, max_actions=10
        )
        
        print(f"\n生成安全控制数量: {len(safe_controls)}")
        
        if len(safe_controls) == 0:
            print("❌ 没有生成安全控制")
            return False
        
        # 分析前5个控制
        print("\n前5个安全控制分析:")
        positive_controls = 0
        
        for i, control in enumerate(safe_controls[:5]):
            # 计算评价分数
            score = dwa.evaluate_control(control, current_state, goal, obstacles)
            
            # 预测运动方向
            try:
                predicted_states = dwa._predict_trajectory(control, current_state, 1.0)
                
                if len(predicted_states) > 1:
                    movement = predicted_states[-1][:3] - predicted_states[0][:3]
                    movement_magnitude = np.linalg.norm(movement)
                    
                    if movement_magnitude > 1e-6:
                        movement_direction = movement / movement_magnitude
                        alignment = np.dot(goal_direction, movement_direction)
                        
                        if alignment > 0.1:
                            positive_controls += 1
                        
                        print(f"  控制{i}: score={score:.3f}, 对齐度={alignment:.3f} {'✅' if alignment > 0.1 else '❌'}")
                        print(f"    控制值: a_T={control[0]:.2f}, a_N={control[1]:.2f}, mu={np.degrees(control[2]):.1f}°")
                    else:
                        print(f"  控制{i}: score={score:.3f}, 无明显移动")
                else:
                    print(f"  控制{i}: score={score:.3f}, 预测失败")
                    
            except Exception as e:
                print(f"  控制{i}: score={score:.3f}, 预测错误: {e}")
        
        print(f"\n朝向目标的控制: {positive_controls}/{min(5, len(safe_controls))}")
        
        # 找到最高分控制
        best_control = None
        best_score = -float('inf')
        best_alignment = 0
        
        for control in safe_controls:
            score = dwa.evaluate_control(control, current_state, goal, obstacles)
            if score > best_score:
                best_score = score
                best_control = control
                
                # 计算这个控制的对齐度
                try:
                    predicted_states = dwa._predict_trajectory(control, current_state, 1.0)
                    if len(predicted_states) > 1:
                        movement = predicted_states[-1][:3] - predicted_states[0][:3]
                        if np.linalg.norm(movement) > 1e-6:
                            movement_direction = movement / np.linalg.norm(movement)
                            best_alignment = np.dot(goal_direction, movement_direction)
                except:
                    best_alignment = 0
        
        print(f"\n最佳控制:")
        print(f"  控制值: {best_control}")
        print(f"  分数: {best_score:.3f}")
        print(f"  对齐度: {best_alignment:.3f}")
        print(f"  朝向目标: {'✅' if best_alignment > 0.1 else '❌'}")
        
        return best_alignment > 0.1
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_weight_values():
    """测试权重值"""
    print("\n🔍 检查DWA权重值")
    print("=" * 30)
    
    dwa = LoiteringMunitionDWA()
    
    print(f"方向权重 (alpha): {dwa.alpha}")
    print(f"速度权重 (beta): {dwa.beta}")
    print(f"距离权重 (distance_weight): {dwa.distance_weight}")
    print(f"安全权重 (delta): {dwa.delta}")
    
    total_weight = dwa.alpha + dwa.beta + dwa.distance_weight + dwa.delta
    print(f"总权重: {total_weight}")
    
    # 检查权重是否合理
    if dwa.distance_weight > 0.1:
        print("✅ 距离权重足够大，应该能引导朝向目标")
        return True
    else:
        print("❌ 距离权重太小，可能无法有效引导")
        return False

if __name__ == "__main__":
    # 测试权重值
    weight_ok = test_weight_values()
    
    # 测试实际行为
    behavior_ok = test_actual_dwa_behavior()
    
    print("\n" + "=" * 50)
    print("📊 最终测试结果:")
    print(f"  权重检查: {'✅' if weight_ok else '❌'}")
    print(f"  行为测试: {'✅' if behavior_ok else '❌'}")
    
    if weight_ok and behavior_ok:
        print("\n🎉 DWA修复验证成功！")
        print("✅ 距离权重已正确修复")
        print("✅ DWA能够选择朝向目标的控制")
        print("✅ 现在可以开始训练，应该会看到巡飞弹朝向目标移动")
    else:
        print("\n⚠️  仍有问题需要解决")
