"""
巡飞弹六自由度运动学环境 - 融合版本
结合巡飞714ver的运动模型和简化ver1的训练框架接口
"""

import numpy as np
import random
import math
import copy

class LoiteringMunitionEnvironment:
    """巡飞弹六自由度运动学环境 - 融合版本"""
    
    def __init__(self, bounds=[2000, 2000, 200], environment_config=None, reward_type='simplified'):
        self.bounds = np.array(bounds)  # [x_max, y_max, z_max]
        self.environment_config = environment_config or {}
        self.reward_type = reward_type
        
        # 巡飞弹物理参数
        self.g = 9.81  # 重力加速度
        self.dt = 0.1  # 时间步长
        
        # 运动约束参数（论文中的参数）
        self.V_min = 15.0    # 最小速度（失速速度）
        self.V_max = 60.0    # 最大速度
        self.a_T_max = 8.0   # 最大切向加速度
        self.a_N_max = 39.24 # 最大法向加速度（4g）
        self.gamma_max = np.pi/3  # 最大航迹倾斜角（60°）
        self.d_safe = 2.0    # 最小安全距离
        
        # 训练参数
        self.max_steps = 2000
        self.step_count = 0
        
        # 初始化环境
        self.reset()
    
    def reset(self, scenario_data=None):
        """重置环境"""
        self.step_count = 0
        
        if scenario_data is not None:
            # 使用指定场景数据
            self._load_scenario(scenario_data)
        else:
            # 生成新场景
            self._generate_scenario()
        
        # 初始化状态 [x, y, z, V, gamma, psi] - 使用智能初始化
        self.state = self._get_intelligent_initial_state()
        
        # 记录初始目标距离
        self._prev_goal_dist = np.linalg.norm(self.state[:3] - self.goal)
        
        return self._get_observation()

    def _get_intelligent_initial_state(self):
        """获取智能的初始状态设置 - 借鉴巡飞714ver的设计"""
        V_cruise = 25.0  # 巡航速度
        gamma_max = np.pi/3  # 最大倾斜角（60°）

        # 计算初始朝向（指向目标）
        direction = self.goal - self.start
        distance = np.linalg.norm(direction)

        if distance > 0:
            # 计算初始偏航角和倾斜角
            psi_initial = np.arctan2(direction[1], direction[0])
            gamma_initial = np.arcsin(direction[2] / distance)
            # 限制倾斜角范围
            gamma_initial = np.clip(gamma_initial, -gamma_max, gamma_max)
        else:
            psi_initial = 0.0
            gamma_initial = 0.0

        return np.array([
            self.start[0], self.start[1], self.start[2],  # 位置
            V_cruise,      # 使用巡航速度作为初始速度
            gamma_initial, # 智能计算的初始倾斜角
            psi_initial    # 智能计算的初始偏航角
        ], dtype=np.float64)

    def _generate_scenario(self):
        """生成训练场景 - 按照简化ver1的设计模式，等比放大"""
        config = self.environment_config

        # 固定起点和终点（左下角到右上角）- 等比放大简化ver1的设计
        # 简化ver1: [10,10,10] -> [80,80,80] (70米距离)
        # 融合版本: [200,200,200] -> [1800,1800,1800] (1400米距离，20倍放大)
        self.start = np.array([200.0, 200.0, 200.0], dtype=np.float64)
        self.goal = np.array([1800.0, 1800.0, 1800.0], dtype=np.float64)
        
        # 生成静态障碍物 - 按照简化ver1的设计，等比放大
        self.obstacles = []
        static_count_range = config.get('static_obstacle_count', (3, 6))
        static_count = np.random.randint(static_count_range[0], static_count_range[1] + 1)

        # 预定义障碍物位置模板（等比放大简化ver1的设计）
        # 简化ver1: [30,30,30], [50,20,40], [40,60,50], [60,40,30], [70,70,60]
        # 融合版本: 20倍放大 + 随机偏移
        obstacle_templates = [
            [600, 600, 600],    # 20 * [30,30,30]
            [1000, 400, 800],   # 20 * [50,20,40]
            [800, 1200, 1000],  # 20 * [40,60,50]
            [1200, 800, 600],   # 20 * [60,40,30]
            [1400, 1400, 1200], # 20 * [70,70,60]
            [400, 1000, 800],   # 额外位置
            [1000, 600, 1400],  # 额外位置
            [1600, 1000, 800],  # 额外位置
        ]

        for i in range(static_count):
            if i < len(obstacle_templates):
                # 使用模板位置 + 随机偏移
                base_pos = obstacle_templates[i]
                center = np.array([
                    base_pos[0] + np.random.uniform(-200, 200),  # ±200米随机偏移
                    base_pos[1] + np.random.uniform(-200, 200),
                    base_pos[2] + np.random.uniform(-200, 200)
                ])
            else:
                # 完全随机位置
                center = np.array([
                    np.random.uniform(400, self.bounds[0] - 400),
                    np.random.uniform(400, self.bounds[1] - 400),
                    np.random.uniform(400, self.bounds[2] - 400)
                ])

            # 确保不与起点终点重叠
            start_dist = np.linalg.norm(center - self.start)
            goal_dist = np.linalg.norm(center - self.goal)

            # 如果太近，调整位置
            if start_dist < 300:
                direction = (center - self.start) / start_dist
                center = self.start + direction * 300
            if goal_dist < 300:
                direction = (center - self.goal) / goal_dist
                center = self.goal + direction * 300

            # 确保在边界内
            center = np.clip(center, [300, 300, 300], [self.bounds[0]-300, self.bounds[1]-300, self.bounds[2]-300])

            # 大障碍物半径：80-120米（简化ver1是4-8米，20倍放大）
            radius = np.random.uniform(80, 120)
            obstacle = {
                'center': center,
                'radius': radius
            }
            self.obstacles.append(obstacle)
        
        # 生成动态障碍物
        self.dynamic_obstacles = []
        if config.get('enable_dynamic_obstacles', False):
            dynamic_count_range = config.get('dynamic_obstacle_count', (2, 4))
            dynamic_count = np.random.randint(dynamic_count_range[0], dynamic_count_range[1] + 1)
            
            for _ in range(dynamic_count):
                obstacle = self._generate_dynamic_obstacle()
                self.dynamic_obstacles.append(obstacle)
    
    def _generate_dynamic_obstacle(self):
        """生成动态障碍物"""
        motion_types = ['linear', 'circular', 'oscillating']
        motion_type = np.random.choice(motion_types)
        
        center = np.array([
            np.random.uniform(400, self.bounds[0] - 400),
            np.random.uniform(400, self.bounds[1] - 400),
            np.random.uniform(400, self.bounds[2] - 400)
        ])

        obstacle = {
            'center': center.copy(),
            'radius': np.random.uniform(60, 100),  # 动态障碍物稍小一些
            'motion_type': motion_type,
            'time': 0.0
        }
        
        if motion_type == 'linear':
            obstacle['motion_params'] = {
                'velocity': np.random.uniform(-10, 10, 3),
                'bounds': {
                    'x': [300, self.bounds[0] - 300],
                    'y': [300, self.bounds[1] - 300],
                    'z': [300, self.bounds[2] - 300]
                }
            }
        elif motion_type == 'circular':
            obstacle['motion_params'] = {
                'center_orbit': center.copy(),
                'radius_orbit': np.random.uniform(50, 150),
                'angular_speed': np.random.uniform(0.1, 0.5),
                'phase': np.random.uniform(0, 2*np.pi)
            }
        elif motion_type == 'oscillating':
            obstacle['motion_params'] = {
                'center_base': center.copy(),
                'amplitude': np.random.uniform(20, 80) * np.random.choice([-1, 1], 3),
                'frequency': np.random.uniform(0.1, 0.3),
                'phase': np.random.uniform(0, 2*np.pi)
            }
        
        return obstacle
    
    def step(self, control_input):
        """执行一步仿真 - 六自由度运动学"""
        # 控制输入：[a_T, a_N, μ]
        a_T = np.clip(control_input[0], -self.a_T_max, self.a_T_max)
        a_N = np.clip(control_input[1], -self.a_N_max, self.a_N_max)
        mu = np.clip(control_input[2], -np.pi/2, np.pi/2)
        
        # 当前状态
        x, y, z, V, gamma, psi = self.state
        
        # 巡飞弹六自由度运动学方程积分
        # 位置更新
        x_new = x + V * np.cos(gamma) * np.cos(psi) * self.dt
        y_new = y + V * np.cos(gamma) * np.sin(psi) * self.dt
        z_new = z + V * np.sin(gamma) * self.dt
        
        # 速度更新
        V_new = V + (a_T - self.g * np.sin(gamma)) * self.dt
        V_new = np.clip(V_new, self.V_min, self.V_max)
        
        # 角度更新
        if V > 0.1:  # 避免除零
            gamma_new = gamma + (a_N * np.cos(mu) - self.g * np.cos(gamma)) / V * self.dt
            psi_new = psi + (a_N * np.sin(mu)) / (V * np.cos(gamma)) * self.dt
        else:
            gamma_new = gamma
            psi_new = psi
        
        # 角度约束
        gamma_new = np.clip(gamma_new, -self.gamma_max, self.gamma_max)
        psi_new = psi_new % (2 * np.pi)  # 保持在[0, 2π]范围内
        
        # 更新状态
        self.state = np.array([x_new, y_new, z_new, V_new, gamma_new, psi_new], dtype=np.float64)
        
        # 更新动态障碍物
        self._update_dynamic_obstacles()
        
        # 计算奖励和终止条件
        reward, done, info = self._calculate_reward()
        
        self.step_count += 1
        if self.step_count >= self.max_steps:
            done = True
            info['timeout'] = True
        
        return self._get_observation(), reward, done, info
    
    def _update_dynamic_obstacles(self):
        """更新动态障碍物位置"""
        for obs in self.dynamic_obstacles:
            obs['time'] += self.dt
            t = obs['time']
            
            if obs['motion_type'] == 'linear':
                # 线性运动
                velocity = obs['motion_params']['velocity']
                bounds = obs['motion_params']['bounds']
                
                new_pos = obs['center'] + velocity * t
                
                # 边界反弹
                if new_pos[0] <= bounds['x'][0] or new_pos[0] >= bounds['x'][1]:
                    obs['motion_params']['velocity'][0] *= -1
                if new_pos[1] <= bounds['y'][0] or new_pos[1] >= bounds['y'][1]:
                    obs['motion_params']['velocity'][1] *= -1
                if new_pos[2] <= bounds['z'][0] or new_pos[2] >= bounds['z'][1]:
                    obs['motion_params']['velocity'][2] *= -1
                
                obs['center'] = np.clip(new_pos, 
                                      [bounds['x'][0], bounds['y'][0], bounds['z'][0]],
                                      [bounds['x'][1], bounds['y'][1], bounds['z'][1]])
            
            elif obs['motion_type'] == 'circular':
                # 圆周运动
                center_orbit = obs['motion_params']['center_orbit']
                radius_orbit = obs['motion_params']['radius_orbit']
                angular_speed = obs['motion_params']['angular_speed']
                phase = obs['motion_params']['phase']
                
                angle = angular_speed * t + phase
                obs['center'] = center_orbit + np.array([
                    radius_orbit * np.cos(angle),
                    radius_orbit * np.sin(angle),
                    0
                ])
            
            elif obs['motion_type'] == 'oscillating':
                # 振荡运动
                center_base = obs['motion_params']['center_base']
                amplitude = obs['motion_params']['amplitude']
                frequency = obs['motion_params']['frequency']
                phase = obs['motion_params']['phase']
                
                obs['center'] = center_base + amplitude * np.sin(frequency * t + phase)
    
    def _calculate_reward(self):
        """计算奖励函数"""
        if self.reward_type == 'simplified':
            return self._calculate_simplified_reward()
        else:
            return self._calculate_basic_reward()
    
    def _calculate_simplified_reward(self):
        """计算论文中的改进奖励函数"""
        pos = self.state[:3]
        
        # 计算到目标的距离
        goal_dist = np.linalg.norm(pos - self.goal)
        
        # 计算距离改善
        distance_improvement = self._prev_goal_dist - goal_dist
        self._prev_goal_dist = goal_dist

        # 终端奖励：到达目标
        if goal_dist < 50.0:
            return 2000.0, True, {'success': True, 'reason': 'goal_reached'}

        # 终端惩罚：碰撞检查
        for obs in self.obstacles + self.dynamic_obstacles:
            dist = np.linalg.norm(pos - obs['center'])
            if dist <= obs['radius'] + 5.0:  # 5米安全距离
                return -500.0, True, {'collision': True, 'reason': 'collision'}

        # 终端惩罚：越界检查
        if (pos[0] < 0 or pos[0] > self.bounds[0] or
            pos[1] < 0 or pos[1] > self.bounds[1] or
            pos[2] < 0 or pos[2] > self.bounds[2]):
            return -200.0, True, {'out_of_bounds': True, 'reason': 'out_of_bounds'}
        
        # 改进的奖励函数组件
        # 1. 距离改善奖励
        distance_reward = distance_improvement * 2.0
        
        # 2. 距离引导奖励
        max_distance = np.linalg.norm(self.start - self.goal)
        distance_progress = 1.0 - (goal_dist / max_distance)
        progress_reward = distance_progress * 20.0
        
        # 3. 前进奖励
        V = self.state[3]
        goal_direction = (self.goal - pos) / (goal_dist + 1e-6)
        velocity_direction = np.array([
            V * np.cos(self.state[4]) * np.cos(self.state[5]),
            V * np.cos(self.state[4]) * np.sin(self.state[5]),
            V * np.sin(self.state[4])
        ])
        if np.linalg.norm(velocity_direction) > 0:
            velocity_direction = velocity_direction / np.linalg.norm(velocity_direction)
            alignment = np.dot(goal_direction, velocity_direction)
            forward_reward = max(0, alignment) * 10.0
        else:
            forward_reward = 0.0
        
        # 4. 安全距离奖励
        min_obs_dist = float('inf')
        for obs in self.obstacles + self.dynamic_obstacles:
            dist = np.linalg.norm(pos - obs['center']) - obs['radius']
            min_obs_dist = min(min_obs_dist, dist)

        if min_obs_dist < float('inf'):
            if min_obs_dist > 30.0:
                safety_reward = 5.0
            elif min_obs_dist > 15.0:
                safety_reward = 2.0
            elif min_obs_dist > 5.0:
                safety_reward = 0.0
            else:
                safety_reward = -min_obs_dist * 2.0
        else:
            safety_reward = 0.0

        # 5. 速度奖励
        target_speed = 25.0
        speed_reward = max(0, 5.0 - abs(V - target_speed) * 0.2)

        # 6. 时间惩罚
        time_penalty = -0.5

        # 总奖励
        total_reward = (distance_reward + progress_reward + forward_reward +
                       safety_reward + speed_reward + time_penalty)
        
        return total_reward, False, {}
    
    def _calculate_basic_reward(self):
        """基础奖励函数（用于对比）"""
        pos = self.state[:3]
        
        # 基础奖励：接近目标
        goal_dist = np.linalg.norm(pos - self.goal)
        goal_reward = -goal_dist / 100.0
        
        # 到达目标奖励
        if goal_dist < 50.0:
            return 100.0, True, {'success': True, 'reason': 'goal_reached'}
        
        # 碰撞惩罚
        for obs in self.obstacles + self.dynamic_obstacles:
            dist = np.linalg.norm(pos - obs['center'])
            if dist <= obs['radius'] + 5.0:
                return -100.0, True, {'collision': True, 'reason': 'collision'}
        
        # 边界惩罚
        if (pos < 0).any() or (pos > self.bounds).any():
            return -50.0, True, {'out_of_bounds': True, 'reason': 'out_of_bounds'}
        
        # 时间惩罚
        time_penalty = -0.1
        
        return goal_reward + time_penalty, False, {}
    
    def _get_observation(self):
        """获取15维观测向量"""
        pos = self.state[:3]
        V, gamma, psi = self.state[3:6]
        
        # 目标方向
        goal_direction = self.goal - pos
        goal_dist = np.linalg.norm(goal_direction)
        if goal_dist > 0:
            goal_direction = goal_direction / goal_dist
        
        # 最近障碍物距离
        min_obs_dist = float('inf')
        for obs in self.obstacles + self.dynamic_obstacles:
            dist = np.linalg.norm(pos - obs['center']) - obs['radius']
            min_obs_dist = min(min_obs_dist, dist)
        
        # 构建15维观测向量
        observation = np.array([
            pos[0] / self.bounds[0],  # 归一化位置
            pos[1] / self.bounds[1],
            pos[2] / self.bounds[2],
            V / self.V_max,           # 归一化速度
            gamma / self.gamma_max,   # 归一化航迹倾斜角
            psi / (2 * np.pi),        # 归一化偏航角
            goal_direction[0],        # 目标方向
            goal_direction[1],
            goal_direction[2],
            goal_dist / 100.0,        # 归一化目标距离
            min_obs_dist / 50.0,      # 归一化障碍物距离
            len(self.obstacles) / 20.0,  # 静态障碍物数量
            len(self.dynamic_obstacles) / 10.0,  # 动态障碍物数量
            1.0 if self.dynamic_obstacles else 0.0,  # 动态标志
            pos[2] / self.bounds[2]   # 地形高度
        ], dtype=np.float64)
        
        return observation
    
    def save_scenario(self):
        """保存当前场景配置"""
        return {
            'start': self.start.copy(),
            'goal': self.goal.copy(),
            'obstacles': copy.deepcopy(self.obstacles),
            'dynamic_obstacles': copy.deepcopy(self.dynamic_obstacles),
            'bounds': self.bounds.copy()
        }
    
    def _load_scenario(self, scenario_data):
        """加载场景配置"""
        self.start = scenario_data['start'].copy()
        self.goal = scenario_data['goal'].copy()
        self.obstacles = copy.deepcopy(scenario_data['obstacles'])
        self.dynamic_obstacles = copy.deepcopy(scenario_data['dynamic_obstacles'])
        self.bounds = scenario_data['bounds'].copy()
