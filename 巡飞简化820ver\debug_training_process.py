"""
调试训练过程
检查训练中实际使用的动作
"""

import numpy as np
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from loitering_munition_environment import LoiteringMunitionEnvironment
from loitering_munition_dwa import LoiteringMunitionDWA
from td3_network import TD3Agent
from environment_config import get_environment_config, get_loitering_munition_config, get_td3_config

def debug_training_action_selection():
    """调试训练中的动作选择"""
    print("🔍 训练动作选择调试")
    print("=" * 50)
    
    # 创建组件
    env_config = get_environment_config('stage1_simple')
    lm_config = get_loitering_munition_config()
    td3_config = get_td3_config()
    
    env = LoiteringMunitionEnvironment(
        bounds=[2000, 2000, 2000],
        environment_config=env_config,
        reward_type='simplified'
    )
    
    dwa = LoiteringMunitionDWA(dt=lm_config['dt'])
    controller = TD3Agent(
        state_dim=td3_config['state_dim'],
        action_dim=td3_config['action_dim'],
        max_action=td3_config['max_action']
    )
    
    # 重置环境
    state = env.reset()
    
    print(f"环境状态:")
    print(f"  起点: {env.start}")
    print(f"  终点: {env.goal}")
    print(f"  当前状态: {env.state}")
    
    # 计算目标方向
    goal_direction = env.goal - env.state[:3]
    goal_direction_norm = goal_direction / np.linalg.norm(goal_direction)
    
    print(f"  目标方向: {goal_direction_norm}")
    
    return env, dwa, controller, goal_direction_norm

def test_early_stage_action_selection():
    """测试早期阶段动作选择"""
    print(f"\n🎲 早期阶段动作选择测试")
    print("=" * 50)
    
    env, dwa, controller, goal_direction_norm = debug_training_action_selection()
    
    print(f"模拟早期阶段（replay_buffer.size() < dwa_guidance_episodes）:")
    
    for i in range(5):
        print(f"\n  测试 {i+1}:")
        
        # 1. DWA生成安全动作集
        safe_controls = dwa.generate_safe_control_set(
            env.state, env.obstacles + env.dynamic_obstacles, env.goal, max_actions=20
        )
        
        print(f"    DWA生成安全控制数量: {len(safe_controls)}")
        
        if safe_controls:
            # 2. 早期阶段：从安全控制中随机选择
            control = safe_controls[np.random.randint(min(len(safe_controls), 10))]
            action = dwa.get_normalized_action(control)
            
            print(f"    选择的控制: {control}")
            print(f"    归一化动作: {action}")
            
            # 3. 转换为环境控制输入
            control_input = np.array([
                action[0] * env.a_T_max,
                action[1] * env.a_N_max,
                action[2] * (np.pi/2)
            ])
            
            print(f"    环境控制输入: {control_input}")
            
            # 4. 验证转换
            expected_control_input = control  # 应该等于原始控制
            print(f"    期望控制输入: {expected_control_input}")
            print(f"    转换正确: {'✅' if np.allclose(control_input, expected_control_input, atol=1e-6) else '❌'}")
            
            if not np.allclose(control_input, expected_control_input, atol=1e-6):
                print(f"    ❌ 转换错误！差异: {control_input - expected_control_input}")
            
            # 5. 预测运动方向
            trajectory = dwa._predict_trajectory(control, env.state, 0.5)
            if trajectory and len(trajectory) > 1:
                movement = trajectory[-1] - trajectory[0]
                movement_norm = movement / np.linalg.norm(movement)
                movement_angle = np.degrees(np.arccos(np.clip(np.dot(movement_norm, goal_direction_norm), -1, 1)))
                
                print(f"    预测运动方向: {movement_norm}")
                print(f"    与目标夹角: {movement_angle:.1f}°")
                print(f"    朝向目标: {'✅' if movement_angle < 90 else '❌'}")
        else:
            print(f"    ❌ 没有安全控制")

def test_late_stage_action_selection():
    """测试后期阶段动作选择"""
    print(f"\n🧠 后期阶段动作选择测试")
    print("=" * 50)
    
    env, dwa, controller, goal_direction_norm = debug_training_action_selection()
    
    print(f"模拟后期阶段（使用TD3选择）:")
    
    for i in range(5):
        print(f"\n  测试 {i+1}:")
        
        # 1. DWA生成安全动作集
        safe_controls = dwa.generate_safe_control_set(
            env.state, env.obstacles + env.dynamic_obstacles, env.goal, max_actions=20
        )
        
        if safe_controls:
            # 2. 将安全控制转换为归一化动作集
            safe_actions = [dwa.get_normalized_action(control) for control in safe_controls]
            
            print(f"    安全动作集大小: {len(safe_actions)}")
            
            # 3. TD3选择动作
            state_obs = env._get_observation()
            td3_action = controller.select_action(state_obs, noise=0.2)
            print(f"    TD3选择: {td3_action}")
            
            # 4. 从安全动作集中找到最接近的
            best_idx = 0
            min_distance = float('inf')
            
            for j, safe_action in enumerate(safe_actions):
                distance = np.linalg.norm(td3_action - safe_action)
                if distance < min_distance:
                    min_distance = distance
                    best_idx = j
            
            selected_action = safe_actions[best_idx]
            selected_control = safe_controls[best_idx]
            
            print(f"    最接近安全动作: {selected_action}")
            print(f"    对应控制: {selected_control}")
            print(f"    距离: {min_distance:.4f}")
            
            # 5. 转换为环境控制输入
            control_input = np.array([
                selected_action[0] * env.a_T_max,
                selected_action[1] * env.a_N_max,
                selected_action[2] * (np.pi/2)
            ])
            
            print(f"    环境控制输入: {control_input}")
            print(f"    期望控制输入: {selected_control}")
            print(f"    转换正确: {'✅' if np.allclose(control_input, selected_control, atol=1e-6) else '❌'}")
            
            # 6. 预测运动方向
            trajectory = dwa._predict_trajectory(selected_control, env.state, 0.5)
            if trajectory and len(trajectory) > 1:
                movement = trajectory[-1] - trajectory[0]
                movement_norm = movement / np.linalg.norm(movement)
                movement_angle = np.degrees(np.arccos(np.clip(np.dot(movement_norm, goal_direction_norm), -1, 1)))
                
                print(f"    预测运动方向: {movement_norm}")
                print(f"    与目标夹角: {movement_angle:.1f}°")
                print(f"    朝向目标: {'✅' if movement_angle < 90 else '❌'}")
        else:
            print(f"    ❌ 没有安全控制")

def test_actual_execution():
    """测试实际执行"""
    print(f"\n⚡ 实际执行测试")
    print("=" * 50)
    
    env, dwa, controller, goal_direction_norm = debug_training_action_selection()
    
    print(f"执行5步，检查实际效果:")
    
    trajectory = [env.state[:3].copy()]
    
    for step in range(5):
        print(f"\n  步骤 {step+1}:")
        
        # 模拟训练框架的动作选择逻辑
        safe_controls = dwa.generate_safe_control_set(
            env.state, env.obstacles + env.dynamic_obstacles, env.goal, max_actions=20
        )
        
        if safe_controls:
            # 早期阶段逻辑
            control = safe_controls[np.random.randint(min(len(safe_controls), 10))]
            action = dwa.get_normalized_action(control)
            
            print(f"    选择控制: {control}")
            print(f"    归一化动作: {action}")
            
            # 训练框架的转换
            control_input = np.array([
                action[0] * env.a_T_max,
                action[1] * env.a_N_max,
                action[2] * (np.pi/2)
            ])
            
            print(f"    环境控制输入: {control_input}")
            
            # 执行
            initial_pos = env.state[:3].copy()
            next_state, reward, done, info = env.step(control_input)
            new_pos = env.state[:3]
            
            trajectory.append(new_pos.copy())
            
            # 检查运动
            movement = new_pos - initial_pos
            if np.linalg.norm(movement) > 1e-6:
                movement_norm = movement / np.linalg.norm(movement)
                movement_angle = np.degrees(np.arccos(np.clip(np.dot(movement_norm, goal_direction_norm), -1, 1)))
                
                print(f"    实际运动: {movement}")
                print(f"    运动方向: {movement_norm}")
                print(f"    与目标夹角: {movement_angle:.1f}°")
                print(f"    朝向目标: {'✅' if movement_angle < 90 else '❌'}")
                print(f"    奖励: {reward:.3f}")
            
            if done:
                print(f"    终止: {info}")
                break
        else:
            print(f"    ❌ 没有安全控制")
            break
    
    # 分析整体轨迹
    if len(trajectory) > 1:
        total_movement = trajectory[-1] - trajectory[0]
        total_movement_norm = total_movement / np.linalg.norm(total_movement)
        total_angle = np.degrees(np.arccos(np.clip(np.dot(total_movement_norm, goal_direction_norm), -1, 1)))
        
        print(f"\n  整体分析:")
        print(f"    总体运动: {total_movement}")
        print(f"    总体方向: {total_movement_norm}")
        print(f"    与目标夹角: {total_angle:.1f}°")
        print(f"    整体朝向目标: {'✅' if total_angle < 90 else '❌'}")

def main():
    """主函数"""
    print("🔍 训练过程深度调试")
    print("=" * 60)
    
    try:
        # 1. 测试早期阶段
        test_early_stage_action_selection()
        
        # 2. 测试后期阶段
        test_late_stage_action_selection()
        
        # 3. 测试实际执行
        test_actual_execution()
        
        print(f"\n🎯 调试总结")
        print("=" * 30)
        print(f"如果所有测试都显示朝向目标，")
        print(f"但训练轨迹图显示朝相反方向，")
        print(f"那么问题可能在于:")
        print(f"  1. 轨迹图的绘制逻辑")
        print(f"  2. 坐标系的定义")
        print(f"  3. 长期训练中的策略退化")
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
