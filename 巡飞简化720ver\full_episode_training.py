"""
完整回合训练 - 直到到达目标
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from loitering_munition_environment import LoiteringMunitionEnvironment
from loitering_munition_dwa import LoiteringMunitionDWA
from environment_config import get_environment_config

def run_full_episode():
    """运行完整回合直到到达目标"""
    print('🚀 开始完整训练回合 - 直到到达目标...')

    # 创建环境
    env_config = get_environment_config('stage1_simple')
    env = LoiteringMunitionEnvironment(
        bounds=[2000, 2000, 2000],
        environment_config=env_config,
        reward_type='simplified'
    )

    # 创建DWA控制器
    dwa = LoiteringMunitionDWA(dt=0.1)

    # 重置环境
    obs = env.reset()
    print(f'起点: {env.start}')
    print(f'目标: {env.goal}')
    print(f'初始距离: {np.linalg.norm(env.start - env.goal):.2f}m')
    goal_radius = 50.0  # 从环境代码中看到的目标半径
    print(f'目标半径: {goal_radius}m')

    # 检查障碍物
    print(f'静态障碍物数量: {len(env.obstacles)}')
    if len(env.obstacles) > 0:
        print('静态障碍物:')
        for i, obs_info in enumerate(env.obstacles):
            center = obs_info['center']
            radius = obs_info['radius']
            print(f'  障碍物 {i+1}: 中心 [{center[0]:.1f}, {center[1]:.1f}, {center[2]:.1f}], 半径 {radius:.1f}m')

    # 记录轨迹数据
    trajectory = [env.state[:3].copy()]
    distances = []
    rewards = []
    actions = []
    dwa_scores = []
    velocities = []
    angles = []

    episode_reward = 0
    step = 0
    max_steps = 2000  # 增加最大步数，确保有足够时间到达目标

    print(f'\n开始训练回合 (最大步数: {max_steps})...')
    print('=' * 80)

    while step < max_steps:
        # 获取当前距离
        current_dist = np.linalg.norm(env.state[:3] - env.goal)
        distances.append(current_dist)
        
        # 记录当前状态信息
        velocities.append(env.state[3])  # 速度
        angles.append([env.state[4], env.state[5]])  # 航迹角
        
        # 使用DWA选择动作
        safe_controls = dwa.generate_safe_control_set(env.state, env.obstacles, env.goal, max_actions=5)
        if len(safe_controls) > 0:
            best_control = None
            best_score = -float('inf')
            
            for control in safe_controls:
                score = dwa.evaluate_control(control, env.state, env.goal, env.obstacles)
                if score > best_score:
                    best_score = score
                    best_control = control
            
            action = best_control if best_control is not None else safe_controls[0]
            dwa_scores.append(best_score)
        else:
            action = [0, 0, 0]
            dwa_scores.append(0.0)
        
        actions.append(action.copy())
        
        # 执行动作
        next_obs, reward, done, info = env.step(action)
        
        # 记录数据
        trajectory.append(env.state[:3].copy())
        rewards.append(reward)
        episode_reward += reward
        
        # 打印进度
        if step % 50 == 0 or step < 10:
            print(f'步骤 {step+1:4d}: 位置 [{env.state[0]:.1f}, {env.state[1]:.1f}, {env.state[2]:.1f}], '
                  f'距离 {current_dist:.2f}m, 奖励 {reward:.2f}, DWA分数 {dwa_scores[-1]:.3f}, '
                  f'速度 {env.state[3]:.1f}m/s')
        
        step += 1
        
        # 检查是否到达目标
        if done:
            if current_dist <= goal_radius:
                print(f'\n🎯 成功到达目标！')
                print(f'   最终距离: {current_dist:.2f}m (目标半径: {goal_radius}m)')
                print(f'   用时: {step}步')
                break
            else:
                print(f'\n⚠️  回合结束但未到达目标')
                print(f'   最终距离: {current_dist:.2f}m (目标半径: {goal_radius}m)')
                print(f'   结束原因: {info.get("termination_reason", "未知")}')
                break

    # 最终距离
    final_dist = np.linalg.norm(env.state[:3] - env.goal)
    distances.append(final_dist)

    print('\n' + '=' * 80)
    print(f'📊 完整回合统计:')
    print(f'总步数: {step}')
    print(f'初始距离: {distances[0]:.2f}m')
    print(f'最终距离: {final_dist:.2f}m')
    print(f'距离改善: {distances[0] - final_dist:.2f}m')
    print(f'改善百分比: {(distances[0] - final_dist)/distances[0]*100:.1f}%')
    print(f'总奖励: {episode_reward:.2f}')
    print(f'平均奖励: {episode_reward/step:.2f}')
    print(f'平均DWA分数: {np.mean(dwa_scores):.3f}')
    print(f'平均速度: {np.mean(velocities):.2f}m/s')
    print(f'是否到达目标: {"是" if done and final_dist <= goal_radius else "否"}')

    # 转换为numpy数组
    trajectory = np.array(trajectory)
    distances = np.array(distances)
    rewards = np.array(rewards)
    actions = np.array(actions)
    velocities = np.array(velocities)
    angles = np.array(angles)

    print(f'\n🎨 生成完整轨迹图...')
    
    # 生成可视化
    generate_full_trajectory_visualization(env, trajectory, distances, rewards, actions, 
                                         dwa_scores, velocities, angles, step, final_dist)
    
    return {
        'trajectory': trajectory,
        'distances': distances,
        'rewards': rewards,
        'actions': actions,
        'dwa_scores': dwa_scores,
        'velocities': velocities,
        'angles': angles,
        'episode_reward': episode_reward,
        'step_count': step,
        'final_distance': final_dist,
        'reached_goal': done and final_dist <= goal_radius
    }

def generate_full_trajectory_visualization(env, trajectory, distances, rewards, actions, 
                                         dwa_scores, velocities, angles, step_count, final_dist):
    """生成完整的轨迹可视化"""
    
    # 创建大图
    fig = plt.figure(figsize=(20, 16))
    
    # 3D轨迹图 (主图)
    ax1 = fig.add_subplot(241, projection='3d')
    
    # 绘制轨迹 - 用颜色表示时间进展
    n_points = len(trajectory)
    colors = plt.cm.viridis(np.linspace(0, 1, n_points))
    
    for i in range(n_points-1):
        ax1.plot(trajectory[i:i+2, 0], trajectory[i:i+2, 1], trajectory[i:i+2, 2], 
                color=colors[i], linewidth=2, alpha=0.8)
    
    # 标记关键点
    ax1.scatter(*env.start, color='green', s=200, marker='o', label='Start', 
               edgecolors='black', linewidth=2)
    ax1.scatter(*env.goal, color='red', s=200, marker='*', label='Target', 
               edgecolors='black', linewidth=2)
    ax1.scatter(*trajectory[-1], color='blue', s=150, marker='X', label='End', 
               edgecolors='black', linewidth=2)
    
    # 绘制目标区域
    u = np.linspace(0, 2 * np.pi, 20)
    v = np.linspace(0, np.pi, 20)
    goal_radius = 50.0  # 目标半径
    x_goal = env.goal[0] + goal_radius * np.outer(np.cos(u), np.sin(v))
    y_goal = env.goal[1] + goal_radius * np.outer(np.sin(u), np.sin(v))
    z_goal = env.goal[2] + goal_radius * np.outer(np.ones(np.size(u)), np.cos(v))
    ax1.plot_surface(x_goal, y_goal, z_goal, alpha=0.2, color='red')
    
    # 绘制障碍物
    for i, obstacle in enumerate(env.obstacles):
        center = obstacle['center']
        radius = obstacle['radius']
        ax1.scatter(*center, color='orange', s=100, marker='s', alpha=0.8)
        ax1.text(center[0], center[1], center[2], f'Obs{i+1}', fontsize=8)
    
    ax1.set_xlabel('X (m)')
    ax1.set_ylabel('Y (m)')
    ax1.set_zlabel('Z (m)')
    ax1.set_title(f'3D Trajectory ({step_count} steps)')
    ax1.legend()
    
    # XY平面投影
    ax2 = fig.add_subplot(242)
    # 用颜色表示时间进展
    scatter = ax2.scatter(trajectory[:, 0], trajectory[:, 1], c=range(len(trajectory)), 
                         cmap='viridis', s=10, alpha=0.7)
    ax2.plot(trajectory[:, 0], trajectory[:, 1], 'b-', linewidth=1, alpha=0.5)
    ax2.scatter(*env.start[:2], color='green', s=100, marker='o', label='Start')
    ax2.scatter(*env.goal[:2], color='red', s=100, marker='*', label='Target')
    
    # 绘制目标区域
    goal_circle = plt.Circle(env.goal[:2], 50.0, fill=False, color='red',
                           linewidth=2, linestyle='--', alpha=0.7)
    ax2.add_patch(goal_circle)
    
    # 绘制障碍物投影
    for i, obstacle in enumerate(env.obstacles):
        center = obstacle['center']
        radius = obstacle['radius']
        circle = plt.Circle(center[:2], radius, fill=False, color='orange', 
                          linewidth=2, alpha=0.7)
        ax2.add_patch(circle)
        ax2.text(center[0], center[1], f'Obs{i+1}', fontsize=8, ha='center')
    
    ax2.set_xlabel('X (m)')
    ax2.set_ylabel('Y (m)')
    ax2.set_title('XY Plane View (colored by time)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_aspect('equal')
    plt.colorbar(scatter, ax=ax2, label='Time Step')
    
    # 距离变化
    ax3 = fig.add_subplot(243)
    ax3.plot(range(len(distances)), distances, 'r-', linewidth=2)
    ax3.axhline(y=50.0, color='red', linestyle='--', alpha=0.7, label='Goal Radius')
    ax3.set_xlabel('Step')
    ax3.set_ylabel('Distance to Target (m)')
    ax3.set_title('Distance vs Steps')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 奖励变化
    ax4 = fig.add_subplot(244)
    ax4.plot(range(len(rewards)), rewards, 'g-', linewidth=1, alpha=0.8)
    # 添加移动平均
    window = min(50, len(rewards)//10)
    if window > 1:
        moving_avg = np.convolve(rewards, np.ones(window)/window, mode='valid')
        ax4.plot(range(window-1, len(rewards)), moving_avg, 'darkgreen', linewidth=2, 
                label=f'Moving Avg ({window})')
        ax4.legend()
    ax4.set_xlabel('Step')
    ax4.set_ylabel('Reward')
    ax4.set_title('Reward vs Steps')
    ax4.grid(True, alpha=0.3)
    
    # DWA分数
    ax5 = fig.add_subplot(245)
    ax5.plot(range(len(dwa_scores)), dwa_scores, 'm-', linewidth=1, alpha=0.8)
    ax5.set_xlabel('Step')
    ax5.set_ylabel('DWA Score')
    ax5.set_title('DWA Control Score vs Steps')
    ax5.grid(True, alpha=0.3)
    
    # 速度变化
    ax6 = fig.add_subplot(246)
    ax6.plot(range(len(velocities)), velocities, 'purple', linewidth=2)
    ax6.axhline(y=25.0, color='purple', linestyle='--', alpha=0.7, label='Cruise Speed')
    ax6.set_xlabel('Step')
    ax6.set_ylabel('Velocity (m/s)')
    ax6.set_title('Velocity vs Steps')
    ax6.legend()
    ax6.grid(True, alpha=0.3)
    
    # 控制输入
    ax7 = fig.add_subplot(247)
    ax7.plot(range(len(actions)), actions[:, 0], 'r-', linewidth=1, label='a_T (m/s²)', alpha=0.8)
    ax7.plot(range(len(actions)), actions[:, 1], 'g-', linewidth=1, label='a_N (m/s²)', alpha=0.8)
    ax7.plot(range(len(actions)), np.degrees(actions[:, 2]), 'b-', linewidth=1, 
            label='φ_dot (deg/s)', alpha=0.8)
    ax7.set_xlabel('Step')
    ax7.set_ylabel('Control Input')
    ax7.set_title('Control Inputs vs Steps')
    ax7.legend()
    ax7.grid(True, alpha=0.3)
    
    # 航迹角变化
    ax8 = fig.add_subplot(248)
    ax8.plot(range(len(angles)), np.degrees(angles[:, 0]), 'orange', linewidth=2, 
            label='γ (pitch)', alpha=0.8)
    ax8.plot(range(len(angles)), np.degrees(angles[:, 1]), 'cyan', linewidth=2, 
            label='ψ (yaw)', alpha=0.8)
    ax8.set_xlabel('Step')
    ax8.set_ylabel('Angle (degrees)')
    ax8.set_title('Flight Path Angles vs Steps')
    ax8.legend()
    ax8.grid(True, alpha=0.3)
    
    # 添加总体信息
    success_text = "SUCCESS" if final_dist <= 50.0 else "INCOMPLETE"
    fig.suptitle(f'Complete Episode Training - {success_text}\\n'
                f'Steps: {step_count}, Final Distance: {final_dist:.2f}m, '
                f'Goal Radius: 50.0m', fontsize=16, fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('full_episode_training_trajectory.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f'✅ 完整轨迹图已保存: full_episode_training_trajectory.png')

if __name__ == "__main__":
    results = run_full_episode()
