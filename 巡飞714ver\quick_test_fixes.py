"""
快速测试修复效果
"""

import numpy as np
import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from loitering_munition_env import LoiteringMunitionEnvironment
from loitering_munition_dwa import LoiteringMunitionDWA
from scenario_config import get_scenario_config

def quick_test():
    """快速测试修复效果"""
    print("🧪 快速测试修复效果")
    print("=" * 40)
    
    # 创建环境
    scenario_config = get_scenario_config('stage1')
    env = LoiteringMunitionEnvironment(
        bounds=scenario_config['environment_bounds'],
        fixed_scenario_config=scenario_config
    )
    dwa = LoiteringMunitionDWA()
    
    # 重置环境
    obs = env.reset(verbose=False)
    print(f"初始状态: 位置={env.state[:3]}, 速度={env.state[3]:.2f}")
    print(f"目标距离: {np.linalg.norm(env.state[:3] - env.goal):.1f}m")
    print(f"最大步数: {env.max_steps}")
    
    # 测试50步
    total_reward = 0
    for step in range(50):
        # 生成安全动作
        safe_actions = dwa.generate_safe_control_set(
            env.state, env.obstacles + env.dynamic_obstacles, env.goal, max_actions=10
        )
        
        if safe_actions:
            action = safe_actions[0]  # 使用最优动作
        else:
            action = np.array([0.0, 0.0, 0.0])  # 无动作
        
        # 执行动作
        obs, reward, done, info = env.step(action)
        total_reward += reward
        
        if step % 10 == 0 or done:
            pos = env.state[:3]
            goal_dist = np.linalg.norm(pos - env.goal)
            print(f"Step {step+1}: 位置=[{pos[0]:.0f},{pos[1]:.0f},{pos[2]:.0f}], "
                  f"目标距离={goal_dist:.1f}m, 奖励={reward:.2f}, 累积={total_reward:.2f}")
        
        if done:
            print(f"终止原因: {info.get('reason', 'unknown')}")
            break
    
    print(f"\n测试结果:")
    print(f"  总步数: {step+1}")
    print(f"  总奖励: {total_reward:.2f}")
    print(f"  平均奖励: {total_reward/(step+1):.2f}")
    print(f"  最终距离: {np.linalg.norm(env.state[:3] - env.goal):.1f}m")
    
    # 评估修复效果
    if total_reward > -50:
        print("✅ 奖励函数修复成功！奖励显著改善")
    elif total_reward > -200:
        print("⚠️ 奖励函数有改善，但仍需优化")
    else:
        print("❌ 奖励函数仍需进一步调整")
    
    if step+1 > 30:
        print("✅ 终止条件修复成功！不再过早终止")
    else:
        print("❌ 仍然过早终止，需要进一步调查")

def test_dwa_diversity():
    """测试DWA动作多样性"""
    print("\n🔍 测试DWA动作多样性")
    print("=" * 40)
    
    scenario_config = get_scenario_config('stage1')
    env = LoiteringMunitionEnvironment(
        bounds=scenario_config['environment_bounds'],
        fixed_scenario_config=scenario_config
    )
    dwa = LoiteringMunitionDWA()
    
    env.reset(verbose=False)
    
    # 生成安全动作
    safe_actions = dwa.generate_safe_control_set(
        env.state, env.obstacles + env.dynamic_obstacles, env.goal, max_actions=20
    )
    
    print(f"生成的安全动作数量: {len(safe_actions)}")
    
    if len(safe_actions) >= 10:
        print("✅ DWA动作多样性良好")
        print("前10个动作:")
        for i, action in enumerate(safe_actions[:10]):
            print(f"  {i+1}. [{action[0]:.2f}, {action[1]:.2f}, {np.degrees(action[2]):.1f}°]")
    elif len(safe_actions) >= 5:
        print("⚠️ DWA动作多样性一般")
        for i, action in enumerate(safe_actions):
            print(f"  {i+1}. [{action[0]:.2f}, {action[1]:.2f}, {np.degrees(action[2]):.1f}°]")
    else:
        print("❌ DWA动作多样性不足")

def main():
    """主测试函数"""
    print("🔧 测试训练问题修复效果")
    print("=" * 50)
    print("修复内容:")
    print("1. ✅ 奖励函数 - 更密集和引导性")
    print("2. ✅ 最大步数 - 从2000增加到3000")
    print("3. ✅ DWA参数 - 更细粒度的动作生成")
    print("4. ✅ 训练参数 - 延长DWA引导期和增加探索")
    print("=" * 50)
    
    try:
        # 测试修复效果
        quick_test()
        test_dwa_diversity()
        
        print("\n" + "=" * 50)
        print("🎯 修复测试完成！")
        print("\n💡 如果测试通过，建议:")
        print("1. 重新运行训练: python train_loitering_munition.py")
        print("2. 监控训练过程，观察:")
        print("   - 奖励是否持续改善")
        print("   - 步数是否增加到合理范围")
        print("   - 成功率是否开始上升")
        print("3. 如果仍有问题，可进一步调整参数")
        print("=" * 50)
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
