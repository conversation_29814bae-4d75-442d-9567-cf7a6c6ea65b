"""
检查GPU状态和训练设备配置
"""

import torch
import numpy as np
import time

def check_gpu_status():
    """检查GPU状态"""
    print('🔍 GPU状态检查')
    print('=' * 50)
    
    # 检查CUDA可用性
    cuda_available = torch.cuda.is_available()
    print(f'CUDA可用: {cuda_available}')
    
    if cuda_available:
        # GPU信息
        gpu_count = torch.cuda.device_count()
        print(f'GPU数量: {gpu_count}')
        
        for i in range(gpu_count):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
            print(f'GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)')
        
        # 当前GPU
        current_device = torch.cuda.current_device()
        print(f'当前GPU设备: {current_device}')
        
        # GPU内存使用情况
        if gpu_count > 0:
            memory_allocated = torch.cuda.memory_allocated() / 1024**3
            memory_reserved = torch.cuda.memory_reserved() / 1024**3
            print(f'已分配内存: {memory_allocated:.2f} GB')
            print(f'已保留内存: {memory_reserved:.2f} GB')
    
    # 推荐设备
    device = torch.device("cuda" if cuda_available else "cpu")
    print(f'推荐训练设备: {device}')
    
    return device

def test_gpu_performance():
    """测试GPU性能"""
    print('\n⚡ GPU性能测试')
    print('=' * 50)
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f'测试设备: {device}')
    
    # 创建测试张量
    size = 1000
    print(f'创建 {size}x{size} 矩阵进行测试...')
    
    # CPU测试
    print('\n📊 CPU性能:')
    start_time = time.time()
    a_cpu = torch.randn(size, size)
    b_cpu = torch.randn(size, size)
    c_cpu = torch.mm(a_cpu, b_cpu)
    cpu_time = time.time() - start_time
    print(f'  矩阵乘法耗时: {cpu_time:.4f}秒')
    
    # GPU测试（如果可用）
    if torch.cuda.is_available():
        print('\n🚀 GPU性能:')
        start_time = time.time()
        a_gpu = torch.randn(size, size).cuda()
        b_gpu = torch.randn(size, size).cuda()
        torch.cuda.synchronize()  # 确保GPU操作完成
        
        start_time = time.time()
        c_gpu = torch.mm(a_gpu, b_gpu)
        torch.cuda.synchronize()
        gpu_time = time.time() - start_time
        print(f'  矩阵乘法耗时: {gpu_time:.4f}秒')
        
        speedup = cpu_time / gpu_time
        print(f'  GPU加速比: {speedup:.2f}x')
        
        if speedup > 1:
            print('  ✅ GPU训练将显著提升性能!')
        else:
            print('  ⚠️ GPU性能提升有限，可能受限于数据传输')
    else:
        print('\n❌ GPU不可用，将使用CPU训练')

def check_training_device_config():
    """检查训练框架的设备配置"""
    print('\n🔧 训练框架设备配置')
    print('=' * 50)
    
    try:
        from td3_network import TD3Agent
        
        # 创建测试智能体
        agent = TD3Agent(state_dim=15, action_dim=3, max_action=1.0)
        print(f'TD3智能体设备: {agent.device}')
        
        # 测试模型创建
        test_state = torch.randn(1, 15)
        if torch.cuda.is_available():
            test_state = test_state.cuda()
        
        # 测试前向传播
        start_time = time.time()
        with torch.no_grad():
            action = agent.select_action(test_state.cpu().numpy())
        inference_time = time.time() - start_time
        
        print(f'模型推理耗时: {inference_time:.4f}秒')
        print(f'输出动作: {action}')
        
        print('\n✅ 训练框架设备配置正常!')
        
    except Exception as e:
        print(f'\n❌ 训练框架设备配置检查失败: {e}')
        import traceback
        traceback.print_exc()

def estimate_training_time():
    """估算训练时间"""
    print('\n⏱️ 训练时间估算')
    print('=' * 50)
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 基于之前的测试结果
    if device.type == 'cuda':
        episode_time = 120  # GPU约2分钟/episode
        print('基于GPU性能估算:')
    else:
        episode_time = 180  # CPU约3分钟/episode
        print('基于CPU性能估算:')
    
    # 不同训练配置的时间估算
    configs = [
        ('单回合测试', 1),
        ('快速测试', 8),
        ('第一阶段完整训练', 200),
        ('完整三阶段训练', 850)
    ]
    
    for name, episodes in configs:
        total_seconds = episodes * episode_time
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        
        print(f'  {name:<15}: {episodes:3d} episodes → {hours:2d}小时{minutes:2d}分钟')
    
    print(f'\n💡 建议: 使用{"GPU" if device.type == "cuda" else "CPU"}训练')
    if device.type == 'cpu':
        print('   如果有GPU可用，建议安装CUDA版本的PyTorch以加速训练')

if __name__ == "__main__":
    print('🖥️ 训练设备状态检查工具')
    print('=' * 60)
    
    device = check_gpu_status()
    test_gpu_performance()
    check_training_device_config()
    estimate_training_time()
    
    print('\n🎯 总结:')
    if torch.cuda.is_available():
        print('✅ 系统支持GPU训练，将自动使用GPU加速')
        print('🚀 建议进行第一阶段完整训练（200 episodes）')
    else:
        print('⚠️ 系统仅支持CPU训练，训练时间较长')
        print('💡 建议先进行快速测试（8 episodes）验证效果')
