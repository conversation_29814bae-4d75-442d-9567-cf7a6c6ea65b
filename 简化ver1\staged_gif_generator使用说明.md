# 分阶段训练GIF生成器使用说明

## 🎬 功能概述

`staged_gif_generator.py` 是基于"分层ver"的`fixed_gif_generator.py`修改的动画生成器，专门用于测试和可视化`staged_training.py`保存的训练结果。

## ✨ 主要特性

- **🔍 自动检测**: 自动找到最新的`staged_training_时间戳`文件夹
- **📊 智能识别**: 自动读取训练报告，识别环境配置
- **🎯 模型加载**: 自动加载对应的训练模型
- **🎬 动画生成**: 生成高质量的3D导航动画GIF
- **📈 统计信息**: 显示详细的导航性能统计

## 🚀 使用方法

### 1. 自动模式（推荐）
```bash
# 自动检测最新训练结果并生成GIF
python staged_gif_generator.py --auto

# 自定义参数
python staged_gif_generator.py --auto --steps 200 --fps 15
```

### 2. 手动指定模式
```bash
# 手动指定模型和环境
python staged_gif_generator.py --model staged_training_20250719_005011/stage_1_model.pth --environment stage1_simple

# 指定不同的环境类型
python staged_gif_generator.py --model path/to/model.pth --environment stage2_complex
python staged_gif_generator.py --model path/to/model.pth --environment stage3_dynamic
```

## ⚙️ 参数说明

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| `--auto` | 自动检测最新训练结果 | - | `--auto` |
| `--model` | 指定模型文件路径 | 自动检测 | `--model stage_1_model.pth` |
| `--environment` | 指定环境配置 | 自动检测 | `--environment stage1_simple` |
| `--steps` | 最大导航步数 | 300 | `--steps 200` |
| `--fps` | GIF帧率 | 12 | `--fps 15` |

## 🌍 支持的环境类型

- **stage1_simple**: 简单环境（3-5个静态障碍物）
- **stage2_complex**: 复杂环境（8-12个复杂静态障碍物）
- **stage3_dynamic**: 动态环境（6-10个静态 + 2-4个动态障碍物）

## 📊 输出内容

### 🎬 GIF动画文件
- **文件名格式**: `staged_navigation_{环境类型}_{时间戳}.gif`
- **示例**: `staged_navigation_Stage1_Simple_20250719_011622.gif`

### 📈 实时信息显示
GIF中包含以下实时信息：
- 当前步数和总步数
- 无人机位置坐标
- 速度信息（大小和分量）
- 加速度信息（大小和分量）
- 到目标的距离
- 当前奖励值

### 📊 统计信息
控制台输出包括：
- 导航步数
- 路径长度
- 平均/最大速度
- 平均/最大加速度
- 初始/最终目标距离
- 距离改善程度

## 🔍 自动检测逻辑

1. **查找训练目录**: 扫描所有`staged_training_*`文件夹
2. **选择最新**: 按创建时间选择最新的训练结果
3. **读取报告**: 解析训练报告获取环境配置
4. **加载模型**: 自动选择最新阶段的模型文件

## 📁 文件结构示例

```
简化ver1/
├── staged_training_20250719_005011/          # 最新训练结果
│   ├── stage_1_model.pth                     # 阶段1模型
│   ├── stage_1_training_report.json          # 训练报告
│   └── ...
├── staged_gif_generator.py                   # GIF生成器
└── staged_navigation_Stage1_Simple_20250719_011622.gif  # 生成的GIF
```

## 🎯 使用示例

### 快速生成GIF
```bash
# 最简单的使用方式
python staged_gif_generator.py --auto
```

### 生成高质量长时间GIF
```bash
# 生成300步、15fps的高质量GIF
python staged_gif_generator.py --auto --steps 300 --fps 15
```

### 测试特定阶段
```bash
# 测试阶段3的动态环境
python staged_gif_generator.py --model staged_training_20250719_005011/stage_3_model.pth --environment stage3_dynamic --steps 400
```

## 🔧 故障排除

### 问题1: 找不到训练结果
```
❌ No staged training results found
💡 Please run staged_training.py first
```
**解决方案**: 先运行`python staged_training.py`进行训练

### 问题2: 模型文件不存在
```
❌ Model file not found: path/to/model.pth
```
**解决方案**: 检查文件路径或使用`--auto`自动检测

### 问题3: 中文字体显示问题
**现象**: GIF标题显示乱码
**解决方案**: 程序已自动使用英文标题避免字体问题

## 📈 性能优化建议

- **快速预览**: 使用`--steps 50 --fps 8`快速生成预览
- **高质量**: 使用`--steps 300 --fps 15`生成高质量动画
- **长时间观察**: 使用`--steps 500`观察完整导航过程

## 🎉 成功示例

```bash
$ python staged_gif_generator.py --auto --steps 100 --fps 10

🔍 Found latest training directory: staged_training_20250719_005011
📊 Latest model: stage_1_model.pth
📋 Environment: stage1_simple
🎬 Staged Training GIF Generator
============================================================
Model: stage_1_model.pth
Environment: 阶段1：简单环境，3-5个预定义静态障碍物
Steps: 100 | FPS: 10
Dynamic Obstacles: OFF
============================================================

📊 Navigation Statistics:
========================================
Steps: 50
Path Length: 16.3m
Velocity - Avg: 3.26m/s | Max: 4.81m/s
Acceleration - Avg: 4.99m/s² | Max: 7.55m/s²
Goal Distance - Initial: 121.2m | Final: 105.5m
Distance Improvement: 15.7m
GIF File: staged_navigation_Stage1_Simple_20250719_011622.gif
========================================

🎉 Staged training GIF generation completed!
📁 File: staged_navigation_Stage1_Simple_20250719_011622.gif
🎬 Based on latest staged training results!
✅ Ready to visualize navigation performance!
```

---

**💡 提示**: 生成的GIF文件可以直接在浏览器中打开查看，或用于演示和分析训练效果！
