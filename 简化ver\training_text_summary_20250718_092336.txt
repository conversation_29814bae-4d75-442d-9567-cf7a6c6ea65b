🎯 增强型简化奖励函数训练总结报告
==================================================

📋 训练配置:
  • 总Episodes: 300
  • 第一阶段(随机场景): 200 episodes
  • 第二阶段(固定场景): 100 episodes

📈 第一阶段(随机场景)统计:
  • 平均奖励: -448.9
  • 奖励标准差: 265.5
  • 奖励范围: -2060.4 ~ -324.0
  • 平均步数: 322.4
  • 成功率: 0.930 (186/200)
  • 碰撞episodes: 0
  • 超时episodes: 14

📈 第二阶段(固定场景)统计:
  • 平均奖励: -483.9
  • 奖励标准差: 366.6
  • 奖励范围: -2125.7 ~ -322.8
  • 平均步数: 323.8
  • 成功率: 0.930 (93/100)
  • 碰撞episodes: 0
  • 超时episodes: 7

🔍 阶段对比分析:
  • 平均奖励变化: -35.0
  • 成功率变化: +0.000
  • 平均步数变化: +1.4
  • 第一阶段奖励趋势: +0.23/episode
  • 第二阶段奖励趋势: -2.03/episode

📅 报告生成时间: 2025-07-18 09:23:36
