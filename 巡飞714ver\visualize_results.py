"""
训练结果可视化脚本
生成详细的训练分析图表和3D轨迹可视化
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from mpl_toolkits.mplot3d import Axes3D
import json
import os
import argparse

from loitering_munition_env import LoiteringMunitionEnvironment
from scenario_config import get_scenario_config
from td3_network import TD3Agent, map_action_to_control

def load_training_results(results_dir):
    """加载训练结果"""
    results_file = os.path.join(results_dir, 'training_results.json')
    if not os.path.exists(results_file):
        raise FileNotFoundError(f"训练结果文件不存在: {results_file}")
    
    with open(results_file, 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    return results

def visualize_3d_trajectory(model_path, stage='stage3', save_dir=None):
    """可视化3D飞行轨迹"""
    print(f"🎬 生成3D轨迹可视化: {stage}")
    
    # 加载模型和环境
    agent = TD3Agent(state_dim=15, action_dim=3, max_action=1.0)
    agent.load(model_path)
    
    scenario_config = get_scenario_config(stage)
    env = LoiteringMunitionEnvironment(
        bounds=scenario_config['environment_bounds'],
        fixed_scenario_config=scenario_config
    )
    
    # 运行一个episode收集轨迹
    state = env.reset(verbose=False)
    trajectory = []
    dynamic_trajectories = [[] for _ in env.dynamic_obstacles]
    
    for step in range(1000):
        # 记录当前状态
        trajectory.append(env.state.copy())
        
        # 记录动态障碍物位置
        for i, dyn_obs in enumerate(env.dynamic_obstacles):
            dynamic_trajectories[i].append(dyn_obs['center'].copy())
        
        # 智能体决策
        action = agent.select_action(state, noise=0.0)
        control = map_action_to_control(action)
        
        # 执行动作
        next_state, reward, done, info = env.step(control)
        state = next_state
        
        if done:
            print(f"   轨迹完成: {info.get('reason', 'unknown')}, 步数: {step}")
            break
    
    # 绘制3D轨迹
    fig = plt.figure(figsize=(16, 12))
    ax = fig.add_subplot(111, projection='3d')
    
    # 巡飞弹轨迹
    traj = np.array(trajectory)
    ax.plot(traj[:, 0], traj[:, 1], traj[:, 2], 'b-', linewidth=3, label='巡飞弹轨迹')
    
    # 起点和终点
    ax.scatter(traj[0, 0], traj[0, 1], traj[0, 2], c='green', s=200, marker='o', label='起点')
    ax.scatter(env.goal[0], env.goal[1], env.goal[2], c='red', s=200, marker='*', label='目标')
    
    # 静态障碍物
    for i, obs in enumerate(env.obstacles):
        center = obs['center']
        radius = obs['radius']
        
        # 绘制球体（简化为多个圆圈）
        u = np.linspace(0, 2 * np.pi, 20)
        v = np.linspace(0, np.pi, 20)
        x = center[0] + radius * np.outer(np.cos(u), np.sin(v))
        y = center[1] + radius * np.outer(np.sin(u), np.sin(v))
        z = center[2] + radius * np.outer(np.ones(np.size(u)), np.cos(v))
        
        ax.plot_surface(x, y, z, alpha=0.3, color='gray')
        
        # 添加标签
        ax.text(center[0], center[1], center[2] + radius + 10, 
               f"{obs.get('type', 'obstacle')}", fontsize=8, ha='center')
    
    # 动态障碍物轨迹
    colors = ['orange', 'purple', 'brown', 'pink', 'cyan']
    for i, (dyn_obs, dyn_traj) in enumerate(zip(env.dynamic_obstacles, dynamic_trajectories)):
        if dyn_traj:
            dyn_traj = np.array(dyn_traj)
            color = colors[i % len(colors)]
            ax.plot(dyn_traj[:, 0], dyn_traj[:, 1], dyn_traj[:, 2], 
                   color=color, linewidth=2, alpha=0.7, 
                   label=f'动态威胁{i+1} ({dyn_obs.get("type", "unknown")})')
            
            # 标记初始和最终位置
            ax.scatter(dyn_traj[0, 0], dyn_traj[0, 1], dyn_traj[0, 2], 
                      c=color, s=100, marker='o', alpha=0.8)
            ax.scatter(dyn_traj[-1, 0], dyn_traj[-1, 1], dyn_traj[-1, 2], 
                      c=color, s=100, marker='x', alpha=0.8)
    
    # 设置坐标轴
    ax.set_xlabel('X (m)', fontsize=12)
    ax.set_ylabel('Y (m)', fontsize=12)
    ax.set_zlabel('Z (m)', fontsize=12)
    ax.set_title(f'巡飞弹3D飞行轨迹 - {stage}', fontsize=16, fontweight='bold')
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    
    # 设置视角
    ax.view_init(elev=20, azim=45)
    
    # 设置坐标轴范围
    bounds = env.bounds
    ax.set_xlim(0, bounds[0])
    ax.set_ylim(0, bounds[1])
    ax.set_zlim(0, bounds[2])
    
    plt.tight_layout()
    
    if save_dir:
        plt.savefig(f"{save_dir}/3d_trajectory_{stage}.png", dpi=300, bbox_inches='tight')
        print(f"   ✅ 3D轨迹图已保存: {save_dir}/3d_trajectory_{stage}.png")
    
    plt.show()
    plt.close()

def visualize_scenario_layout(stage='stage3', save_dir=None):
    """可视化场景布局"""
    print(f"🗺️ 生成场景布局图: {stage}")
    
    scenario_config = get_scenario_config(stage)
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # 俯视图
    ax1.set_aspect('equal')
    bounds = scenario_config['environment_bounds']
    
    # 绘制边界
    rect = patches.Rectangle((0, 0), bounds[0], bounds[1], 
                           linewidth=2, edgecolor='black', facecolor='lightblue', alpha=0.3)
    ax1.add_patch(rect)
    
    # 起点和终点
    start = scenario_config['start']
    goal = scenario_config['goal']
    ax1.scatter(start[0], start[1], c='green', s=200, marker='o', label='起点', zorder=5)
    ax1.scatter(goal[0], goal[1], c='red', s=200, marker='*', label='目标', zorder=5)
    
    # 静态障碍物
    for i, obs in enumerate(scenario_config['static_obstacles']):
        center = obs['center']
        radius = obs['radius']
        circle = patches.Circle((center[0], center[1]), radius, 
                              facecolor='gray', alpha=0.6, edgecolor='black')
        ax1.add_patch(circle)
        ax1.text(center[0], center[1], f"{obs.get('type', 'obs')}\n{i+1}", 
                ha='center', va='center', fontsize=8, fontweight='bold')
    
    # 动态障碍物初始位置
    for i, obs in enumerate(scenario_config['dynamic_obstacles']):
        center = obs['center']
        radius = obs['radius']
        circle = patches.Circle((center[0], center[1]), radius, 
                              facecolor='orange', alpha=0.6, edgecolor='red', linestyle='--')
        ax1.add_patch(circle)
        ax1.text(center[0], center[1], f"动态{i+1}\n{obs.get('type', 'dyn')}", 
                ha='center', va='center', fontsize=8, fontweight='bold', color='red')
    
    ax1.set_xlim(0, bounds[0])
    ax1.set_ylim(0, bounds[1])
    ax1.set_xlabel('X (m)')
    ax1.set_ylabel('Y (m)')
    ax1.set_title(f'{stage} 场景俯视图')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 侧视图
    ax2.set_aspect('equal')
    
    # 绘制边界
    rect = patches.Rectangle((0, 0), bounds[0], bounds[2], 
                           linewidth=2, edgecolor='black', facecolor='lightcyan', alpha=0.3)
    ax2.add_patch(rect)
    
    # 起点和终点
    ax2.scatter(start[0], start[2], c='green', s=200, marker='o', label='起点', zorder=5)
    ax2.scatter(goal[0], goal[2], c='red', s=200, marker='*', label='目标', zorder=5)
    
    # 静态障碍物
    for i, obs in enumerate(scenario_config['static_obstacles']):
        center = obs['center']
        radius = obs['radius']
        circle = patches.Circle((center[0], center[2]), radius, 
                              facecolor='gray', alpha=0.6, edgecolor='black')
        ax2.add_patch(circle)
    
    # 动态障碍物
    for i, obs in enumerate(scenario_config['dynamic_obstacles']):
        center = obs['center']
        radius = obs['radius']
        circle = patches.Circle((center[0], center[2]), radius, 
                              facecolor='orange', alpha=0.6, edgecolor='red', linestyle='--')
        ax2.add_patch(circle)
    
    ax2.set_xlim(0, bounds[0])
    ax2.set_ylim(0, bounds[2])
    ax2.set_xlabel('X (m)')
    ax2.set_ylabel('Z (m)')
    ax2.set_title(f'{stage} 场景侧视图')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.suptitle(f'巡飞弹作战场景布局 - {stage}', fontsize=16, fontweight='bold')
    plt.tight_layout()
    
    if save_dir:
        plt.savefig(f"{save_dir}/scenario_layout_{stage}.png", dpi=300, bbox_inches='tight')
        print(f"   ✅ 场景布局图已保存: {save_dir}/scenario_layout_{stage}.png")
    
    plt.show()
    plt.close()

def generate_comprehensive_report(results_dir):
    """生成综合分析报告"""
    print("📊 生成综合训练分析报告...")
    
    # 加载结果
    results = load_training_results(results_dir)
    
    # 创建可视化目录
    viz_dir = os.path.join(results_dir, 'comprehensive_analysis')
    os.makedirs(viz_dir, exist_ok=True)
    
    # 1. 生成场景布局图
    for stage in ['stage1', 'stage2', 'stage3']:
        visualize_scenario_layout(stage, viz_dir)
    
    # 2. 生成3D轨迹图（如果有模型文件）
    for stage in ['stage1', 'stage2', 'stage3']:
        model_path = os.path.join(results_dir, f'{stage}_model.pth')
        if os.path.exists(model_path):
            try:
                visualize_3d_trajectory(model_path, stage, viz_dir)
            except Exception as e:
                print(f"   ⚠️ {stage} 3D轨迹生成失败: {e}")
    
    print(f"✅ 综合分析报告已生成: {viz_dir}/")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='训练结果可视化')
    parser.add_argument('--results-dir', type=str, required=True, help='训练结果目录')
    parser.add_argument('--mode', choices=['trajectory', 'layout', 'comprehensive'], 
                       default='comprehensive', help='可视化模式')
    parser.add_argument('--stage', choices=['stage1', 'stage2', 'stage3'], 
                       default='stage3', help='可视化阶段')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.results_dir):
        print(f"❌ 结果目录不存在: {args.results_dir}")
        return
    
    if args.mode == 'trajectory':
        model_path = os.path.join(args.results_dir, f'{args.stage}_model.pth')
        if os.path.exists(model_path):
            visualize_3d_trajectory(model_path, args.stage)
        else:
            print(f"❌ 模型文件不存在: {model_path}")
    
    elif args.mode == 'layout':
        visualize_scenario_layout(args.stage)
    
    elif args.mode == 'comprehensive':
        generate_comprehensive_report(args.results_dir)

if __name__ == "__main__":
    main()
