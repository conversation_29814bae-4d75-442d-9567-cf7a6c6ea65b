"""
阶段选择器 - 交互式选择要测试的训练阶段
"""

import os
import sys
import subprocess
from datetime import datetime

def main():
    print("🎬 训练结果GIF生成器 - 阶段选择")
    print("=" * 50)
    print(f"📅 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"⚙️  默认设置: 最大步数=1500, 帧率=12fps")
    print()
    
    # 检查训练目录
    target_dir = "../loitering_munition_staged_training_20250726_085626"
    if not os.path.exists(target_dir):
        print("❌ 未找到训练结果目录")
        return False
    
    print(f"✅ 找到训练目录: {target_dir}")
    
    # 检查模型文件
    files = os.listdir(target_dir)
    model_files = [f for f in files if f.endswith('.pth')]
    print(f"📦 找到模型文件: {model_files}")
    
    # 显示阶段选择
    print("\n🎯 选择要测试的训练阶段:")
    print("=" * 40)
    
    stage_info = {
        1: {"name": "阶段1 - 简单环境基础训练", "success_rate": "98.3%", "env": "简单静态障碍物"},
        2: {"name": "阶段2 - 复杂静态环境训练", "success_rate": "91.6%", "env": "高密度静态障碍物"},
        3: {"name": "阶段3 - 动态环境适应训练", "success_rate": "4.0%", "env": "动态障碍物环境"}
    }
    
    available_stages = []
    for i in range(1, 4):
        if f'stage_{i}_model.pth' in model_files:
            available_stages.append(i)
            info = stage_info[i]
            print(f"  {i}. {info['name']}")
            print(f"     成功率: {info['success_rate']} | 环境: {info['env']}")
    
    print(f"  0. 退出程序")
    print("=" * 40)
    
    # 获取用户选择
    while True:
        try:
            choice = input(f"请选择要测试的阶段 ({'/'.join(map(str, available_stages))}/0): ").strip()
            
            if choice == '0':
                print("👋 退出程序")
                return False
            
            choice = int(choice)
            if choice in available_stages:
                selected_info = stage_info[choice]
                print(f"\n✅ 已选择: {selected_info['name']}")
                print(f"📊 预期成功率: {selected_info['success_rate']}")
                print(f"🌍 测试环境: {selected_info['env']}")
                
                # 确认开始
                confirm = input("\n是否开始生成GIF? (y/n): ").strip().lower()
                if confirm in ['y', 'yes', '是']:
                    print(f"\n🚀 开始生成阶段 {choice} 的GIF动画...")
                    
                    # 调用GIF生成器
                    cmd = [sys.executable, 'test_training_gif_generator.py', '--stage', str(choice), '--steps', '1500']
                    print(f"🔧 执行命令: {' '.join(cmd)}")
                    
                    try:
                        # 直接运行，显示实时输出
                        result = subprocess.run(cmd, timeout=600)
                        if result.returncode == 0:
                            print("\n✅ GIF生成完成!")
                        else:
                            print("\n❌ GIF生成失败")
                    except subprocess.TimeoutExpired:
                        print("\n⚠️ GIF生成超时（10分钟）")
                    except Exception as e:
                        print(f"\n❌ 执行出错: {e}")
                    
                    return True
                else:
                    print("❌ 用户取消操作")
                    return False
            else:
                print(f"❌ 无效选择，请输入 {'/'.join(map(str, available_stages))} 或 0")
        except ValueError:
            print("❌ 请输入有效的数字")
        except KeyboardInterrupt:
            print("\n👋 用户中断操作")
            return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print(f"\n🎉 程序执行完成!")
        else:
            print(f"\n❌ 程序执行失败或用户取消")
    except KeyboardInterrupt:
        print(f"\n👋 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序运行出现错误: {e}")
        import traceback
        traceback.print_exc()
