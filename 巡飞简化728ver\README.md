# 巡飞弹分阶段训练系统 - 融合版本

## 概述

本项目融合了简化ver1的分阶段训练框架和巡飞714ver的六自由度运动模型，创建了一个完整的巡飞弹强化学习训练系统。

## 核心特性

### 🚁 六自由度运动模型
- **状态空间**: [x, y, z, V, γ, ψ] (位置、速度、航迹倾斜角、偏航角)
- **控制输入**: [a_T, a_N, μ] (切向加速度、法向加速度、倾斜角)
- **物理约束**: 速度范围、加速度限制、角度约束
- **运动学方程**: 基于巡飞弹真实物理模型

### 🎯 分阶段训练框架
- **阶段1**: 简单环境基础训练 (3-6个静态障碍物)
- **阶段2**: 复杂静态环境训练 (8-15个静态障碍物)
- **阶段3**: 动态环境适应训练 (6-10个静态 + 2-5个动态障碍物)
- **子阶段**: 每个阶段分为随机场景探索 + 固定场景强化两个子阶段

### 🧠 智能控制算法
- **TD3网络**: 双延迟深度确定性策略梯度算法
- **DWA控制器**: 动态窗口算法提供安全引导
- **混合策略**: 早期DWA引导 + 后期RL自主学习

### 🏆 改进奖励函数
- **距离改善奖励**: 鼓励接近目标
- **进度奖励**: 基于完成度的连续奖励
- **前进奖励**: 鼓励朝目标方向移动
- **安全奖励**: 平衡安全和进取
- **速度奖励**: 鼓励保持合理巡航速度

## 文件结构

```
巡飞简化ver/
├── loitering_munition_environment.py    # 巡飞弹环境模块
├── loitering_munition_dwa.py           # DWA控制器
├── td3_network.py                      # TD3网络架构
├── staged_training_framework.py        # 分阶段训练框架
├── environment_config.py               # 环境和训练配置
├── run_staged_training.py             # 主训练脚本
├── test_fusion_system.py              # 系统测试脚本
├── demo_fusion_system.py              # 演示脚本
└── README.md                           # 说明文档
```

## 快速开始

### 1. 环境要求

```bash
pip install numpy torch matplotlib
```

### 2. 系统测试

```bash
# 运行完整测试
python test_fusion_system.py

# 运行特定测试
python test_fusion_system.py env      # 测试环境模块
python test_fusion_system.py dwa      # 测试DWA控制器
python test_fusion_system.py td3      # 测试TD3网络
python test_fusion_system.py integration  # 测试系统集成
```

### 3. 系统演示

```bash
# 运行完整演示
python demo_fusion_system.py

# 运行特定演示
python demo_fusion_system.py env      # 环境可视化
python demo_fusion_system.py motion   # 运动模型演示
python demo_fusion_system.py reward   # 奖励函数演示
python demo_fusion_system.py dwa      # DWA控制演示
```

### 4. 开始训练

```bash
# 交互式演示模式
python run_staged_training.py

# 快速测试
python run_staged_training.py --quick-test

# 完整训练（所有三个阶段）
python run_staged_training.py --start-stage 1 --end-stage 3

# 单阶段训练
python run_staged_training.py --start-stage 2 --end-stage 2

# 显示配置信息
python run_staged_training.py --show-config
```

## 训练配置

### 环境配置
- **简单环境**: 3-6个静态障碍物，适合基础训练
- **复杂环境**: 8-15个静态障碍物，提高避障能力
- **动态环境**: 静态+动态障碍物，训练适应性

### 训练参数
- **阶段1**: 150个随机 + 100个固定 = 250个episode
- **阶段2**: 200个随机 + 150个固定 = 350个episode  
- **阶段3**: 150个随机 + 100个固定 = 250个episode

### 网络参数
- **状态维度**: 15 (位置、速度、角度、目标信息、障碍物信息等)
- **动作维度**: 3 (切向加速度、法向加速度、倾斜角)
- **隐藏层**: 256维
- **学习率**: 3e-4

## 核心算法

### 分阶段训练流程

1. **随机场景探索阶段**
   - 生成多个随机场景
   - 评估场景复杂度
   - 选择最具挑战性的场景

2. **固定场景强化阶段**
   - 在选定场景中重复训练
   - 强化特定技能
   - 提高成功率

3. **阶段间模型继承**
   - 下一阶段继承上一阶段的训练模型
   - 渐进式学习
   - 避免灾难性遗忘

### DWA-RL混合策略

```python
if replay_buffer.size() < dwa_guidance_episodes:
    # 早期使用DWA引导
    action = dwa.select_best_control(state, obstacles, goal)
else:
    # 后期使用RL自主学习
    action = td3_agent.select_action(state, noise=exploration_noise)
```

## 输出结果

训练完成后会生成以下文件：

- `staged_training_results.json`: 训练结果摘要
- `staged_training_data.pkl`: 详细训练数据
- `stage_X_model.pth`: 各阶段训练模型
- `StageX_*_episode_*_3d_trajectory.png`: 轨迹可视化图片

## 性能指标

- **成功率**: 到达目标的episode比例
- **平均奖励**: episode平均累积奖励
- **场景复杂度**: 基于障碍物数量、密度、运动模式的综合评分
- **训练效率**: 各阶段训练时间和收敛速度

## 扩展功能

### 自定义环境配置

```python
# 在environment_config.py中添加新配置
ENVIRONMENT_CONFIGS["custom_env"] = {
    "static_obstacle_count": (5, 8),
    "enable_dynamic_obstacles": True,
    "dynamic_obstacle_count": (1, 3),
    "use_complex_generation": True,
    "description": "自定义环境配置"
}
```

### 自定义训练阶段

```python
# 在environment_config.py中添加新阶段
TRAINING_STAGES["stage4_extreme"] = {
    "environment": "custom_env",
    "random_episodes": 100,
    "fixed_episodes": 50,
    "total_episodes": 150,
    "description": "阶段4：极限挑战训练"
}
```

## 故障排除

### 常见问题

1. **训练不收敛**
   - 检查奖励函数设计
   - 调整学习率和网络参数
   - 增加DWA引导期

2. **内存不足**
   - 减少经验回放缓冲区大小
   - 降低批次大小
   - 减少可视化频率

3. **可视化失败**
   - 检查matplotlib安装
   - 确保有足够的磁盘空间
   - 检查文件权限

### 调试模式

```bash
# 启用详细日志
python run_staged_training.py --start-stage 1 --end-stage 1 --viz-interval 5

# 快速测试模式
python run_staged_training.py --quick-test
```

## 贡献指南

欢迎提交问题报告和改进建议！

## 许可证

本项目采用MIT许可证。
