"""
测试预训练神经网络框架
验证神经网络学习效果和性能提升
"""

import sys
import os
import numpy as np
import torch
import time
import matplotlib.pyplot as plt

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_pretrained_framework import EnhancedPretrainedFramework, EnhancedNeuralRL
from test_neural_enhanced_framework import MockEnvironment

def test_pretraining_process():
    """测试预训练过程"""
    print("🧠 测试预训练过程...")
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")
    
    # 创建框架
    framework = EnhancedPretrainedFramework(device)
    
    # 预训练（使用较小的数据量进行快速测试）
    print("开始预训练...")
    losses = framework.pretrain_action_generator(
        num_expert_episodes=100,  # 减少数据量
        num_epochs=50             # 减少训练轮数
    )
    
    print(f"✅ 预训练完成，最终损失: {losses[-1]:.4f}")
    
    # 绘制训练曲线
    plt.figure(figsize=(10, 4))
    plt.subplot(1, 2, 1)
    plt.plot(losses)
    plt.title('预训练损失曲线')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    
    # 测试生成质量
    env = MockEnvironment()
    state = env.reset()
    
    # 预训练前后对比
    print("\n📊 预训练前后对比:")
    
    # 创建未训练的框架进行对比
    untrained_framework = EnhancedPretrainedFramework(device)
    
    # 测试未训练版本
    start_time = time.time()
    untrained_actions = untrained_framework.generate_safe_action_set(
        state, env.obstacles, env.goal, 20
    )
    untrained_time = time.time() - start_time
    
    # 测试训练后版本
    start_time = time.time()
    trained_actions = framework.generate_safe_action_set(
        state, env.obstacles, env.goal, 20
    )
    trained_time = time.time() - start_time
    
    print(f"  未训练版本:")
    print(f"    生成时间: {untrained_time:.4f}s")
    print(f"    安全动作数: {len(untrained_actions)}")
    untrained_score = untrained_actions[0]['total_score'] if untrained_actions else 0
    print(f"    最优评分: {untrained_score:.3f}")

    print(f"  训练后版本:")
    print(f"    生成时间: {trained_time:.4f}s")
    print(f"    安全动作数: {len(trained_actions)}")
    trained_score = trained_actions[0]['total_score'] if trained_actions else 0
    print(f"    最优评分: {trained_score:.3f}")
    
    # 质量提升
    if untrained_actions and trained_actions:
        quality_improvement = (trained_actions[0]['total_score'] - 
                             untrained_actions[0]['total_score']) / untrained_actions[0]['total_score'] * 100
        print(f"    质量提升: {quality_improvement:.1f}%")
    
    return framework, losses

def test_action_quality():
    """测试动作质量"""
    print("\n🎯 测试动作质量...")
    
    device = 'cpu'  # 使用CPU避免内存问题
    framework = EnhancedPretrainedFramework(device)
    
    # 尝试加载预训练模型
    if not framework.load_pretrained_model():
        print("未找到预训练模型，进行快速训练...")
        framework.pretrain_action_generator(num_expert_episodes=50, num_epochs=30)
    
    # 创建多个测试场景
    test_scenarios = []
    for _ in range(10):
        env = MockEnvironment()
        state = env.reset()
        # 随机调整目标位置
        env.goal = np.random.uniform([200, 200, 30], [800, 800, 70])
        test_scenarios.append((state, env.obstacles, env.goal))
    
    print("测试多个场景的动作质量...")
    
    total_quality = 0
    total_safety_rate = 0
    total_time = 0
    
    for i, (state, obstacles, goal) in enumerate(test_scenarios):
        start_time = time.time()
        safe_actions = framework.generate_safe_action_set(state, obstacles, goal, 30)
        generation_time = time.time() - start_time
        
        total_time += generation_time
        
        if safe_actions:
            total_quality += safe_actions[0]['total_score']
            total_safety_rate += 1.0  # 生成了安全动作
            
            # 验证前5个动作的安全性
            verified_count = 0
            for action_info in safe_actions[:5]:
                trajectory = action_info['trajectory']
                # 简单验证：检查是否越界
                if not ((trajectory < 0).any() or (trajectory > [1000, 1000, 100]).any()):
                    verified_count += 1
            
            safety_rate = verified_count / 5
        else:
            safety_rate = 0.0
        
        if i % 3 == 0:
            quality_score = safe_actions[0]['total_score'] if safe_actions else 0
            print(f"  场景 {i+1}: 质量 {quality_score:.3f}, "
                  f"安全率 {safety_rate:.1%}, 时间 {generation_time:.4f}s")
    
    avg_quality = total_quality / len(test_scenarios)
    avg_safety_rate = total_safety_rate / len(test_scenarios)
    avg_time = total_time / len(test_scenarios)
    
    print(f"\n📈 动作质量测试结果:")
    print(f"  平均质量评分: {avg_quality:.3f}")
    print(f"  安全动作生成率: {avg_safety_rate:.1%}")
    print(f"  平均生成时间: {avg_time:.4f}s")
    
    return avg_quality, avg_safety_rate, avg_time

def test_complete_episode_with_pretrained():
    """测试使用预训练框架的完整episode"""
    print("\n🎮 测试预训练框架的完整episode...")
    
    device = 'cpu'
    rl_framework = EnhancedNeuralRL(state_dim=6, action_dim=3, device=device)
    
    # 加载或训练预训练模型
    if not rl_framework.pretrained_framework.load_pretrained_model():
        print("进行快速预训练...")
        rl_framework.pretrained_framework.pretrain_action_generator(
            num_expert_episodes=50, num_epochs=30
        )
    
    env = MockEnvironment()
    state = env.reset()
    
    total_reward = 0
    steps = 0
    max_steps = 150
    safety_violations = 0
    
    trajectory_points = []
    
    print("开始episode测试...")
    
    for step in range(max_steps):
        trajectory_points.append(state[:3].copy())
        
        # 使用预训练框架选择动作
        action, info = rl_framework.get_action(state, env.obstacles, env.goal, training=False)
        
        # 执行动作
        next_state, reward, done, env_info = env.step(action)
        
        # 检查安全违反
        if env_info.get('collision', False) or env_info.get('out_of_bounds', False):
            safety_violations += 1
            print(f"  ❌ 步骤 {step}: 安全违反! {'碰撞' if env_info.get('collision') else '越界'}")
        
        total_reward += reward
        steps += 1
        state = next_state
        
        # 每30步打印一次
        if step % 30 == 0:
            goal_dist = np.linalg.norm(state[:3] - env.goal)
            print(f"  步骤 {step}: 距离 {goal_dist:.1f}m, 奖励 {reward:.2f}, "
                  f"方法 {info['selection_method']}, 候选数 {info['num_safe_candidates']}")
        
        if done:
            if env_info.get('success', False):
                print(f"  ✅ 成功到达目标! 步骤: {steps}")
                break
            else:
                print(f"  ❌ Episode结束: {'碰撞' if env_info.get('collision') else '越界' if env_info.get('out_of_bounds') else '其他'}")
                break
    
    final_dist = np.linalg.norm(state[:3] - env.goal)
    success = env_info.get('success', False) if 'env_info' in locals() else False
    
    print(f"\n📈 预训练框架Episode结果:")
    print(f"  总步骤: {steps}")
    print(f"  总奖励: {total_reward:.1f}")
    print(f"  最终距离: {final_dist:.1f}m")
    print(f"  安全违反: {safety_violations} 次")
    print(f"  安全率: {(1 - safety_violations/steps)*100:.1f}%")
    print(f"  任务成功: {'✅' if success else '❌'}")
    
    return {
        'success': success,
        'steps': steps,
        'reward': total_reward,
        'final_distance': final_dist,
        'safety_violations': safety_violations,
        'safety_rate': (1 - safety_violations/steps) if steps > 0 else 0
    }

def compare_frameworks():
    """对比不同框架的性能"""
    print("\n⚖️ 对比不同框架性能...")
    
    # 测试场景
    test_scenarios = []
    for _ in range(5):
        env = MockEnvironment()
        state = env.reset()
        env.goal = np.random.uniform([300, 300, 30], [700, 700, 70])
        test_scenarios.append((state.copy(), env.obstacles.copy(), env.goal.copy()))
    
    results = {
        'pretrained': [],
        'hybrid': [],
        'random': []
    }
    
    device = 'cpu'
    
    # 预训练框架
    pretrained_framework = EnhancedNeuralRL(device=device)
    if not pretrained_framework.pretrained_framework.load_pretrained_model():
        print("快速训练预训练模型...")
        pretrained_framework.pretrained_framework.pretrain_action_generator(
            num_expert_episodes=30, num_epochs=20
        )
    
    # 混合框架
    from neural_enhanced_rl_framework import NeuralEnhancedSafeRL
    hybrid_framework = NeuralEnhancedSafeRL(device=device)
    
    print("测试各框架性能...")
    
    for i, (init_state, obstacles, goal) in enumerate(test_scenarios):
        print(f"\n测试场景 {i+1}:")
        
        for framework_name, framework in [
            ('pretrained', pretrained_framework),
            ('hybrid', hybrid_framework)
        ]:
            env = MockEnvironment()
            env.state = init_state.copy()
            env.obstacles = obstacles.copy()
            env.goal = goal.copy()
            
            state = env.state.copy()
            total_reward = 0
            steps = 0
            safety_violations = 0
            
            for step in range(50):  # 短episode测试
                action, info = framework.get_action(state, env.obstacles, env.goal, training=False)
                next_state, reward, done, env_info = env.step(action)
                
                if env_info.get('collision', False) or env_info.get('out_of_bounds', False):
                    safety_violations += 1
                
                total_reward += reward
                steps += 1
                state = next_state
                
                if done:
                    break
            
            final_dist = np.linalg.norm(state[:3] - goal)
            results[framework_name].append({
                'reward': total_reward,
                'steps': steps,
                'final_distance': final_dist,
                'safety_violations': safety_violations,
                'success': env_info.get('success', False) if 'env_info' in locals() else False
            })
            
            print(f"  {framework_name}: 奖励 {total_reward:.1f}, 距离 {final_dist:.1f}m, 违反 {safety_violations}")
        
        # 随机策略对比
        env = MockEnvironment()
        env.state = init_state.copy()
        env.obstacles = obstacles.copy()
        env.goal = goal.copy()
        
        state = env.state.copy()
        total_reward = 0
        steps = 0
        safety_violations = 0
        
        for step in range(50):
            action = np.random.uniform(-1.5, 1.5, 3)  # 保守的随机动作
            next_state, reward, done, env_info = env.step(action)
            
            if env_info.get('collision', False) or env_info.get('out_of_bounds', False):
                safety_violations += 1
            
            total_reward += reward
            steps += 1
            state = next_state
            
            if done:
                break
        
        final_dist = np.linalg.norm(state[:3] - goal)
        results['random'].append({
            'reward': total_reward,
            'steps': steps,
            'final_distance': final_dist,
            'safety_violations': safety_violations,
            'success': env_info.get('success', False) if 'env_info' in locals() else False
        })
        
        print(f"  random: 奖励 {total_reward:.1f}, 距离 {final_dist:.1f}m, 违反 {safety_violations}")
    
    # 统计结果
    print(f"\n📊 框架性能对比:")
    for framework_name, framework_results in results.items():
        avg_reward = np.mean([r['reward'] for r in framework_results])
        avg_distance = np.mean([r['final_distance'] for r in framework_results])
        avg_violations = np.mean([r['safety_violations'] for r in framework_results])
        success_rate = np.mean([r['success'] for r in framework_results])
        
        print(f"  {framework_name}:")
        print(f"    平均奖励: {avg_reward:.1f}")
        print(f"    平均最终距离: {avg_distance:.1f}m")
        print(f"    平均安全违反: {avg_violations:.1f}")
        print(f"    成功率: {success_rate:.1%}")
    
    return results

def main():
    """主测试函数"""
    print("🚀 预训练神经网络框架完整测试")
    print("=" * 60)
    
    try:
        # 1. 测试预训练过程
        framework, losses = test_pretraining_process()
        
        # 2. 测试动作质量
        quality, safety_rate, avg_time = test_action_quality()
        
        # 3. 测试完整episode
        episode_result = test_complete_episode_with_pretrained()
        
        # 4. 框架对比
        comparison_results = compare_frameworks()
        
        print("\n" + "=" * 60)
        print("📊 测试总结:")
        print(f"  预训练收敛: {'✅' if losses[-1] < 1.0 else '❌'}")
        print(f"  动作质量: {quality:.3f}")
        print(f"  安全率: {safety_rate:.1%}")
        print(f"  Episode成功: {'✅' if episode_result['success'] else '❌'}")
        print(f"  生成速度: {avg_time:.4f}s")
        
        if quality > 0.5 and safety_rate > 0.8 and episode_result['safety_rate'] > 0.9:
            print("✅ 预训练框架测试通过! 神经网络学习效果良好")
        else:
            print("⚠️ 预训练框架需要进一步优化")
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
