#!/usr/bin/env python3
"""
测试新的协同奖励函数效果
"""

import numpy as np
import matplotlib.pyplot as plt
from loitering_munition_environment import LoiteringMunitionEnvironment
from loitering_munition_dwa import LoiteringMunitionDWA
from environment_config import LOITERING_MUNITION_CONFIG, ENVIRONMENT_CONFIGS

def test_reward_function():
    """测试新奖励函数的效果"""
    print("🧪 测试新的协同奖励函数")
    print("=" * 50)
    
    # 创建环境
    env_config = ENVIRONMENT_CONFIGS['stage1_simple']
    env = LoiteringMunitionEnvironment(
        bounds=[2000, 2000, 2000],
        environment_config=env_config,
        reward_type='simplified'  # 使用新的协同奖励函数
    )
    
    # 创建DWA控制器
    dwa = LoiteringMunitionDWA(dt=LOITERING_MUNITION_CONFIG['dt'])
    
    # 运行几个episode来测试奖励函数
    episode_rewards = []
    episode_details = []
    
    for episode in range(5):
        print(f"\n--- Episode {episode + 1} ---")
        
        # 重置环境
        state = env.reset()
        episode_reward = 0
        episode_info = {
            'energy_rewards': [],
            'smoothness_rewards': [],
            'progress_rewards': [],
            'energy_efficiencies': [],
            'smoothnesses': []
        }
        
        print(f"起点: {env.start}")
        print(f"目标: {env.goal}")
        print(f"初始距离: {np.linalg.norm(env.start - env.goal):.1f}m")
        
        for step in range(100):  # 限制步数
            # DWA生成安全控制
            safe_controls = dwa.generate_safe_control_set(
                env.state, env.obstacles + env.dynamic_obstacles, env.goal,
                max_actions=10, bounds=env.bounds
            )
            
            if not safe_controls:
                print(f"  步骤 {step}: 无安全控制，停止")
                break
            
            # 选择最优控制（模拟TD3选择）
            control = safe_controls[0]  # 选择DWA评分最高的

            # 直接使用DWA生成的控制输入
            next_state, reward, done, info = env.step(control)
            episode_reward += reward
            
            # 记录奖励组成
            if not done:
                episode_info['energy_rewards'].append(info.get('energy_reward', 0))
                episode_info['smoothness_rewards'].append(info.get('smoothness_reward', 0))
                episode_info['progress_rewards'].append(info.get('progress_reward', 0))
                episode_info['energy_efficiencies'].append(info.get('energy_efficiency', 0.5))
                episode_info['smoothnesses'].append(info.get('smoothness', 0.5))

            # 显示速度信息
            if step % 20 == 0:
                current_dist = np.linalg.norm(env.state[:3] - env.goal)
                current_speed = env.state[3]
                optimal_speed = env._calculate_adaptive_optimal_speed(current_dist, env.state[:3])
                speed_adaptation = info.get('speed_adaptation', 0.5)
                print(f"  步骤 {step}: 距离 {current_dist:.1f}m, 速度 {current_speed:.1f}m/s (最优:{optimal_speed:.1f}), 适应度:{speed_adaptation:.3f}, 奖励 {reward:.2f}")
            

            
            if done:
                print(f"  Episode结束: {info}")
                break
        
        episode_rewards.append(episode_reward)
        episode_details.append(episode_info)
        
        print(f"Episode {episode + 1} 总奖励: {episode_reward:.2f}")
        
        # 分析奖励组成
        if episode_info['energy_rewards']:
            avg_energy = np.mean(episode_info['energy_rewards'])
            avg_smoothness = np.mean(episode_info['smoothness_rewards'])
            avg_progress = np.mean(episode_info['progress_rewards'])
            avg_energy_eff = np.mean(episode_info['energy_efficiencies'])
            avg_smooth_eff = np.mean(episode_info['smoothnesses'])

            print(f"  平均能耗奖励: {avg_energy:.2f}")
            print(f"  平均平滑奖励: {avg_smoothness:.2f}")
            print(f"  平均进度奖励: {avg_progress:.2f}")
            print(f"  平均能耗效率: {avg_energy_eff:.3f}")
            print(f"  平均平滑效率: {avg_smooth_eff:.3f}")

            # 显示速度策略效果
            final_speed = env.state[3]
            final_dist = np.linalg.norm(env.state[:3] - env.goal)
            final_optimal_speed = env._calculate_adaptive_optimal_speed(final_dist, env.state[:3])
            print(f"  最终速度: {final_speed:.1f}m/s (最优: {final_optimal_speed:.1f}m/s)")
            print(f"  速度策略差异: DWA固定25m/s vs TD3自适应{final_optimal_speed:.1f}m/s")
    
    # 分析结果
    print(f"\n📊 总体分析")
    print(f"平均episode奖励: {np.mean(episode_rewards):.2f}")
    print(f"奖励标准差: {np.std(episode_rewards):.2f}")
    print(f"最高奖励: {np.max(episode_rewards):.2f}")
    print(f"最低奖励: {np.min(episode_rewards):.2f}")
    
    return episode_rewards, episode_details

def test_reward_consistency():
    """测试奖励函数的一致性（不受随机障碍物影响）"""
    print(f"\n🔄 测试奖励一致性")
    print("=" * 50)
    
    # 固定起终点，测试不同障碍物配置下的奖励
    start_pos = np.array([200, 200, 200])
    goal_pos = np.array([1800, 1800, 1800])
    
    rewards_different_obstacles = []
    
    for test_run in range(3):
        print(f"\n测试运行 {test_run + 1} (不同障碍物配置)")
        
        # 创建环境（每次会生成不同的随机障碍物）
        env_config = ENVIRONMENT_CONFIGS['stage1_simple']
        env = LoiteringMunitionEnvironment(
            bounds=[2000, 2000, 2000],
            environment_config=env_config,
            reward_type='simplified'
        )
        
        # 设置固定起终点
        env.start = start_pos.copy()
        env.goal = goal_pos.copy()
        
        # 重置到固定状态
        state = env.reset()
        env.state[:3] = start_pos.copy()
        
        dwa = LoiteringMunitionDWA(dt=LOITERING_MUNITION_CONFIG['dt'])
        
        episode_reward = 0
        for step in range(50):  # 固定步数
            safe_controls = dwa.generate_safe_control_set(
                env.state, env.obstacles + env.dynamic_obstacles, env.goal,
                max_actions=10, bounds=env.bounds
            )
            
            if not safe_controls:
                break

            control = safe_controls[0]

            # 直接使用DWA生成的控制输入
            next_state, reward, done, info = env.step(control)
            episode_reward += reward
            
            if done:
                break
        
        rewards_different_obstacles.append(episode_reward)
        print(f"  障碍物配置 {test_run + 1}: 奖励 {episode_reward:.2f}")
    
    # 分析一致性
    reward_std = np.std(rewards_different_obstacles)
    reward_mean = np.mean(rewards_different_obstacles)
    consistency_ratio = reward_std / abs(reward_mean) if reward_mean != 0 else float('inf')
    
    print(f"\n📈 一致性分析:")
    print(f"平均奖励: {reward_mean:.2f}")
    print(f"标准差: {reward_std:.2f}")
    print(f"变异系数: {consistency_ratio:.3f}")
    
    if consistency_ratio < 0.1:
        print("✅ 奖励函数一致性很好（变异系数 < 0.1）")
    elif consistency_ratio < 0.2:
        print("⚠️ 奖励函数一致性一般（变异系数 < 0.2）")
    else:
        print("❌ 奖励函数一致性较差（变异系数 >= 0.2）")
    
    return rewards_different_obstacles

if __name__ == "__main__":
    print("🚀 测试新的协同奖励函数")
    print("=" * 60)
    
    # 测试奖励函数效果
    episode_rewards, episode_details = test_reward_function()
    
    # 测试奖励一致性
    consistency_rewards = test_reward_consistency()
    
    print(f"\n🎉 测试完成！")
    print(f"新奖励函数专注于优化DWA无法处理的长期指标：")
    print(f"  ✅ 能耗效率：控制输入的能耗优化")
    print(f"  ✅ 路径平滑性：控制序列的平滑性优化")
    print(f"  ✅ 与DWA协同：不与DWA的目标冲突")
