"""
测试分阶段训练GIF生成器
"""

import os
import sys

def test_gif_generator():
    """测试GIF生成器的各种功能"""
    print("🧪 测试分阶段训练GIF生成器")
    print("=" * 50)
    
    # 测试1: 自动模式
    print("\n📋 测试1: 自动模式")
    print("命令: python staged_gif_generator.py --auto --steps 30 --fps 8")
    
    try:
        import subprocess
        result = subprocess.run([
            sys.executable, 'staged_gif_generator.py', 
            '--auto', '--steps', '30', '--fps', '8'
        ], capture_output=True, text=True, timeout=60)
        
        print("✅ 自动模式测试完成")
        print(f"返回码: {result.returncode}")
        if result.stdout:
            print("输出:")
            print(result.stdout[-500:])  # 显示最后500字符
        if result.stderr:
            print("错误:")
            print(result.stderr[-500:])
            
    except subprocess.TimeoutExpired:
        print("⚠️ 自动模式测试超时")
    except Exception as e:
        print(f"❌ 自动模式测试失败: {e}")
    
    # 测试2: 检查文件存在性
    print("\n📋 测试2: 检查训练结果文件")
    
    # 查找训练目录
    import glob
    training_dirs = glob.glob('staged_training_*')
    print(f"找到训练目录: {len(training_dirs)} 个")
    
    for i, dir_path in enumerate(training_dirs[:3]):  # 只显示前3个
        print(f"  {i+1}. {dir_path}")
        
        # 检查模型文件
        model_files = glob.glob(os.path.join(dir_path, 'stage_*_model.pth'))
        print(f"     模型文件: {len(model_files)} 个")
        
        for model_file in model_files:
            file_size = os.path.getsize(model_file) / (1024*1024)  # MB
            print(f"       • {os.path.basename(model_file)} ({file_size:.1f} MB)")
    
    # 测试3: 检查生成的GIF文件
    print("\n📋 测试3: 检查生成的GIF文件")
    
    gif_files = glob.glob('staged_navigation_*.gif')
    print(f"找到GIF文件: {len(gif_files)} 个")
    
    for gif_file in gif_files:
        file_size = os.path.getsize(gif_file) / (1024*1024)  # MB
        creation_time = os.path.getctime(gif_file)
        import datetime
        creation_str = datetime.datetime.fromtimestamp(creation_time).strftime('%Y-%m-%d %H:%M:%S')
        print(f"  • {gif_file} ({file_size:.1f} MB, 创建于 {creation_str})")
    
    # 测试4: 手动模式（如果有训练结果）
    if training_dirs and len(glob.glob(os.path.join(training_dirs[0], 'stage_*_model.pth'))) > 0:
        print("\n📋 测试4: 手动模式")
        latest_dir = max(training_dirs, key=lambda x: os.path.getctime(x))
        model_files = glob.glob(os.path.join(latest_dir, 'stage_*_model.pth'))
        
        if model_files:
            test_model = model_files[0]  # 使用第一个模型
            print(f"使用模型: {test_model}")
            print("命令: python staged_gif_generator.py --model ... --environment stage1_simple --steps 20")
            
            try:
                result = subprocess.run([
                    sys.executable, 'staged_gif_generator.py',
                    '--model', test_model,
                    '--environment', 'stage1_simple',
                    '--steps', '20', '--fps', '8'
                ], capture_output=True, text=True, timeout=45)
                
                print("✅ 手动模式测试完成")
                print(f"返回码: {result.returncode}")
                if result.stdout:
                    print("输出:")
                    print(result.stdout[-300:])  # 显示最后300字符
                if result.stderr:
                    print("错误:")
                    print(result.stderr[-300:])
                    
            except subprocess.TimeoutExpired:
                print("⚠️ 手动模式测试超时")
            except Exception as e:
                print(f"❌ 手动模式测试失败: {e}")
    
    print("\n🎉 GIF生成器测试完成!")

if __name__ == "__main__":
    test_gif_generator()
