#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建文本格式的图表来可视化训练结果
"""

import csv
import json
import numpy as np
import os
from datetime import datetime

def load_csv_data():
    """从CSV文件加载数据"""
    csv_file = "enhanced_training_20250718_022622/simplified_reward_training_rewards.csv"
    
    episode_rewards = []
    episode_steps = []
    episode_results = []
    
    with open(csv_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            episode_rewards.append(float(row['Episode_Reward']))
            episode_steps.append(int(row['Steps']))
            episode_results.append(row['Result'])
    
    return episode_rewards, episode_steps, episode_results

def create_ascii_histogram(data, title, bins=20, width=60):
    """创建ASCII直方图"""
    hist, bin_edges = np.histogram(data, bins=bins)
    max_count = max(hist)
    
    result = [f"\n{title}"]
    result.append("=" * len(title))
    
    for i in range(len(hist)):
        bin_start = bin_edges[i]
        bin_end = bin_edges[i + 1]
        count = hist[i]
        
        # 计算条形长度
        bar_length = int((count / max_count) * width) if max_count > 0 else 0
        bar = "█" * bar_length
        
        result.append(f"{bin_start:8.1f}-{bin_end:8.1f} |{bar:<{width}} {count:3d}")
    
    return "\n".join(result)

def create_ascii_line_chart(data, title, width=80, height=20):
    """创建ASCII折线图"""
    if not data:
        return f"\n{title}\n" + "=" * len(title) + "\n无数据"
    
    min_val = min(data)
    max_val = max(data)
    val_range = max_val - min_val if max_val != min_val else 1
    
    result = [f"\n{title}"]
    result.append("=" * len(title))
    result.append(f"范围: {min_val:.1f} 到 {max_val:.1f}")
    result.append("")
    
    # 创建图表网格
    chart = [[' ' for _ in range(width)] for _ in range(height)]
    
    # 绘制数据点
    for i, value in enumerate(data):
        if i >= width:
            break
        
        # 计算y坐标（从底部开始）
        y = int((value - min_val) / val_range * (height - 1))
        y = height - 1 - y  # 翻转y轴
        
        if 0 <= y < height:
            chart[y][i] = '●'
    
    # 添加y轴标签和图表
    for i, row in enumerate(chart):
        y_val = max_val - (i / (height - 1)) * val_range
        result.append(f"{y_val:8.1f} |{''.join(row)}")
    
    # 添加x轴
    x_axis = "         +" + "-" * width
    result.append(x_axis)
    
    # 添加x轴标签
    x_labels = "         "
    for i in range(0, width, 10):
        x_labels += f"{i:<10}"
    result.append(x_labels)
    
    return "\n".join(result)

def create_phase_comparison_chart():
    """创建阶段对比图表"""
    episode_rewards, episode_steps, episode_results = load_csv_data()
    
    # 分割数据
    random_episodes = 200
    phase1_rewards = episode_rewards[:random_episodes]
    phase2_rewards = episode_rewards[random_episodes:]
    
    phase1_steps = episode_steps[:random_episodes]
    phase2_steps = episode_steps[random_episodes:]
    
    # 计算统计数据
    phase1_success = sum(1 for i in range(random_episodes) if episode_results[i] == 'success')
    phase2_success = sum(1 for i in range(random_episodes, len(episode_results)) if episode_results[i] == 'success')
    
    phase1_timeout = sum(1 for i in range(random_episodes) if episode_results[i] == 'timeout')
    phase2_timeout = sum(1 for i in range(random_episodes, len(episode_results)) if episode_results[i] == 'timeout')
    
    result = []
    
    # 标题
    result.append("🎯 训练阶段对比分析")
    result.append("=" * 50)
    
    # 基本统计对比
    result.append("\n📊 基本统计对比:")
    result.append("-" * 30)
    result.append(f"{'指标':<15} {'第一阶段':<12} {'第二阶段':<12} {'变化':<10}")
    result.append("-" * 50)
    result.append(f"{'平均奖励':<15} {np.mean(phase1_rewards):<12.1f} {np.mean(phase2_rewards):<12.1f} {np.mean(phase2_rewards) - np.mean(phase1_rewards):+.1f}")
    result.append(f"{'奖励标准差':<15} {np.std(phase1_rewards):<12.1f} {np.std(phase2_rewards):<12.1f} {np.std(phase2_rewards) - np.std(phase1_rewards):+.1f}")
    result.append(f"{'平均步数':<15} {np.mean(phase1_steps):<12.1f} {np.mean(phase2_steps):<12.1f} {np.mean(phase2_steps) - np.mean(phase1_steps):+.1f}")
    result.append(f"{'成功率':<15} {phase1_success/200:<12.3f} {phase2_success/100:<12.3f} {phase2_success/100 - phase1_success/200:+.3f}")
    result.append(f"{'超时episodes':<15} {phase1_timeout:<12d} {phase2_timeout:<12d} {phase2_timeout - phase1_timeout:+d}")
    
    # 成功率条形图
    result.append("\n📈 成功率对比:")
    result.append("-" * 30)
    phase1_rate = phase1_success / 200
    phase2_rate = phase2_success / 100
    
    bar1_length = int(phase1_rate * 40)
    bar2_length = int(phase2_rate * 40)
    
    result.append(f"第一阶段 |{'█' * bar1_length:<40} {phase1_rate:.3f}")
    result.append(f"第二阶段 |{'█' * bar2_length:<40} {phase2_rate:.3f}")
    
    # 奖励分布对比
    result.append("\n📊 奖励分布对比:")
    result.append("-" * 30)
    
    # 创建奖励范围统计
    ranges = [(-2200, -2000), (-2000, -1500), (-1500, -1000), (-1000, -500), (-500, -300)]
    range_labels = ["<-2000", "-2000~-1500", "-1500~-1000", "-1000~-500", "-500~-300"]
    
    result.append(f"{'奖励范围':<12} {'第一阶段':<10} {'第二阶段':<10}")
    result.append("-" * 35)
    
    for i, (min_r, max_r) in enumerate(ranges):
        count1 = sum(1 for r in phase1_rewards if min_r <= r < max_r)
        count2 = sum(1 for r in phase2_rewards if min_r <= r < max_r)
        result.append(f"{range_labels[i]:<12} {count1:<10d} {count2:<10d}")
    
    return "\n".join(result)

def create_trend_analysis():
    """创建趋势分析"""
    episode_rewards, episode_steps, episode_results = load_csv_data()
    
    result = []
    result.append("\n🔍 训练趋势分析")
    result.append("=" * 30)
    
    # 分段分析（每50个episodes）
    segment_size = 50
    segments = len(episode_rewards) // segment_size
    
    result.append(f"\n📈 分段趋势分析 (每{segment_size}个episodes):")
    result.append("-" * 50)
    result.append(f"{'段数':<8} {'Episodes':<12} {'平均奖励':<12} {'成功率':<10} {'趋势':<10}")
    result.append("-" * 50)
    
    for i in range(segments):
        start_idx = i * segment_size
        end_idx = min((i + 1) * segment_size, len(episode_rewards))
        
        segment_rewards = episode_rewards[start_idx:end_idx]
        segment_results = episode_results[start_idx:end_idx]
        
        avg_reward = np.mean(segment_rewards)
        success_rate = sum(1 for r in segment_results if r == 'success') / len(segment_results)
        
        # 计算趋势
        if len(segment_rewards) > 1:
            trend = np.polyfit(range(len(segment_rewards)), segment_rewards, 1)[0]
            trend_str = f"{trend:+.1f}"
        else:
            trend_str = "N/A"
        
        episodes_range = f"{start_idx+1}-{end_idx}"
        result.append(f"{i+1:<8} {episodes_range:<12} {avg_reward:<12.1f} {success_rate:<10.3f} {trend_str:<10}")
    
    # 移动平均分析
    result.append(f"\n📊 移动平均分析 (窗口大小: 20):")
    result.append("-" * 40)
    
    window_size = 20
    moving_averages = []
    
    for i in range(window_size, len(episode_rewards) + 1):
        window_rewards = episode_rewards[i-window_size:i]
        moving_averages.append(np.mean(window_rewards))
    
    # 显示关键点的移动平均
    key_points = [20, 50, 100, 150, 200, 250, 300]
    for point in key_points:
        if point <= len(moving_averages):
            result.append(f"Episode {point:3d}: {moving_averages[point-1]:.1f}")
    
    return "\n".join(result)

def main():
    """主函数"""
    print("🎯 创建文本格式的训练图表")
    print("=" * 40)
    
    try:
        episode_rewards, episode_steps, episode_results = load_csv_data()
        print(f"✅ 数据加载成功: {len(episode_rewards)} episodes")
        
        # 创建输出文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"training_text_charts_{timestamp}.txt"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            # 写入标题
            f.write("🎯 增强型简化奖励函数训练 - 文本图表分析\n")
            f.write("=" * 60 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            
            # 奖励分布直方图
            f.write(create_ascii_histogram(episode_rewards, "📊 Episode奖励分布直方图"))
            
            # 步数分布直方图
            f.write(create_ascii_histogram(episode_steps, "📊 Episode步数分布直方图"))
            
            # 奖励趋势折线图（前100个episodes）
            f.write(create_ascii_line_chart(episode_rewards[:100], "📈 前100个Episodes奖励趋势"))
            
            # 奖励趋势折线图（后100个episodes）
            f.write(create_ascii_line_chart(episode_rewards[-100:], "📈 后100个Episodes奖励趋势"))
            
            # 阶段对比分析
            f.write(create_phase_comparison_chart())
            
            # 趋势分析
            f.write(create_trend_analysis())
            
            f.write(f"\n\n📅 报告生成完成: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        print(f"📊 文本图表已保存: {output_file}")
        
        # 同时在控制台显示阶段对比
        print(create_phase_comparison_chart())
        
    except Exception as e:
        print(f"❌ 图表生成失败: {e}")

if __name__ == "__main__":
    main()
