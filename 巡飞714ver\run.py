"""
巡飞弹系统一键运行脚本
提供简单的命令行界面来运行训练和测试
"""

import os
import sys
import subprocess
import argparse

def print_banner():
    """打印系统横幅"""
    print("🚁" + "="*58 + "🚁")
    print("    巡飞弹六自由度分阶段训练系统")
    print("    Loitering Munition 6-DOF Staged Training System")
    print("🚁" + "="*58 + "🚁")
    print()
    print("📋 系统特性:")
    print("   ✅ 六自由度巡飞弹运动学模型")
    print("   ✅ 三项奖励函数（距离+效率+安全）")
    print("   ✅ 分阶段训练策略（简单→复杂）")
    print("   ✅ TD3深度强化学习算法")
    print("   ✅ DWA安全约束保证")
    print()

def check_dependencies():
    """检查依赖库"""
    print("🔍 检查系统依赖...")
    
    required_packages = ['numpy', 'torch', 'matplotlib']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ 缺少依赖库: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖库已安装")
    return True

def run_quick_test():
    """运行快速测试"""
    print("\n⚡ 运行快速系统测试...")
    try:
        result = subprocess.run([sys.executable, 'test_system.py', '--mode', 'quick'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 快速测试通过")
            print(result.stdout)
            return True
        else:
            print("❌ 快速测试失败")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        return False

def run_training():
    """运行训练"""
    print("\n🚀 开始巡飞弹分阶段训练...")
    print("⏰ 预计训练时间: 2-4小时")
    print("💾 训练结果将保存到 loitering_munition_training_XXXXXX/ 目录")
    
    confirm = input("\n是否继续训练? (y/N): ")
    if confirm.lower() != 'y':
        print("❌ 训练已取消")
        return False
    
    try:
        # 实时显示训练输出
        process = subprocess.Popen([sys.executable, 'train_loitering_munition.py'],
                                 stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                                 universal_newlines=True, bufsize=1)
        
        print("\n📊 训练进度:")
        print("-" * 60)
        
        for line in process.stdout:
            print(line.rstrip())
        
        process.wait()
        
        if process.returncode == 0:
            print("\n🎉 训练完成!")
            return True
        else:
            print("\n❌ 训练失败")
            return False
            
    except Exception as e:
        print(f"❌ 训练执行失败: {e}")
        return False

def run_test(model_dir=None):
    """运行测试"""
    if model_dir is None:
        # 查找最新的训练目录
        dirs = [d for d in os.listdir('.') if d.startswith('loitering_munition_training_')]
        if not dirs:
            print("❌ 未找到训练结果目录")
            print("请先运行训练或指定模型目录")
            return False
        
        model_dir = sorted(dirs)[-1]  # 选择最新的
        print(f"📁 使用训练目录: {model_dir}")
    
    print(f"\n🧪 测试训练模型...")
    
    try:
        result = subprocess.run([sys.executable, 'test_system.py', '--mode', 'all', 
                               '--model-dir', model_dir], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 测试完成")
            print(result.stdout)
            return True
        else:
            print("❌ 测试失败")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        return False

def show_help():
    """显示帮助信息"""
    print("📖 使用说明:")
    print()
    print("🔧 基本命令:")
    print("   python run.py --mode check     # 检查系统依赖")
    print("   python run.py --mode test      # 快速功能测试")
    print("   python run.py --mode train     # 执行完整训练")
    print("   python run.py --mode eval      # 测试训练结果")
    print("   python run.py --mode all       # 执行完整流程")
    print()
    print("📊 高级命令:")
    print("   python train_loitering_munition.py    # 直接训练")
    print("   python test_system.py --mode quick    # 直接测试")
    print()
    print("📁 输出文件:")
    print("   loitering_munition_training_XXXXXX/   # 训练结果目录")
    print("   ├── stage1_model.pth                  # 阶段1模型")
    print("   ├── stage2_model.pth                  # 阶段2模型")
    print("   ├── stage3_model.pth                  # 阶段3模型")
    print("   ├── training_results.json             # 训练数据")
    print("   └── training_report.txt               # 训练报告")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='巡飞弹系统运行脚本')
    parser.add_argument('--mode', choices=['check', 'test', 'train', 'eval', 'all', 'help'],
                       default='help', help='运行模式')
    parser.add_argument('--model-dir', type=str, help='模型目录路径（用于测试）')
    
    args = parser.parse_args()
    
    print_banner()
    
    if args.mode == 'help':
        show_help()
    
    elif args.mode == 'check':
        check_dependencies()
    
    elif args.mode == 'test':
        if check_dependencies():
            run_quick_test()
    
    elif args.mode == 'train':
        if check_dependencies():
            if run_quick_test():
                run_training()
            else:
                print("❌ 快速测试失败，请检查系统配置")
    
    elif args.mode == 'eval':
        if check_dependencies():
            run_test(args.model_dir)
    
    elif args.mode == 'all':
        print("🔄 执行完整流程...")
        
        # 1. 检查依赖
        if not check_dependencies():
            return
        
        # 2. 快速测试
        if not run_quick_test():
            print("❌ 快速测试失败，停止执行")
            return
        
        # 3. 训练
        if not run_training():
            print("❌ 训练失败，停止执行")
            return
        
        # 4. 测试
        if not run_test():
            print("❌ 测试失败")
            return
        
        print("\n🎉 完整流程执行成功!")
        print("📊 系统已完成训练和验证，可以投入使用。")

if __name__ == "__main__":
    main()
