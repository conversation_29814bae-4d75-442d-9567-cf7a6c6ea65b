"""
环境配置文件 - 融合版本
定义不同训练阶段的环境配置和训练参数
"""

# 环境配置字典
ENVIRONMENT_CONFIGS = {
    # 阶段1：简单环境配置 - 基于路径的挑战性障碍物布局
    "stage1_simple": {
        "static_obstacle_count": (6, 8),  # 大幅增加数量，确保稳定阻挡
        "enable_dynamic_obstacles": False,
        "dynamic_obstacle_count": (0, 0),
        "use_complex_generation": False,
        "description": "阶段1：路径阻挡型环境，6-8个大型静态障碍物(150-220m半径)"
    },

    # 阶段2：复杂静态环境配置
    "stage2_complex": {
        "static_obstacle_count": (12, 15),  # 大幅增加数量和密度
        "enable_dynamic_obstacles": False,
        "dynamic_obstacle_count": (0, 0),
        "use_complex_generation": True,
        "description": "阶段2：高密度静态环境，8-12个大型静态障碍物"
    },

    # 阶段3：动态环境配置
    "stage3_dynamic": {
        "static_obstacle_count": (8, 10),  # 大幅增加静态障碍物基数
        "enable_dynamic_obstacles": True,
        "dynamic_obstacle_count": (4, 6),  # 增加动态障碍物数量
        "use_complex_generation": True,
        "description": "阶段3：高密度动态环境，7-10个静态 + 4-6个动态大型障碍物"
    },
    
    # 测试环境配置
    "test_simple": {
        "static_obstacle_count": (2, 4),
        "enable_dynamic_obstacles": False,
        "dynamic_obstacle_count": (0, 0),
        "use_complex_generation": False,
        "description": "测试环境：简单配置"
    },
    
    "test_complex": {
        "static_obstacle_count": (10, 20),
        "enable_dynamic_obstacles": True,
        "dynamic_obstacle_count": (3, 6),
        "use_complex_generation": True,
        "description": "测试环境：复杂配置"
    }
}

# 训练阶段配置 - 每个阶段分为随机+固定两个子阶段
TRAINING_STAGES = {
    "stage1_simple": {
        "environment": "stage1_simple",
        "random_episodes": 200,     # 增加随机场景探索训练
        "fixed_episodes": 150,      # 增加固定场景强化训练
        "total_episodes": 350,
        "description": "阶段1：路径阻挡型环境基础训练"
    },

    "stage2_complex": {
        "environment": "stage2_complex",
        "random_episodes": 250,     # 增加随机场景探索训练
        "fixed_episodes": 200,      # 增加固定场景强化训练
        "total_episodes": 450,
        "description": "阶段2：高密度静态环境训练"
    },

    "stage3_dynamic": {
        "environment": "stage3_dynamic",
        "random_episodes": 200,     # 增加随机场景探索训练
        "fixed_episodes": 150,      # 增加固定场景强化训练
        "total_episodes": 350,
        "description": "阶段3：高密度动态环境适应训练"
    }
}

# TD3网络配置
TD3_CONFIG = {
    "state_dim": 15,        # 观测维度
    "action_dim": 3,        # 动作维度 [a_T, a_N, μ]
    "max_action": 1.0,      # 最大动作值（归一化后）
    "lr": 3e-4,             # 学习率
    "batch_size": 256,      # 批次大小
    "gamma": 0.99,          # 折扣因子
    "tau": 0.005,           # 软更新参数
    "policy_noise": 0.2,    # 策略噪声
    "noise_clip": 0.5,      # 噪声裁剪
    "policy_freq": 2,       # 策略更新频率
    "buffer_size": 1000000, # 经验回放缓冲区大小
    "hidden_dim": 256       # 隐藏层维度
}

# 巡飞弹物理参数配置
LOITERING_MUNITION_CONFIG = {
    "bounds": [2000, 2000, 2000], # 环境边界 [x_max, y_max, z_max] - 等比放大的立方体
    "dt": 0.1,                    # 时间步长
    "max_steps": 2000,            # 最大步数
    
    # 运动约束参数
    "V_min": 15.0,                # 最小速度（失速速度）
    "V_max": 60.0,                # 最大速度
    "V_cruise": 25.0,             # 巡航速度
    "a_T_max": 8.0,               # 最大切向加速度
    "a_N_max": 39.24,             # 最大法向加速度（4g）
    "gamma_max": 1.047,           # 最大航迹倾斜角（60°）
    "d_safe": 5.0,                # 最小安全距离
    
    # 物理常数
    "g": 9.81                     # 重力加速度
}

# DWA控制器配置
DWA_CONFIG = {
    "predict_time": 3.0,          # 预测时间窗口
    "min_safe_distance": 5.0,     # 最小安全距离
    
    # 控制输入离散化参数
    "a_T_resolution": 1.5,        # 切向加速度分辨率
    "a_N_resolution": 6.0,        # 法向加速度分辨率
    "mu_resolution": 0.15,        # 倾斜角分辨率
    
    # 评价函数权重
    "alpha": 0.4,                 # 目标方向权重
    "beta": 0.2,                  # 速度权重
    "gamma": 0.3,                 # 距离权重
    "delta": 0.1                  # 障碍物权重
}

# 训练配置
TRAINING_CONFIG = {
    "seed": 42,                   # 随机种子
    "visualization_interval": 10, # 可视化间隔（每20个episode生成一次轨迹图）
    "save_interval": 50,          # 模型保存间隔
    "log_interval": 1,           # 日志输出间隔
    "eval_interval": 100,         # 评估间隔
    "eval_episodes": 1,          # 评估回合数

    # TD3优化配置
    "exploration_noise": 0.1,      # 探索噪声（降低，因为DWA已保证安全）
}

def get_environment_config(env_name):
    """获取环境配置"""
    if env_name not in ENVIRONMENT_CONFIGS:
        raise ValueError(f"未知的环境配置: {env_name}")
    return ENVIRONMENT_CONFIGS[env_name]

def get_training_stage_config(stage_name):
    """获取训练阶段配置"""
    if stage_name not in TRAINING_STAGES:
        raise ValueError(f"未知的训练阶段: {stage_name}")
    return TRAINING_STAGES[stage_name]

def get_td3_config():
    """获取TD3配置"""
    return TD3_CONFIG.copy()

def get_loitering_munition_config():
    """获取巡飞弹配置"""
    return LOITERING_MUNITION_CONFIG.copy()

def get_dwa_config():
    """获取DWA配置"""
    return DWA_CONFIG.copy()

def get_training_config():
    """获取训练配置"""
    return TRAINING_CONFIG.copy()

def print_config_summary():
    """打印配置摘要"""
    print("🔧 配置摘要")
    print("=" * 50)
    
    print("\n📊 训练阶段:")
    for stage_name, config in TRAINING_STAGES.items():
        print(f"  {stage_name}: {config['description']}")
        print(f"    随机训练: {config['random_episodes']} episodes")
        print(f"    固定训练: {config['fixed_episodes']} episodes")
        print(f"    总计: {config['total_episodes']} episodes")
    
    print("\n🌍 环境配置:")
    for env_name, config in ENVIRONMENT_CONFIGS.items():
        print(f"  {env_name}: {config['description']}")
    
    print(f"\n🚁 巡飞弹参数:")
    lm_config = LOITERING_MUNITION_CONFIG
    print(f"  环境边界: {lm_config['bounds']}")
    print(f"  速度范围: {lm_config['V_min']}-{lm_config['V_max']} m/s")
    print(f"  最大加速度: 切向{lm_config['a_T_max']}, 法向{lm_config['a_N_max']} m/s²")
    
    print(f"\n🧠 TD3网络:")
    td3_config = TD3_CONFIG
    print(f"  状态维度: {td3_config['state_dim']}")
    print(f"  动作维度: {td3_config['action_dim']}")
    print(f"  学习率: {td3_config['lr']}")
    print(f"  批次大小: {td3_config['batch_size']}")

if __name__ == "__main__":
    print_config_summary()
