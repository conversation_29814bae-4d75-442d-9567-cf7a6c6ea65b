"""
环境配置文件 - 简化ver1分阶段训练
支持三个阶段的环境复杂度配置：简单-复杂-动态
每个阶段内部支持固定+随机的训练方式
"""

import numpy as np
import random

# 环境复杂度配置
ENVIRONMENT_CONFIGS = {
    # 阶段1：简单环境（原始简化版本）
    "stage1_simple": {
        "enable_dynamic_obstacles": False,
        "static_obstacle_count": (3, 5),
        "use_complex_generation": False,
        "obstacle_radius_range": (4, 8),
        "predefined_obstacles": [
            [30, 30, 30],
            [50, 20, 40], 
            [40, 60, 50],
            [60, 40, 30],
            [70, 70, 60]
        ],
        "description": "阶段1：简单环境，3-5个预定义静态障碍物"
    },
    
    # 阶段2：复杂静态环境
    "stage2_complex": {
        "enable_dynamic_obstacles": False,
        "static_obstacle_count": (8, 12),
        "use_complex_generation": True,
        "obstacle_radius_range": (3, 7),
        "core_obstacles": [
            {'center': [35, 35, 35], 'radius': 6},
            {'center': [45, 25, 45], 'radius': 5},
            {'center': [25, 45, 25], 'radius': 5},
            {'center': [55, 35, 35], 'radius': 4},
            {'center': [35, 55, 45], 'radius': 4},
            {'center': [45, 45, 25], 'radius': 4},
        ],
        "corridor_obstacles": [
            {'center': [20, 50, 50], 'radius': 3},
            {'center': [30, 50, 50], 'radius': 3},
            {'center': [50, 20, 60], 'radius': 3},
            {'center': [50, 30, 60], 'radius': 3},
            {'center': [60, 50, 20], 'radius': 3},
            {'center': [70, 50, 30], 'radius': 3},
        ],
        "boundary_obstacles": [
            {'center': [15, 15, 50], 'radius': 4},
            {'center': [85, 15, 50], 'radius': 4},
            {'center': [15, 85, 50], 'radius': 4},
            {'center': [85, 85, 50], 'radius': 4},
        ],
        "description": "阶段2：复杂静态环境，8-12个多样化静态障碍物"
    },
    
    # 阶段3：动态环境
    "stage3_dynamic": {
        "enable_dynamic_obstacles": True,
        "static_obstacle_count": (6, 10),
        "dynamic_obstacle_count": (2, 4),
        "use_complex_generation": True,
        "obstacle_radius_range": (3, 6),
        "dynamic_motion_types": ['linear', 'circular', 'oscillating'],
        "dynamic_speed_range": (0.5, 2.0),
        # 为动态环境定义基础静态障碍物
        "core_obstacles": [
            {'center': [35, 35, 35], 'radius': 5},
            {'center': [45, 25, 45], 'radius': 4},
            {'center': [25, 45, 25], 'radius': 4},
            {'center': [55, 35, 35], 'radius': 4},
        ],
        "corridor_obstacles": [
            {'center': [20, 50, 50], 'radius': 3},
            {'center': [30, 50, 50], 'radius': 3},
            {'center': [50, 20, 60], 'radius': 3},
            {'center': [50, 30, 60], 'radius': 3},
        ],
        "boundary_obstacles": [
            {'center': [15, 15, 50], 'radius': 3},
            {'center': [85, 15, 50], 'radius': 3},
        ],
        "description": "阶段3：动态环境，6-10个静态 + 2-4个动态障碍物"
    }
}

# 训练阶段配置 - 每个阶段分为随机+固定两个子阶段（先随机探索，再固定强化）
TRAINING_STAGES = {
    "stage1_simple": {
        "environment": "stage1_simple",
        "random_episodes": 150,     # 随机场景探索训练
        "fixed_episodes": 100,      # 固定场景强化训练
        "total_episodes": 250,
        "description": "阶段1：简单环境基础训练"
    },

    "stage2_complex": {
        "environment": "stage2_complex",
        "random_episodes": 200,     # 随机场景探索训练
        "fixed_episodes": 150,      # 固定场景强化训练
        "total_episodes": 350,
        "description": "阶段2：复杂静态环境训练"
    },

    "stage3_dynamic": {
        "environment": "stage3_dynamic",
        "random_episodes": 150,     # 随机场景探索训练
        "fixed_episodes": 100,      # 固定场景强化训练
        "total_episodes": 250,
        "description": "阶段3：动态环境适应训练"
    }
}

# 测试配置
TEST_CONFIGS = {
    "comprehensive": {
        "environments": ["stage1_simple", "stage2_complex", "stage3_dynamic"],
        "episodes_per_env": 10,
        "description": "全面测试：在所有阶段环境中评估性能"
    },
    
    "progressive": {
        "environments": ["stage2_complex", "stage3_dynamic"],
        "episodes_per_env": 15,
        "description": "渐进测试：从复杂静态到动态环境"
    }
}

def get_environment_config(config_name):
    """获取环境配置"""
    if config_name not in ENVIRONMENT_CONFIGS:
        raise ValueError(f"未知的环境配置: {config_name}")
    return ENVIRONMENT_CONFIGS[config_name]

def get_training_stage_config(stage_name):
    """获取训练阶段配置"""
    if stage_name not in TRAINING_STAGES:
        raise ValueError(f"未知的训练阶段: {stage_name}")
    return TRAINING_STAGES[stage_name]

def get_test_config(test_name):
    """获取测试配置"""
    if test_name not in TEST_CONFIGS:
        raise ValueError(f"未知的测试配置: {test_name}")
    return TEST_CONFIGS[test_name]

def generate_obstacles_for_config(env_config, seed=None):
    """根据配置生成障碍物"""
    if seed is not None:
        random.seed(seed)
        np.random.seed(seed)
    
    obstacles = []
    
    if not env_config.get("use_complex_generation", False):
        # 简单生成方式（阶段1）
        predefined = env_config.get("predefined_obstacles", [])
        count_range = env_config.get("static_obstacle_count", (3, 5))
        radius_range = env_config.get("obstacle_radius_range", (4, 8))
        
        num_obstacles = random.randint(*count_range)
        for i in range(min(num_obstacles, len(predefined))):
            center = np.array(predefined[i], dtype=np.float64)
            radius = random.uniform(*radius_range)
            obstacles.append({'center': center, 'radius': radius})
    
    else:
        # 复杂生成方式（阶段2和3）
        all_obstacles = []
        
        # 核心障碍物
        if "core_obstacles" in env_config:
            all_obstacles.extend(env_config["core_obstacles"])
        
        # 通道障碍物
        if "corridor_obstacles" in env_config:
            all_obstacles.extend(env_config["corridor_obstacles"])
        
        # 边界障碍物
        if "boundary_obstacles" in env_config:
            all_obstacles.extend(env_config["boundary_obstacles"])
        
        # 随机障碍物
        count_range = env_config.get("static_obstacle_count", (8, 12))
        num_random = random.randint(2, 4)  # 额外的随机障碍物
        
        for _ in range(num_random):
            # 避免在起点和终点附近生成障碍物
            while True:
                center = [
                    random.uniform(20, 80),
                    random.uniform(20, 80), 
                    random.uniform(20, 80)
                ]
                # 检查与起点终点的距离
                start = np.array([10.0, 10.0, 10.0])
                goal = np.array([80.0, 80.0, 80.0])
                start_dist = np.linalg.norm(np.array(center) - start)
                goal_dist = np.linalg.norm(np.array(center) - goal)
                if start_dist > 15 and goal_dist > 15:
                    break
            
            radius_range = env_config.get("obstacle_radius_range", (3, 6))
            radius = random.uniform(*radius_range)
            all_obstacles.append({'center': center, 'radius': radius})
        
        # 随机选择障碍物
        num_selected = random.randint(*count_range)
        selected_obstacles = random.sample(all_obstacles, min(num_selected, len(all_obstacles)))
        
        # 转换格式
        for obs_config in selected_obstacles:
            center = np.array(obs_config['center'], dtype=np.float64)
            radius = obs_config['radius']
            obstacles.append({'center': center, 'radius': radius})
    
    return obstacles

def generate_dynamic_obstacles_for_config(env_config, seed=None):
    """根据配置生成动态障碍物"""
    if not env_config.get("enable_dynamic_obstacles", False):
        return []
    
    if seed is not None:
        random.seed(seed)
        np.random.seed(seed)
    
    dynamic_obstacles = []
    count_range = env_config.get("dynamic_obstacle_count", (2, 4))
    num_dynamic = random.randint(*count_range)
    
    motion_types = env_config.get("dynamic_motion_types", ['linear', 'circular'])
    speed_range = env_config.get("dynamic_speed_range", (0.5, 2.0))
    
    for _ in range(num_dynamic):
        # 随机生成动态障碍物
        center = [
            random.uniform(30, 70),
            random.uniform(30, 70),
            random.uniform(30, 70)
        ]
        
        motion_type = random.choice(motion_types)
        speed = random.uniform(*speed_range)
        
        # 根据运动类型设置参数
        if motion_type == 'linear':
            direction = np.random.uniform(-1, 1, 3)
            direction = direction / np.linalg.norm(direction)
            motion_params = {
                'direction': direction,
                'speed': speed
            }
        elif motion_type == 'circular':
            motion_params = {
                'center_base': center.copy(),
                'radius': random.uniform(5, 15),
                'angular_speed': speed * 0.1
            }
        else:  # oscillating
            amplitude = [random.uniform(5, 15) for _ in range(3)]
            frequency = [speed * 0.1 * random.uniform(0.5, 2.0) for _ in range(3)]
            motion_params = {
                'center_base': center.copy(),
                'amplitude': amplitude,
                'frequency': frequency,
                'phase': [random.uniform(0, 2*np.pi) for _ in range(3)]
            }
        
        dynamic_obs = {
            'center': np.array(center, dtype=np.float64),
            'radius': random.uniform(3, 6),
            'motion_type': motion_type,
            'motion_params': motion_params,
            'time': 0.0
        }
        
        dynamic_obstacles.append(dynamic_obs)
    
    return dynamic_obstacles

def print_all_configs():
    """打印所有可用配置"""
    print("🌍 可用环境配置:")
    print("=" * 50)
    for name, config in ENVIRONMENT_CONFIGS.items():
        print(f"{name}: {config['description']}")
    
    print("\n🏋️ 训练阶段配置:")
    print("=" * 50)
    for name, config in TRAINING_STAGES.items():
        print(f"{name}: {config['description']}")
        print(f"  • 随机探索: {config['random_episodes']} episodes")
        print(f"  • 固定强化: {config['fixed_episodes']} episodes")
        print(f"  • 总计: {config['total_episodes']} episodes")
    
    print("\n🧪 测试配置:")
    print("=" * 50)
    for name, config in TEST_CONFIGS.items():
        print(f"{name}: {config['description']}")

if __name__ == "__main__":
    print_all_configs()
