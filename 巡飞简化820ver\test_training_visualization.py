"""
测试训练时的可视化效果
验证训练框架生成的图表是否使用了detailed版本的渲染
"""

import numpy as np
import matplotlib.pyplot as plt
from staged_training_framework import LoiteringMunitionStagedTrainer
from loitering_munition_environment import LoiteringMunitionEnvironment
from loitering_munition_dwa import LoiteringMunitionDWA
from environment_config import get_environment_config

def test_training_visualization():
    """测试训练可视化效果"""
    print('🎨 测试训练时的可视化效果...')
    
    # 创建训练器
    trainer = LoiteringMunitionStagedTrainer(
        start_stage=1, 
        end_stage=1, 
        seed=42,
        visualization_interval=1  # 每个episode都生成图表
    )
    
    # 创建环境
    env_config = get_environment_config('stage1_simple')
    env = LoiteringMunitionEnvironment(
        bounds=[2000, 2000, 2000],
        environment_config=env_config,
        reward_type='simplified'
    )
    
    # 创建DWA控制器
    dwa = LoiteringMunitionDWA(dt=0.1)
    
    print(f'环境信息:')
    print(f'  起点: {env.start}')
    print(f'  目标: {env.goal}')
    print(f'  障碍物数量: {len(env.obstacles)}')
    
    # 模拟一个训练episode
    obs = env.reset()
    trajectory = [env.state[:3].copy()]
    
    max_steps = 100
    step = 0
    episode_success = False
    
    print(f'\\n🚀 开始模拟训练episode...')
    
    while step < max_steps:
        # 使用DWA选择动作
        safe_controls = dwa.generate_safe_control_set(env.state, env.obstacles, env.goal, max_actions=5)
        if len(safe_controls) > 0:
            best_control = None
            best_score = -float('inf')
            
            for control in safe_controls:
                score = dwa.evaluate_control(control, env.state, env.goal, env.obstacles)
                if score > best_score:
                    best_score = score
                    best_control = control
            
            action = best_control if best_control is not None else safe_controls[0]
        else:
            action = [0, 0, 0]
        
        # 执行动作
        next_obs, reward, done, info = env.step(action)
        trajectory.append(env.state[:3].copy())
        
        # 检查是否成功
        current_dist = np.linalg.norm(env.state[:3] - env.goal)
        if current_dist <= 50.0:  # 目标半径
            episode_success = True
            print(f'🎯 成功到达目标！步数: {step+1}, 距离: {current_dist:.2f}m')
            break
        
        step += 1
        
        if done:
            break
    
    print(f'Episode完成: {step+1}步, 成功: {episode_success}')
    
    # 使用训练器的可视化函数生成图表
    print(f'\\n🎨 生成训练风格的可视化图表...')
    trainer._generate_3d_trajectory_plot(env, trajectory, 0, episode_success, "Test_Training")
    
    # 同时生成一个对比图，展示两种风格的差异
    generate_comparison_plot(env, trajectory, episode_success)
    
    print(f'\\n✅ 测试完成！')
    print(f'生成的图表:')
    print(f'  - Test_Training_episode_001_3d_trajectory.png (训练风格)')
    print(f'  - visualization_comparison.png (对比图)')

def generate_comparison_plot(env, trajectory, success):
    """生成对比图，展示不同可视化风格"""
    
    fig = plt.figure(figsize=(20, 10))
    
    # 左侧：简单风格
    ax1 = fig.add_subplot(121, projection='3d')
    
    trajectory = np.array(trajectory)
    
    # 简单轨迹
    color = 'blue' if success else 'red'
    ax1.plot(trajectory[:, 0], trajectory[:, 1], trajectory[:, 2], 
             color=color, linewidth=2, alpha=0.8, label='Trajectory')
    
    # 起点和目标
    ax1.scatter(*env.start, color='green', s=100, marker='o', label='Start')
    ax1.scatter(*env.goal, color='red', s=100, marker='*', label='Target')
    
    # 简单障碍物表示
    for i, obs in enumerate(env.obstacles):
        center = obs['center']
        radius = obs['radius']
        ax1.scatter(*center, color='orange', s=50, marker='s', alpha=0.7)
        ax1.text(center[0], center[1], center[2], f'Obs{i+1}', fontsize=8)
    
    ax1.set_xlabel('X (m)')
    ax1.set_ylabel('Y (m)')
    ax1.set_zlabel('Z (m)')
    ax1.set_title('Simple Style (Old)', fontsize=14, fontweight='bold')
    ax1.legend()
    
    # 右侧：详细风格（训练框架使用的）
    ax2 = fig.add_subplot(122, projection='3d')
    
    # 详细轨迹
    ax2.plot(trajectory[:, 0], trajectory[:, 1], trajectory[:, 2], 
             color=color, linewidth=3, alpha=0.8, label='Trajectory')
    
    # 起点和目标
    ax2.scatter(*env.start, color='green', s=200, marker='o', label='Start', 
               edgecolors='black', linewidth=2)
    ax2.scatter(*env.goal, color='red', s=200, marker='*', label='Target', 
               edgecolors='black', linewidth=2)
    ax2.scatter(*trajectory[-1], color=color, s=150, marker='X', label='End', 
               edgecolors='black', linewidth=2)
    
    # 详细障碍物球体
    obstacle_colors = ['orange', 'yellow', 'purple', 'brown', 'pink']
    for i, obs in enumerate(env.obstacles):
        # 高分辨率球体
        u = np.linspace(0, 2 * np.pi, 30)
        v = np.linspace(0, np.pi, 30)
        
        center = obs['center']
        radius = obs['radius']
        x = center[0] + radius * np.outer(np.cos(u), np.sin(v))
        y = center[1] + radius * np.outer(np.sin(u), np.sin(v))
        z = center[2] + radius * np.outer(np.ones(np.size(u)), np.cos(v))
        
        color_obs = obstacle_colors[i % len(obstacle_colors)]
        ax2.plot_surface(x, y, z, alpha=0.4, color=color_obs, 
                        edgecolor='black', linewidth=0.1)
        
        # 标签
        ax2.text(center[0], center[1], center[2] + radius + 20, 
                f'Obs{i+1}\\nr={radius:.0f}m', fontsize=10, ha='center', 
                bbox=dict(boxstyle="round,pad=0.3", facecolor=color_obs, alpha=0.7))
    
    ax2.set_xlabel('X (m)', fontweight='bold')
    ax2.set_ylabel('Y (m)', fontweight='bold')
    ax2.set_zlabel('Z (m)', fontweight='bold')
    ax2.set_title('Detailed Style (Training Framework)', fontsize=14, fontweight='bold')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 设置相同的视角和范围
    for ax in [ax1, ax2]:
        ax.set_xlim(0, env.bounds[0])
        ax.set_ylim(0, env.bounds[1])
        ax.set_zlim(0, env.bounds[2])
        ax.set_box_aspect([env.bounds[0], env.bounds[1], env.bounds[2]])
    
    plt.suptitle('Visualization Style Comparison', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('visualization_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f'✅ 对比图已保存: visualization_comparison.png')

if __name__ == "__main__":
    test_training_visualization()
