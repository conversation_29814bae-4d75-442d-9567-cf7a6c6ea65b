"""
专家数据库构建器
Expert Database Builder

提供多种方式构建高质量的专家数据库：
1. 传统DWA最优解数据库
2. 人工专家演示数据库  
3. 强化学习专家数据库
4. 混合专家数据库
"""

import numpy as np
import pickle
import os
from typing import List, Dict, Tuple
import time
from collections import defaultdict

class ExpertDatabaseBuilder:
    """专家数据库构建器"""
    
    def __init__(self):
        self.database = []
        self.database_stats = defaultdict(int)
        
    def build_dwa_expert_database(self, num_scenarios=1000, save_path="dwa_expert_database.pkl"):
        """
        方案1：基于传统DWA的最优解数据库
        
        优势：
        - DWA解是理论最优的（在其约束下）
        - 数据质量有保证
        - 可以大量生成
        
        创新点：
        - 用DWA的最优解训练神经网络
        - 神经网络学会快速生成DWA级别的解
        - 实现O(n³) → O(1)的效率突破
        """
        print(f"🎯 构建DWA专家数据库，场景数: {num_scenarios}")
        
        dwa_expert_data = []
        
        for scenario_id in range(num_scenarios):
            # 生成多样化的测试场景
            scenario = self._generate_diverse_scenario(scenario_id)
            
            # 使用传统DWA求解最优动作集
            dwa_optimal_actions = self._solve_with_traditional_dwa(scenario)
            
            if dwa_optimal_actions:  # 如果DWA找到了解
                expert_sample = {
                    'scenario_id': scenario_id,
                    'state': scenario['state'],
                    'goal': scenario['goal'],
                    'obstacles': scenario['obstacles'],
                    'expert_actions': dwa_optimal_actions,
                    'expert_type': 'dwa_optimal',
                    'quality_score': self._evaluate_action_quality(scenario, dwa_optimal_actions),
                    'solve_time': scenario.get('solve_time', 0)
                }
                dwa_expert_data.append(expert_sample)
                self.database_stats['dwa_optimal'] += 1
            
            if scenario_id % 100 == 0:
                print(f"  DWA求解进度: {scenario_id}/{num_scenarios}")
        
        # 保存数据库
        with open(save_path, 'wb') as f:
            pickle.dump(dwa_expert_data, f)
        
        print(f"✅ DWA专家数据库构建完成: {len(dwa_expert_data)} 条数据")
        print(f"💾 已保存到: {save_path}")
        
        return dwa_expert_data
    
    def build_human_expert_database(self, save_path="human_expert_database.pkl"):
        """
        方案2：人工专家演示数据库
        
        优势：
        - 真正的专家级决策
        - 包含人类直觉和经验
        - 处理复杂边界情况的能力
        
        实现方式：
        - 交互式场景生成器
        - 专家通过界面选择最优动作
        - 记录专家的决策过程
        """
        print("👨‍💼 构建人工专家演示数据库")
        print("注意：这需要领域专家参与，当前提供模拟版本")
        
        # 模拟专家演示数据
        human_expert_data = []
        
        # 预定义一些经典场景和专家解
        classic_scenarios = self._get_classic_scenarios_with_expert_solutions()
        
        for scenario_id, (scenario, expert_solution) in enumerate(classic_scenarios):
            expert_sample = {
                'scenario_id': scenario_id,
                'state': scenario['state'],
                'goal': scenario['goal'],
                'obstacles': scenario['obstacles'],
                'expert_actions': expert_solution['actions'],
                'expert_type': 'human_expert',
                'expert_reasoning': expert_solution['reasoning'],
                'quality_score': expert_solution['quality'],
                'difficulty_level': scenario['difficulty']
            }
            human_expert_data.append(expert_sample)
            self.database_stats['human_expert'] += 1
        
        # 保存数据库
        with open(save_path, 'wb') as f:
            pickle.dump(human_expert_data, f)
        
        print(f"✅ 人工专家数据库构建完成: {len(human_expert_data)} 条数据")
        return human_expert_data
    
    def build_rl_expert_database(self, num_episodes=5000, save_path="rl_expert_database.pkl"):
        """
        方案3：强化学习专家数据库
        
        优势：
        - 通过大量试错学习到的最优策略
        - 能够处理复杂的长期规划
        - 适应性强
        
        实现方式：
        - 先训练一个强化学习专家
        - 收集专家级agent的决策数据
        - 用这些数据训练动作生成网络
        """
        print(f"🤖 构建强化学习专家数据库，训练episodes: {num_episodes}")
        
        # 这里需要先有一个训练好的RL专家
        # 为了演示，我们模拟一个RL专家的行为
        rl_expert_data = []
        
        print("注意：需要先训练RL专家，当前提供模拟版本")
        
        for episode in range(min(num_episodes, 500)):  # 限制演示数量
            # 生成场景
            scenario = self._generate_diverse_scenario(episode)
            
            # 模拟RL专家的决策过程
            rl_expert_actions = self._simulate_rl_expert_decision(scenario)
            
            expert_sample = {
                'scenario_id': episode,
                'state': scenario['state'],
                'goal': scenario['goal'],
                'obstacles': scenario['obstacles'],
                'expert_actions': rl_expert_actions,
                'expert_type': 'rl_expert',
                'quality_score': self._evaluate_action_quality(scenario, rl_expert_actions),
                'episode_reward': np.random.uniform(80, 100)  # 模拟高奖励
            }
            rl_expert_data.append(expert_sample)
            self.database_stats['rl_expert'] += 1
            
            if episode % 100 == 0:
                print(f"  RL专家数据收集进度: {episode}/{min(num_episodes, 500)}")
        
        # 保存数据库
        with open(save_path, 'wb') as f:
            pickle.dump(rl_expert_data, f)
        
        print(f"✅ RL专家数据库构建完成: {len(rl_expert_data)} 条数据")
        return rl_expert_data
    
    def build_hybrid_expert_database(self, save_path="hybrid_expert_database.pkl"):
        """
        方案4：混合专家数据库
        
        优势：
        - 结合多种专家的优势
        - 数据多样性最高
        - 鲁棒性最强
        """
        print("🔄 构建混合专家数据库")
        
        # 加载或生成各种专家数据
        dwa_data = self.build_dwa_expert_database(num_scenarios=300)
        human_data = self.build_human_expert_database()
        rl_data = self.build_rl_expert_database(num_episodes=200)
        
        # 合并数据库
        hybrid_data = dwa_data + human_data + rl_data
        
        # 数据质量过滤
        high_quality_data = [
            sample for sample in hybrid_data 
            if sample['quality_score'] > 0.7
        ]
        
        # 保存混合数据库
        with open(save_path, 'wb') as f:
            pickle.dump(high_quality_data, f)
        
        print(f"✅ 混合专家数据库构建完成: {len(high_quality_data)} 条高质量数据")
        print(f"📊 数据分布: {dict(self.database_stats)}")
        
        return high_quality_data
    
    def _generate_diverse_scenario(self, scenario_id: int) -> Dict:
        """生成多样化的测试场景"""
        np.random.seed(scenario_id)  # 确保可重现
        
        # 根据scenario_id生成不同难度的场景
        difficulty = scenario_id % 5  # 0-4难度等级
        
        if difficulty == 0:  # 简单场景
            state = np.random.uniform([100, 100, 30, -2, -2, -1], [300, 300, 50, 2, 2, 1])
            goal = np.random.uniform([700, 700, 30], [900, 900, 50])
            num_obs = np.random.randint(1, 3)
        elif difficulty == 1:  # 中等场景
            state = np.random.uniform([100, 100, 20, -3, -3, -1], [400, 400, 60, 3, 3, 1])
            goal = np.random.uniform([600, 600, 20], [900, 900, 60])
            num_obs = np.random.randint(2, 4)
        else:  # 困难场景
            state = np.random.uniform([50, 50, 20, -5, -5, -2], [950, 950, 80, 5, 5, 2])
            goal = np.random.uniform([100, 100, 20], [900, 900, 80])
            num_obs = np.random.randint(3, 6)
        
        # 生成障碍物
        obstacles = []
        for _ in range(num_obs):
            obs_center = np.random.uniform([100, 100, 20], [900, 900, 80])
            obs_radius = np.random.uniform(15, 40)
            obs_velocity = np.random.uniform([-2, -2, -1], [2, 2, 1])
            obstacles.append({
                'center': obs_center,
                'radius': obs_radius,
                'velocity': obs_velocity
            })
        
        return {
            'state': state,
            'goal': goal,
            'obstacles': obstacles,
            'difficulty': difficulty,
            'scenario_id': scenario_id
        }
    
    def _solve_with_traditional_dwa(self, scenario: Dict) -> List[np.ndarray]:
        """使用传统DWA求解最优动作集"""
        # 这里应该调用真正的DWA算法
        # 为了演示，我们模拟DWA的求解过程
        
        state = scenario['state']
        goal = scenario['goal']
        obstacles = scenario['obstacles']
        
        start_time = time.time()
        
        # 模拟DWA的暴力搜索过程
        best_actions = []
        best_score = -float('inf')
        
        # 简化的DWA搜索（实际应该更复杂）
        for vx in np.arange(-3, 3.1, 0.5):
            for vy in np.arange(-3, 3.1, 0.5):
                for vz in np.arange(-2, 2.1, 0.5):
                    action = np.array([vx, vy, vz])
                    
                    # 简单的评价函数
                    goal_direction = goal - state[:3]
                    goal_direction = goal_direction / (np.linalg.norm(goal_direction) + 1e-6)
                    
                    # 目标导向性评分
                    if np.linalg.norm(action) > 0.1:
                        action_direction = action / np.linalg.norm(action)
                        alignment_score = np.dot(action_direction, goal_direction)
                    else:
                        alignment_score = 0
                    
                    # 安全性评分（简化）
                    safety_score = 1.0
                    for obs in obstacles:
                        dist_to_obs = np.linalg.norm(state[:3] - obs['center'])
                        if dist_to_obs < obs['radius'] + 50:
                            safety_score *= 0.5
                    
                    total_score = alignment_score * safety_score
                    
                    if total_score > best_score:
                        best_score = total_score
                        best_actions = [action]
                    elif abs(total_score - best_score) < 0.01:
                        best_actions.append(action)
        
        solve_time = time.time() - start_time
        scenario['solve_time'] = solve_time
        
        # 返回前20个最优动作
        return best_actions[:20] if best_actions else []
    
    def _get_classic_scenarios_with_expert_solutions(self) -> List[Tuple[Dict, Dict]]:
        """获取经典场景和专家解"""
        classic_scenarios = []
        
        # 场景1：简单直线到达
        scenario1 = {
            'state': np.array([100, 100, 50, 0, 0, 0]),
            'goal': np.array([900, 900, 50]),
            'obstacles': [
                {'center': np.array([500, 300, 50]), 'radius': 30, 'velocity': np.array([0, 0, 0])}
            ],
            'difficulty': 1
        }
        solution1 = {
            'actions': [np.array([2.0, 2.0, 0.0]), np.array([2.5, 2.5, 0.0])],
            'reasoning': "直接朝向目标，避开障碍物",
            'quality': 0.9
        }
        classic_scenarios.append((scenario1, solution1))
        
        # 场景2：复杂避障
        scenario2 = {
            'state': np.array([200, 200, 40, 1, 1, 0]),
            'goal': np.array([800, 800, 60]),
            'obstacles': [
                {'center': np.array([400, 400, 50]), 'radius': 40, 'velocity': np.array([0, 0, 0])},
                {'center': np.array([600, 300, 45]), 'radius': 35, 'velocity': np.array([1, 0, 0])}
            ],
            'difficulty': 3
        }
        solution2 = {
            'actions': [np.array([1.5, 3.0, 0.5]), np.array([2.0, 2.0, 0.0])],
            'reasoning': "绕过多个障碍物，选择安全路径",
            'quality': 0.85
        }
        classic_scenarios.append((scenario2, solution2))
        
        return classic_scenarios
    
    def _simulate_rl_expert_decision(self, scenario: Dict) -> List[np.ndarray]:
        """模拟RL专家的决策"""
        # 这里应该调用训练好的RL专家
        # 为了演示，我们模拟一个智能的决策过程
        
        state = scenario['state']
        goal = scenario['goal']
        obstacles = scenario['obstacles']
        
        expert_actions = []
        
        # 模拟RL专家的多步决策
        for i in range(20):
            # 计算目标方向
            goal_direction = goal - state[:3]
            goal_direction = goal_direction / (np.linalg.norm(goal_direction) + 1e-6)
            
            # 计算避障方向
            avoidance_direction = np.zeros(3)
            for obs in obstacles:
                to_obs = obs['center'] - state[:3]
                dist = np.linalg.norm(to_obs)
                if dist < obs['radius'] + 100:
                    avoidance_strength = (obs['radius'] + 50) / (dist + 1e-6)
                    avoidance_direction -= to_obs / (dist + 1e-6) * avoidance_strength
            
            # RL专家的智能组合策略
            if i < 10:
                # 前半部分：平衡目标和避障
                action = 0.6 * goal_direction * 2.5 + 0.4 * avoidance_direction
            else:
                # 后半部分：更多探索
                action = 0.4 * goal_direction * 2.0 + 0.6 * avoidance_direction
                action += np.random.normal(0, 0.3, 3)  # 添加探索噪声
            
            action = np.clip(action, -3.0, 3.0)
            expert_actions.append(action)
        
        return expert_actions
    
    def _evaluate_action_quality(self, scenario: Dict, actions: List[np.ndarray]) -> float:
        """评价动作质量"""
        if not actions:
            return 0.0
        
        state = scenario['state']
        goal = scenario['goal']
        obstacles = scenario['obstacles']
        
        # 计算平均目标导向性
        goal_direction = goal - state[:3]
        goal_direction = goal_direction / (np.linalg.norm(goal_direction) + 1e-6)
        
        alignment_scores = []
        for action in actions:
            if np.linalg.norm(action) > 0.1:
                action_direction = action / np.linalg.norm(action)
                alignment = max(0, np.dot(action_direction, goal_direction))
                alignment_scores.append(alignment)
        
        avg_alignment = np.mean(alignment_scores) if alignment_scores else 0
        
        # 计算安全性评分
        safety_score = 1.0
        for obs in obstacles:
            dist_to_obs = np.linalg.norm(state[:3] - obs['center'])
            if dist_to_obs < obs['radius'] + 30:
                safety_score *= 0.7
        
        # 综合质量评分
        quality_score = 0.7 * avg_alignment + 0.3 * safety_score
        
        return quality_score
    
    def load_expert_database(self, database_path: str) -> List[Dict]:
        """加载专家数据库"""
        if os.path.exists(database_path):
            with open(database_path, 'rb') as f:
                database = pickle.load(f)
            print(f"📂 已加载专家数据库: {len(database)} 条数据")
            return database
        else:
            print(f"❌ 数据库文件不存在: {database_path}")
            return []
    
    def analyze_database(self, database: List[Dict]):
        """分析数据库质量"""
        if not database:
            print("❌ 数据库为空")
            return
        
        print(f"\n📊 数据库分析报告:")
        print(f"  总数据量: {len(database)}")
        
        # 按专家类型分组
        expert_types = defaultdict(int)
        quality_scores = []
        
        for sample in database:
            expert_types[sample['expert_type']] += 1
            quality_scores.append(sample['quality_score'])
        
        print(f"  专家类型分布: {dict(expert_types)}")
        print(f"  平均质量评分: {np.mean(quality_scores):.3f}")
        print(f"  质量评分范围: [{np.min(quality_scores):.3f}, {np.max(quality_scores):.3f}]")
        
        # 难度分布
        if 'difficulty' in database[0]:
            difficulties = [sample.get('difficulty', 0) for sample in database]
            difficulty_dist = defaultdict(int)
            for d in difficulties:
                difficulty_dist[d] += 1
            print(f"  难度分布: {dict(difficulty_dist)}")

def main():
    """主函数：演示专家数据库构建"""
    print("🏗️ 专家数据库构建器演示")
    print("=" * 60)
    
    builder = ExpertDatabaseBuilder()
    
    try:
        # 构建混合专家数据库
        hybrid_database = builder.build_hybrid_expert_database()
        
        # 分析数据库质量
        builder.analyze_database(hybrid_database)
        
        print("\n✅ 专家数据库构建完成!")
        print("💡 使用建议:")
        print("  1. DWA专家数据：适合快速原型和基础训练")
        print("  2. 人工专家数据：适合处理复杂边界情况")
        print("  3. RL专家数据：适合长期规划和适应性学习")
        print("  4. 混合数据库：推荐用于最终训练")
        
    except Exception as e:
        print(f"❌ 构建过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
