#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的图表生成器 - 生成第二阶段和对比图
"""

import pickle
import json
import numpy as np
import os
import sys

def load_training_data():
    """加载训练数据"""
    data_dir = "enhanced_training_20250718_022622"

    try:
        # 加载训练报告
        with open(os.path.join(data_dir, "simplified_reward_training_report.json"), 'r', encoding='utf-8') as f:
            report = json.load(f)

        # 从CSV文件加载详细数据
        import csv
        csv_file = os.path.join(data_dir, "simplified_reward_training_rewards.csv")

        episode_rewards = []
        episode_steps = []
        episode_results = []

        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                episode_rewards.append(float(row['Episode_Reward']))
                episode_steps.append(int(row['Steps']))
                episode_results.append(row['Result'])

        # 构建训练数据字典
        training_data = {
            'episode_rewards': episode_rewards,
            'episode_steps': episode_steps,
            'episode_results': episode_results,
            'success_episodes': [i for i, result in enumerate(episode_results) if result == 'success'],
            'collision_episodes': [i for i, result in enumerate(episode_results) if result == 'collision'],
            'timeout_episodes': [i for i, result in enumerate(episode_results) if result == 'timeout']
        }

        return training_data, report
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return None, None

def analyze_data():
    """分析训练数据并生成统计信息"""
    print("🎯 分析训练数据...")
    
    training_data, report = load_training_data()
    if training_data is None:
        return
    
    print("✅ 数据加载成功")
    
    # 基本统计
    total_episodes = len(training_data['episode_rewards'])
    random_episodes = 200
    
    print(f"📊 训练统计:")
    print(f"  • 总Episodes: {total_episodes}")
    print(f"  • 第一阶段(随机场景): {random_episodes} episodes")
    print(f"  • 第二阶段(固定场景): {total_episodes - random_episodes} episodes")
    
    # 第一阶段统计
    phase1_rewards = training_data['episode_rewards'][:random_episodes]
    phase1_success = [ep for ep in training_data['success_episodes'] if ep < random_episodes]
    phase1_collision = [ep for ep in training_data['collision_episodes'] if ep < random_episodes]
    phase1_timeout = [ep for ep in training_data['timeout_episodes'] if ep < random_episodes]
    
    print(f"\n📈 第一阶段统计:")
    print(f"  • 平均奖励: {np.mean(phase1_rewards):.1f}")
    print(f"  • 奖励范围: {np.min(phase1_rewards):.1f} ~ {np.max(phase1_rewards):.1f}")
    print(f"  • 成功率: {len(phase1_success)/random_episodes:.3f} ({len(phase1_success)}/{random_episodes})")
    print(f"  • 碰撞: {len(phase1_collision)} episodes")
    print(f"  • 超时: {len(phase1_timeout)} episodes")
    
    # 第二阶段统计
    phase2_rewards = training_data['episode_rewards'][random_episodes:]
    phase2_success = [ep for ep in training_data['success_episodes'] if ep >= random_episodes]
    phase2_collision = [ep for ep in training_data['collision_episodes'] if ep >= random_episodes]
    phase2_timeout = [ep for ep in training_data['timeout_episodes'] if ep >= random_episodes]
    
    phase2_episodes_count = len(phase2_rewards)
    
    print(f"\n📈 第二阶段统计:")
    print(f"  • 平均奖励: {np.mean(phase2_rewards):.1f}")
    print(f"  • 奖励范围: {np.min(phase2_rewards):.1f} ~ {np.max(phase2_rewards):.1f}")
    print(f"  • 成功率: {len(phase2_success)/phase2_episodes_count:.3f} ({len(phase2_success)}/{phase2_episodes_count})")
    print(f"  • 碰撞: {len(phase2_collision)} episodes")
    print(f"  • 超时: {len(phase2_timeout)} episodes")
    
    # 对比分析
    print(f"\n🔍 阶段对比:")
    print(f"  • 平均奖励变化: {np.mean(phase2_rewards) - np.mean(phase1_rewards):+.1f}")
    print(f"  • 成功率变化: {len(phase2_success)/phase2_episodes_count - len(phase1_success)/random_episodes:+.3f}")
    
    # 奖励趋势分析
    if len(phase1_rewards) > 1:
        phase1_trend = np.polyfit(range(len(phase1_rewards)), phase1_rewards, 1)[0]
        print(f"  • 第一阶段奖励趋势: {phase1_trend:+.2f}/episode")
    
    if len(phase2_rewards) > 1:
        phase2_trend = np.polyfit(range(len(phase2_rewards)), phase2_rewards, 1)[0]
        print(f"  • 第二阶段奖励趋势: {phase2_trend:+.2f}/episode")
    
    # 生成CSV报告
    generate_csv_report(training_data, random_episodes)
    
    print("\n✅ 数据分析完成！")

def generate_csv_report(training_data, random_episodes):
    """生成CSV格式的详细报告"""
    try:
        import csv
        from datetime import datetime
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"training_analysis_report_{timestamp}.csv"
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            
            # 写入标题
            writer.writerow(['Episode', 'Phase', 'Reward', 'Steps', 'Result', 'Success_Rate'])
            
            # 写入数据
            for i, (reward, steps) in enumerate(zip(training_data['episode_rewards'], training_data['episode_steps'])):
                episode = i
                phase = "Phase1_Random" if i < random_episodes else "Phase2_Fixed"
                
                # 确定结果
                if episode in training_data['success_episodes']:
                    result = "Success"
                elif episode in training_data['collision_episodes']:
                    result = "Collision"
                elif episode in training_data['timeout_episodes']:
                    result = "Timeout"
                else:
                    result = "Unknown"
                
                # 计算累积成功率
                success_count = len([ep for ep in training_data['success_episodes'] if ep <= i])
                success_rate = success_count / (i + 1)
                
                writer.writerow([episode + 1, phase, f"{reward:.1f}", steps, result, f"{success_rate:.3f}"])
        
        print(f"📊 详细分析报告已保存: {filename}")
        
    except Exception as e:
        print(f"⚠️ CSV报告生成失败: {e}")

def generate_text_summary():
    """生成文本格式的训练总结"""
    training_data, report = load_training_data()
    if training_data is None:
        return
    
    try:
        from datetime import datetime
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"training_text_summary_{timestamp}.txt"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("🎯 增强型简化奖励函数训练总结报告\n")
            f.write("=" * 50 + "\n\n")
            
            # 基本信息
            total_episodes = len(training_data['episode_rewards'])
            random_episodes = 200
            
            f.write(f"📋 训练配置:\n")
            f.write(f"  • 总Episodes: {total_episodes}\n")
            f.write(f"  • 第一阶段(随机场景): {random_episodes} episodes\n")
            f.write(f"  • 第二阶段(固定场景): {total_episodes - random_episodes} episodes\n\n")
            
            # 第一阶段详细统计
            phase1_rewards = training_data['episode_rewards'][:random_episodes]
            phase1_steps = training_data['episode_steps'][:random_episodes]
            phase1_success = [ep for ep in training_data['success_episodes'] if ep < random_episodes]
            phase1_collision = [ep for ep in training_data['collision_episodes'] if ep < random_episodes]
            phase1_timeout = [ep for ep in training_data['timeout_episodes'] if ep < random_episodes]
            
            f.write(f"📈 第一阶段(随机场景)统计:\n")
            f.write(f"  • 平均奖励: {np.mean(phase1_rewards):.1f}\n")
            f.write(f"  • 奖励标准差: {np.std(phase1_rewards):.1f}\n")
            f.write(f"  • 奖励范围: {np.min(phase1_rewards):.1f} ~ {np.max(phase1_rewards):.1f}\n")
            f.write(f"  • 平均步数: {np.mean(phase1_steps):.1f}\n")
            f.write(f"  • 成功率: {len(phase1_success)/random_episodes:.3f} ({len(phase1_success)}/{random_episodes})\n")
            f.write(f"  • 碰撞episodes: {len(phase1_collision)}\n")
            f.write(f"  • 超时episodes: {len(phase1_timeout)}\n\n")
            
            # 第二阶段详细统计
            phase2_rewards = training_data['episode_rewards'][random_episodes:]
            phase2_steps = training_data['episode_steps'][random_episodes:]
            phase2_success = [ep for ep in training_data['success_episodes'] if ep >= random_episodes]
            phase2_collision = [ep for ep in training_data['collision_episodes'] if ep >= random_episodes]
            phase2_timeout = [ep for ep in training_data['timeout_episodes'] if ep >= random_episodes]
            
            phase2_episodes_count = len(phase2_rewards)
            
            f.write(f"📈 第二阶段(固定场景)统计:\n")
            f.write(f"  • 平均奖励: {np.mean(phase2_rewards):.1f}\n")
            f.write(f"  • 奖励标准差: {np.std(phase2_rewards):.1f}\n")
            f.write(f"  • 奖励范围: {np.min(phase2_rewards):.1f} ~ {np.max(phase2_rewards):.1f}\n")
            f.write(f"  • 平均步数: {np.mean(phase2_steps):.1f}\n")
            f.write(f"  • 成功率: {len(phase2_success)/phase2_episodes_count:.3f} ({len(phase2_success)}/{phase2_episodes_count})\n")
            f.write(f"  • 碰撞episodes: {len(phase2_collision)}\n")
            f.write(f"  • 超时episodes: {len(phase2_timeout)}\n\n")
            
            # 对比分析
            f.write(f"🔍 阶段对比分析:\n")
            f.write(f"  • 平均奖励变化: {np.mean(phase2_rewards) - np.mean(phase1_rewards):+.1f}\n")
            f.write(f"  • 成功率变化: {len(phase2_success)/phase2_episodes_count - len(phase1_success)/random_episodes:+.3f}\n")
            f.write(f"  • 平均步数变化: {np.mean(phase2_steps) - np.mean(phase1_steps):+.1f}\n")
            
            # 趋势分析
            if len(phase1_rewards) > 1:
                phase1_trend = np.polyfit(range(len(phase1_rewards)), phase1_rewards, 1)[0]
                f.write(f"  • 第一阶段奖励趋势: {phase1_trend:+.2f}/episode\n")
            
            if len(phase2_rewards) > 1:
                phase2_trend = np.polyfit(range(len(phase2_rewards)), phase2_rewards, 1)[0]
                f.write(f"  • 第二阶段奖励趋势: {phase2_trend:+.2f}/episode\n")
            
            f.write(f"\n📅 报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        print(f"📄 文本总结报告已保存: {filename}")
        
    except Exception as e:
        print(f"⚠️ 文本报告生成失败: {e}")

def main():
    """主函数"""
    print("🎯 简单图表生成器")
    print("由于matplotlib环境问题，改为生成数据分析报告")
    print("=" * 50)
    
    # 分析数据
    analyze_data()
    
    # 生成文本总结
    generate_text_summary()
    
    print("\n✅ 所有报告生成完成！")
    print("📊 您可以使用生成的CSV和文本文件来了解训练结果")
    print("🎨 如需图表，建议在有GUI环境的机器上运行matplotlib代码")

if __name__ == "__main__":
    main()
