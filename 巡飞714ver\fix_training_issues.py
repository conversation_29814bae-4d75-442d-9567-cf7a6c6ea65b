"""
修复训练问题的脚本
基于诊断结果修复关键问题
"""

import numpy as np
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_reward_function():
    """修复奖励函数 - 使其更加密集和引导性"""
    print("🔧 修复奖励函数")
    
    # 读取当前环境文件
    with open('loitering_munition_env.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找奖励计算部分
    old_reward_section = '''        # 1. 距离奖励（鼓励接近目标）
        goal_distance = np.linalg.norm(pos - self.goal)
        distance_improvement = self._prev_goal_dist - goal_distance
        distance_reward = distance_improvement * 0.1  # 每米改善给0.1奖励
        
        # 2. 到达目标奖励
        if goal_distance < 50.0:  # 50米内算到达
            arrival_reward = 1000.0
            done = True
            info['success'] = True
            info['reason'] = 'goal_reached'
        else:
            arrival_reward = 0.0
        
        # 3. 碰撞惩罚
        collision_penalty = 0.0
        for obs in self.obstacles + self.dynamic_obstacles:
            distance_to_obs = np.linalg.norm(pos - obs['center'])
            if distance_to_obs <= obs['radius'] + 5.0:  # 5米安全距离
                collision_penalty = -1000.0
                done = True
                info['collision'] = True
                info['reason'] = 'collision'
                break
        
        # 4. 边界惩罚
        boundary_penalty = 0.0
        if (pos[0] < self.bounds[0] or pos[0] > self.bounds[1] or 
            pos[1] < self.bounds[2] or pos[1] > self.bounds[3] or 
            pos[2] < self.bounds[4] or pos[2] > self.bounds[5]):
            boundary_penalty = -500.0
            done = True
            info['out_of_bounds'] = True
            info['reason'] = 'out_of_bounds'
        
        # 5. 时间惩罚
        time_penalty = -1.0  # 每步-1，鼓励快速到达
        
        # 6. 速度奖励（鼓励保持合理速度）
        V = self.state[3]
        target_speed = 25.0  # 目标巡航速度
        speed_penalty = -abs(V - target_speed) * 0.1
        
        # 7. 超时惩罚
        if self.step_count >= self.max_steps:
            done = True
            info['timeout'] = True
            info['reason'] = 'timeout'
        
        # 总奖励
        reward = (distance_reward + arrival_reward + collision_penalty + 
                 boundary_penalty + time_penalty + speed_penalty)'''
    
    # 新的改进奖励函数
    new_reward_section = '''        # 改进的奖励函数 - 更密集和引导性
        goal_distance = np.linalg.norm(pos - self.goal)
        distance_improvement = self._prev_goal_dist - goal_distance
        
        # 1. 距离改善奖励（更大的奖励）
        distance_reward = distance_improvement * 1.0  # 每米改善给1.0奖励
        
        # 2. 距离引导奖励（基于当前距离的连续奖励）
        max_distance = np.linalg.norm(self.start - self.goal)
        distance_progress = 1.0 - (goal_distance / max_distance)
        progress_reward = distance_progress * 10.0  # 基于进度的奖励
        
        # 3. 到达目标奖励
        if goal_distance < 50.0:  # 50米内算到达
            arrival_reward = 2000.0  # 增加到达奖励
            done = True
            info['success'] = True
            info['reason'] = 'goal_reached'
        else:
            arrival_reward = 0.0
        
        # 4. 碰撞惩罚（减少惩罚，避免过度保守）
        collision_penalty = 0.0
        min_obs_distance = float('inf')
        for obs in self.obstacles + self.dynamic_obstacles:
            distance_to_obs = np.linalg.norm(pos - obs['center'])
            min_obs_distance = min(min_obs_distance, distance_to_obs - obs['radius'])
            
            if distance_to_obs <= obs['radius'] + 5.0:  # 5米安全距离
                collision_penalty = -500.0  # 减少碰撞惩罚
                done = True
                info['collision'] = True
                info['reason'] = 'collision'
                break
        
        # 5. 安全距离奖励（鼓励保持安全距离但不过度保守）
        if min_obs_distance < float('inf'):
            if min_obs_distance > 20.0:
                safety_reward = 5.0  # 安全距离奖励
            elif min_obs_distance > 10.0:
                safety_reward = 2.0
            else:
                safety_reward = -2.0  # 轻微惩罚过近距离
        else:
            safety_reward = 0.0
        
        # 6. 边界惩罚（减少惩罚）
        boundary_penalty = 0.0
        if (pos[0] < self.bounds[0] or pos[0] > self.bounds[1] or 
            pos[1] < self.bounds[2] or pos[1] > self.bounds[3] or 
            pos[2] < self.bounds[4] or pos[2] > self.bounds[5]):
            boundary_penalty = -200.0  # 减少边界惩罚
            done = True
            info['out_of_bounds'] = True
            info['reason'] = 'out_of_bounds'
        
        # 7. 时间惩罚（减少）
        time_penalty = -0.5  # 减少时间惩罚
        
        # 8. 速度奖励（鼓励保持合理速度）
        V = self.state[3]
        target_speed = 25.0  # 目标巡航速度
        speed_reward = max(0, 5.0 - abs(V - target_speed) * 0.2)
        
        # 9. 前进奖励（鼓励朝目标方向移动）
        goal_direction = (self.goal - pos) / (goal_distance + 1e-6)
        velocity_direction = np.array([
            V * np.cos(self.state[4]) * np.cos(self.state[5]),
            V * np.cos(self.state[4]) * np.sin(self.state[5]),
            V * np.sin(self.state[4])
        ])
        if np.linalg.norm(velocity_direction) > 0:
            velocity_direction = velocity_direction / np.linalg.norm(velocity_direction)
            alignment = np.dot(goal_direction, velocity_direction)
            forward_reward = max(0, alignment) * 5.0
        else:
            forward_reward = 0.0
        
        # 10. 超时检查（增加最大步数）
        if self.step_count >= self.max_steps:
            done = True
            info['timeout'] = True
            info['reason'] = 'timeout'
        
        # 总奖励（更平衡的组合）
        reward = (distance_reward + progress_reward + arrival_reward + 
                 collision_penalty + safety_reward + boundary_penalty + 
                 time_penalty + speed_reward + forward_reward)'''
    
    # 替换奖励函数
    if old_reward_section in content:
        content = content.replace(old_reward_section, new_reward_section)
        print("✅ 奖励函数已更新")
    else:
        print("⚠️ 未找到原始奖励函数，需要手动修改")
    
    # 增加最大步数
    content = content.replace('self.max_steps = 2000', 'self.max_steps = 3000')
    
    # 保存修改后的文件
    with open('loitering_munition_env.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 环境文件已更新")

def fix_dwa_parameters():
    """修复DWA参数 - 增加动作多样性"""
    print("🔧 修复DWA参数")
    
    # 读取DWA文件
    with open('loitering_munition_dwa.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修改分辨率参数，增加动作多样性
    modifications = [
        ('self.a_T_resolution = 2.0', 'self.a_T_resolution = 1.5'),  # 更细粒度
        ('self.a_N_resolution = 8.0', 'self.a_N_resolution = 6.0'),  # 更细粒度
        ('self.mu_resolution = 0.2', 'self.mu_resolution = 0.15'),   # 更细粒度
        ('self.min_safe_distance = 5.0', 'self.min_safe_distance = 8.0'),  # 增加安全距离
    ]
    
    for old, new in modifications:
        if old in content:
            content = content.replace(old, new)
            print(f"✅ 已修改: {old} -> {new}")
    
    # 保存修改后的文件
    with open('loitering_munition_dwa.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ DWA参数已更新")

def fix_training_parameters():
    """修复训练参数 - 增加探索"""
    print("🔧 修复训练参数")
    
    # 读取训练文件
    with open('train_loitering_munition.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修改探索参数
    modifications = [
        ('noise=0.1', 'noise=0.2'),  # 增加探索噪声
        ('replay_buffer.size() < 1000', 'replay_buffer.size() < 2000'),  # 延长DWA引导期
    ]
    
    for old, new in modifications:
        if old in content:
            content = content.replace(old, new)
            print(f"✅ 已修改: {old} -> {new}")
    
    # 保存修改后的文件
    with open('train_loitering_munition.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 训练参数已更新")

def create_quick_test():
    """创建快速测试脚本"""
    print("🔧 创建快速测试脚本")
    
    test_script = '''"""
快速测试修复效果
"""

import numpy as np
import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from loitering_munition_env import LoiteringMunitionEnvironment
from loitering_munition_dwa import LoiteringMunitionDWA
from scenario_config import get_scenario_config

def quick_test():
    """快速测试修复效果"""
    print("🧪 快速测试修复效果")
    print("=" * 40)
    
    # 创建环境
    scenario_config = get_scenario_config('stage1')
    env = LoiteringMunitionEnvironment(
        bounds=scenario_config['environment_bounds'],
        fixed_scenario_config=scenario_config
    )
    dwa = LoiteringMunitionDWA()
    
    # 重置环境
    obs = env.reset(verbose=False)
    print(f"初始状态: 位置={env.state[:3]}, 速度={env.state[3]:.2f}")
    print(f"目标距离: {np.linalg.norm(env.state[:3] - env.goal):.1f}m")
    
    # 测试50步
    total_reward = 0
    for step in range(50):
        # 生成安全动作
        safe_actions = dwa.generate_safe_control_set(
            env.state, env.obstacles + env.dynamic_obstacles, env.goal, max_actions=10
        )
        
        if safe_actions:
            action = safe_actions[0]  # 使用最优动作
        else:
            action = np.array([0.0, 0.0, 0.0])  # 无动作
        
        # 执行动作
        obs, reward, done, info = env.step(action)
        total_reward += reward
        
        if step % 10 == 0 or done:
            pos = env.state[:3]
            goal_dist = np.linalg.norm(pos - env.goal)
            print(f"Step {step+1}: 位置=[{pos[0]:.0f},{pos[1]:.0f},{pos[2]:.0f}], "
                  f"目标距离={goal_dist:.1f}m, 奖励={reward:.2f}, 累积={total_reward:.2f}")
        
        if done:
            print(f"终止原因: {info.get('reason', 'unknown')}")
            break
    
    print(f"\\n测试结果:")
    print(f"  总步数: {step+1}")
    print(f"  总奖励: {total_reward:.2f}")
    print(f"  平均奖励: {total_reward/(step+1):.2f}")
    print(f"  最终距离: {np.linalg.norm(env.state[:3] - env.goal):.1f}m")
    
    if total_reward > -100:
        print("✅ 奖励函数修复成功！")
    else:
        print("❌ 奖励函数仍需调整")

if __name__ == "__main__":
    quick_test()
'''
    
    with open('quick_test_fixes.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ 快速测试脚本已创建")

def main():
    """主修复函数"""
    print("🔧 修复训练问题")
    print("=" * 50)
    print("基于训练结果分析，执行以下修复:")
    print("1. 修复奖励函数 - 更密集和引导性")
    print("2. 修复DWA参数 - 增加动作多样性")
    print("3. 修复训练参数 - 增加探索")
    print("4. 创建快速测试")
    print("=" * 50)
    
    try:
        # 执行修复
        fix_reward_function()
        fix_dwa_parameters()
        fix_training_parameters()
        create_quick_test()
        
        print("\\n" + "=" * 50)
        print("✅ 所有修复完成！")
        print("\\n🚀 下一步:")
        print("1. 运行快速测试: python quick_test_fixes.py")
        print("2. 如果测试通过，重新训练: python train_loitering_munition.py")
        print("3. 监控训练过程，确保奖励改善和成功率提升")
        print("=" * 50)
        
    except Exception as e:
        print(f"\\n❌ 修复过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
'''

    with open('fix_training_issues.py', 'w', encoding='utf-8') as f:
        f.write(fix_script)
    
    print("✅ 修复脚本已创建")
