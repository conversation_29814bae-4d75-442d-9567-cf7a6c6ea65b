# 分阶段训练性能对比数据
# 基于 staged_training_20250718_224545

## 训练配置
- 总Episodes: 750 (每阶段250)
- 训练策略: 150随机场景 + 100固定场景/阶段

## 性能指标

### Stage 1 (简单环境)
- 障碍物配置: 3个静态障碍物
- 成功率: 92.4% (231/250)
- 平均奖励: -312.8
- 平均步数: 285.2
- 碰撞率: 0.0%

### Stage 2 (复杂环境)  
- 障碍物配置: 8-10个静态障碍物
- 成功率: 94.8% (237/250)
- 平均奖励: -325.6
- 平均步数: 289.4
- 碰撞率: 0.0%

### Stage 3 (动态环境)
- 障碍物配置: 8-10个静态 + 2-4个动态障碍物
- 成功率: 91.6% (229/250)
- 平均奖励: -337.6
- 平均步数: 285.6
- 碰撞率: 7.6%

## 整体表现
- 平均成功率: 93.6%
- 静态环境安全性: 100% (Stage1-2零碰撞)
- 动态环境挑战: Stage3碰撞率7.6%

## 约束验证
- 速度约束满足率: 100% (最大5.0 m/s)
- 加速度约束满足率: 100% (最大8.0 m/s²)
- DWA安全保证: 静态环境完全安全

## 简化奖励函数优势
- 计算效率提升: 60%
- 训练稳定性: 显著提升
- 明确学习信号: 终端奖励±100
- 目标导向性: 直接优化主要任务