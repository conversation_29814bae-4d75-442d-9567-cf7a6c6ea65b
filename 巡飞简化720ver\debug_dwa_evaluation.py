"""
调试DWA评价函数
检查为什么DWA生成的控制不能正确朝向目标
"""

import numpy as np
import matplotlib.pyplot as plt
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from loitering_munition_environment import LoiteringMunitionEnvironment
from loitering_munition_dwa import LoiteringMunitionDWA
from environment_config import get_environment_config, get_loitering_munition_config

def debug_dwa_evaluation():
    """调试DWA评价函数"""
    print("🔍 DWA评价函数调试")
    print("=" * 50)
    
    # 创建环境和DWA
    env_config = get_environment_config('stage1_simple')
    lm_config = get_loitering_munition_config()
    
    env = LoiteringMunitionEnvironment(
        bounds=[2000, 2000, 2000],
        environment_config=env_config,
        reward_type='simplified'
    )
    
    dwa = LoiteringMunitionDWA(dt=lm_config['dt'])
    
    # 重置环境
    state = env.reset()
    
    print(f"测试场景:")
    print(f"  起点: {env.start}")
    print(f"  终点: {env.goal}")
    print(f"  当前状态: {env.state}")
    
    # 计算目标方向
    goal_direction = env.goal - env.state[:3]
    goal_direction_norm = goal_direction / np.linalg.norm(goal_direction)
    
    print(f"  目标方向: {goal_direction}")
    print(f"  目标方向(归一化): {goal_direction_norm}")
    print(f"  理论偏航角: {np.degrees(np.arctan2(goal_direction[1], goal_direction[0])):.1f}°")
    
    # 当前速度方向
    V, gamma, psi = env.state[3:6]
    current_direction = np.array([
        np.cos(gamma) * np.cos(psi),
        np.cos(gamma) * np.sin(psi),
        np.sin(gamma)
    ])
    
    print(f"  当前速度方向: {current_direction}")
    print(f"  当前偏航角: {np.degrees(psi):.1f}°")
    print(f"  当前倾斜角: {np.degrees(gamma):.1f}°")
    
    # 计算当前朝向与目标的夹角
    current_heading_score = np.dot(current_direction, goal_direction_norm)
    current_angle = np.degrees(np.arccos(np.clip(current_heading_score, -1, 1)))
    
    print(f"  当前朝向与目标夹角: {current_angle:.1f}°")
    print(f"  当前朝向评分: {current_heading_score:.3f}")
    
    return env, dwa, goal_direction_norm, current_direction

def test_control_prediction():
    """测试控制预测"""
    print(f"\n🎮 控制预测测试")
    print("=" * 50)
    
    env, dwa, goal_direction_norm, current_direction = debug_dwa_evaluation()
    
    # 生成一些测试控制
    test_controls = [
        [8.0, 0.0, 0.0],      # 纯加速
        [0.0, 30.0, 0.0],     # 纯法向加速
        [0.0, 0.0, np.pi/4],  # 纯倾斜角变化
        [4.0, 20.0, np.pi/6], # 组合控制
    ]
    
    print(f"测试不同控制输入的效果:")
    
    for i, control in enumerate(test_controls):
        print(f"\n  控制 {i+1}: a_T={control[0]:.1f}, a_N={control[1]:.1f}, μ={np.degrees(control[2]):.1f}°")
        
        # 预测轨迹
        trajectory = dwa._predict_trajectory(control, env.state, dwa.predict_time)
        
        if trajectory and len(trajectory) > 1:
            # 计算运动方向
            movement = trajectory[-1] - trajectory[0]
            movement_norm = movement / np.linalg.norm(movement) if np.linalg.norm(movement) > 0 else np.zeros(3)
            
            # 计算与目标方向的夹角
            movement_heading_score = np.dot(movement_norm, goal_direction_norm)
            movement_angle = np.degrees(np.arccos(np.clip(movement_heading_score, -1, 1)))
            
            print(f"    预测移动: {movement}")
            print(f"    移动方向: {movement_norm}")
            print(f"    与目标夹角: {movement_angle:.1f}°")
            print(f"    朝向目标: {'✅' if movement_angle < 90 else '❌'}")
            
            # 预测最终状态
            final_state = dwa._predict_final_state(control, env.state, dwa.predict_time)
            final_V, final_gamma, final_psi = final_state[3:6]
            
            # 最终速度方向
            final_direction = np.array([
                np.cos(final_gamma) * np.cos(final_psi),
                np.cos(final_gamma) * np.sin(final_psi),
                np.sin(final_gamma)
            ])
            
            final_heading_score = np.dot(final_direction, goal_direction_norm)
            final_angle = np.degrees(np.arccos(np.clip(final_heading_score, -1, 1)))
            
            print(f"    最终速度方向: {final_direction}")
            print(f"    最终朝向夹角: {final_angle:.1f}°")
            
            # DWA评价
            score = dwa.evaluate_control(control, env.state, env.goal, env.obstacles + env.dynamic_obstacles)
            print(f"    DWA评分: {score:.3f}")
            
        else:
            print(f"    ❌ 无法预测轨迹")

def test_dwa_evaluation_bug():
    """测试DWA评价函数的bug"""
    print(f"\n🐛 DWA评价函数Bug测试")
    print("=" * 50)
    
    env, dwa, goal_direction_norm, current_direction = debug_dwa_evaluation()
    
    # 创建一个明显朝向目标的控制
    # 目标在东北上方，所以需要正的偏航角和倾斜角变化
    target_control = [4.0, 20.0, np.pi/6]  # 加速 + 法向加速 + 倾斜角变化
    
    print(f"测试明显朝向目标的控制: {target_control}")
    
    # 预测这个控制的效果
    trajectory = dwa._predict_trajectory(target_control, env.state, dwa.predict_time)
    final_state = dwa._predict_final_state(target_control, env.state, dwa.predict_time)
    
    if trajectory and len(trajectory) > 1:
        # 实际运动方向
        movement = trajectory[-1] - trajectory[0]
        movement_norm = movement / np.linalg.norm(movement)
        movement_angle = np.degrees(np.arccos(np.clip(np.dot(movement_norm, goal_direction_norm), -1, 1)))
        
        print(f"  实际运动方向: {movement_norm}")
        print(f"  与目标夹角: {movement_angle:.1f}°")
        print(f"  实际朝向目标: {'✅' if movement_angle < 90 else '❌'}")
        
        # 最终速度方向
        final_V, final_gamma, final_psi = final_state[3:6]
        final_direction = np.array([
            np.cos(final_gamma) * np.cos(final_psi),
            np.cos(final_gamma) * np.sin(final_psi),
            np.sin(final_gamma)
        ])
        final_angle = np.degrees(np.arccos(np.clip(np.dot(final_direction, goal_direction_norm), -1, 1)))
        
        print(f"  最终速度方向: {final_direction}")
        print(f"  最终朝向夹角: {final_angle:.1f}°")
        print(f"  最终朝向目标: {'✅' if final_angle < 90 else '❌'}")
        
        # 现在检查DWA评价函数使用的是哪个方向
        print(f"\n🔍 DWA评价函数分析:")
        
        # 当前方向评分（DWA实际使用的）
        current_heading_score = max(0, np.dot(current_direction, goal_direction_norm))
        print(f"  当前方向评分: {current_heading_score:.3f} (DWA实际使用)")
        
        # 最终方向评分（应该使用的）
        final_heading_score = max(0, np.dot(final_direction, goal_direction_norm))
        print(f"  最终方向评分: {final_heading_score:.3f} (应该使用)")
        
        # 运动方向评分（更直观的）
        movement_heading_score = max(0, np.dot(movement_norm, goal_direction_norm))
        print(f"  运动方向评分: {movement_heading_score:.3f} (最直观)")
        
        print(f"\n🐛 Bug确认:")
        print(f"  DWA使用当前方向: {current_heading_score:.3f}")
        print(f"  应该使用最终方向: {final_heading_score:.3f}")
        print(f"  差异: {abs(current_heading_score - final_heading_score):.3f}")
        
        if abs(current_heading_score - final_heading_score) > 0.1:
            print(f"  ❌ 确认Bug：DWA评价函数使用错误的方向！")
        else:
            print(f"  ✅ 方向评价正确")
        
        # 完整的DWA评分
        total_score = dwa.evaluate_control(target_control, env.state, env.goal, env.obstacles + env.dynamic_obstacles)
        print(f"  DWA总评分: {total_score:.3f}")

def test_safe_control_generation():
    """测试安全控制生成"""
    print(f"\n🛡️ 安全控制生成测试")
    print("=" * 50)
    
    env, dwa, goal_direction_norm, current_direction = debug_dwa_evaluation()
    
    # 生成安全控制
    safe_controls = dwa.generate_safe_control_set(
        env.state, env.obstacles + env.dynamic_obstacles, env.goal, max_actions=10
    )
    
    print(f"生成的安全控制数量: {len(safe_controls)}")
    
    if safe_controls:
        print(f"\n分析前5个安全控制:")
        
        for i, control in enumerate(safe_controls[:5]):
            print(f"\n  控制 {i+1}: {control}")
            
            # 预测效果
            trajectory = dwa._predict_trajectory(control, env.state, dwa.predict_time)
            
            if trajectory and len(trajectory) > 1:
                movement = trajectory[-1] - trajectory[0]
                movement_norm = movement / np.linalg.norm(movement)
                movement_angle = np.degrees(np.arccos(np.clip(np.dot(movement_norm, goal_direction_norm), -1, 1)))
                
                print(f"    运动方向: {movement_norm}")
                print(f"    与目标夹角: {movement_angle:.1f}°")
                print(f"    朝向目标: {'✅' if movement_angle < 90 else '❌'}")
                
                # DWA评分
                score = dwa.evaluate_control(control, env.state, env.goal, env.obstacles + env.dynamic_obstacles)
                print(f"    DWA评分: {score:.3f}")
            
        # 统计朝向目标的比例
        toward_target_count = 0
        for control in safe_controls:
            trajectory = dwa._predict_trajectory(control, env.state, dwa.predict_time)
            if trajectory and len(trajectory) > 1:
                movement = trajectory[-1] - trajectory[0]
                movement_norm = movement / np.linalg.norm(movement)
                movement_angle = np.degrees(np.arccos(np.clip(np.dot(movement_norm, goal_direction_norm), -1, 1)))
                if movement_angle < 90:
                    toward_target_count += 1
        
        toward_target_rate = toward_target_count / len(safe_controls)
        print(f"\n朝向目标的控制比例: {toward_target_count}/{len(safe_controls)} = {toward_target_rate:.1%}")
        
        if toward_target_rate < 0.5:
            print(f"❌ 大部分控制不朝向目标！这解释了为什么训练失败")
        else:
            print(f"✅ 大部分控制朝向目标")

def main():
    """主函数"""
    print("🔍 DWA评价函数深度调试")
    print("=" * 60)
    
    try:
        # 1. 调试评价函数
        debug_dwa_evaluation()
        
        # 2. 测试控制预测
        test_control_prediction()
        
        # 3. 测试评价函数bug
        test_dwa_evaluation_bug()
        
        # 4. 测试安全控制生成
        test_safe_control_generation()
        
        print(f"\n🎯 调试总结")
        print("=" * 30)
        print(f"如果发现DWA评价函数使用当前方向而不是预测方向，")
        print(f"这就解释了为什么生成的控制不能正确朝向目标！")
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
