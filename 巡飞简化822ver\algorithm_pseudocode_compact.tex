\documentclass[UTF8,11pt]{ctexart}

% 字体设置
\usepackage{fontspec}
\usepackage{xeCJK}
\setCJKmainfont{SimSun}
\setCJKsansfont{SimHei}

% 算法相关包
\usepackage{algorithm}
\usepackage{algpseudocode}
\usepackage{amsmath,amssymb}

% 页面设置 - 紧凑单列布局
\usepackage[a4paper,left=2.5cm,right=2.5cm,top=2cm,bottom=2cm]{geometry}

% 算法格式设置
\algrenewcommand\algorithmicrequire{\textbf{输入:}}
\algrenewcommand\algorithmicensure{\textbf{输出:}}

% 增加算法步骤之间的间距
\algrenewcommand{\algorithmiccomment}[1]{\hfill\(\triangleright\) #1}
\renewcommand{\algorithmicrequire}{\textbf{Require:}}
\renewcommand{\algorithmicensure}{\textbf{Ensure:}}

% 设置较大的行间距，类似图片效果
\renewcommand{\baselinestretch}{1.3}
\setlength{\parskip}{0.8em}

% 设置算法环境的行间距
\makeatletter
\renewcommand{\ALG@beginalgorithmic}{\small\setlength{\itemsep}{0.3em}}
\makeatother

% 标题设置
\title{\textbf{DWA-TD3约束优化训练算法}}
\author{}
\date{}

\begin{document}

\maketitle

% 紧凑的算法布局
\begin{algorithm}[H]
\caption{DWA-TD3约束优化训练算法}
\label{alg:dwa_td3_constrained}
\begin{algorithmic}[1]
\Require 初始状态$s_0$，目标位置$\mathbf{g}_{goal}$，环境$\mathcal{E}$
\Ensure 训练后的策略网络$\pi_\theta$，价值网络$Q_{\phi_1}, Q_{\phi_2}$
\State 初始化Actor网络$\pi_\theta$，双Critic网络$Q_{\phi_1}, Q_{\phi_2}$
\State 初始化目标网络$\pi_{\bar{\theta}}, Q_{\bar{\phi}_1}, Q_{\bar{\phi}_2}$
\State 初始化经验回放缓冲区$\mathcal{D} = \emptyset$
\For{episode $= 1$ to $N_{episodes}$}
    \State 重置环境获取初始状态：$s_0 = \text{Environment.reset()}$
    \State $t = 0, \mathbf{o}_t = \phi(s_t)$
    \While{not terminal and $t < T_{max}$}
        \State $\mathcal{U}_{safe} = \text{DWA}(\mathbf{o}_t, \mathcal{E}, \mathbf{g}_{goal})$
        \If{$|\mathcal{U}_{safe}| > 0$}
            \State $\mathcal{A}_{safe} = \{\phi_{norm}(u_i) : u_i \in \mathcal{U}_{safe}\}$
            \State $Q_{best} = -\infty, a_t^* = \mathcal{A}_{safe}[0]$
            \For{$a_i \in \mathcal{A}_{safe}$}
                \State $Q_i = \min(Q_{\phi_1}(\mathbf{o}_t, a_i), Q_{\phi_2}(\mathbf{o}_t, a_i))$
                \If{$Q_i > Q_{best}$}
                    \State $Q_{best} = Q_i, a_t^* = a_i$
                \EndIf
            \EndFor
        \Else
            \State $a_t^* = [-0.5, 0.0, 0.0]$
        \EndIf
        \State $u_t = \phi_{denorm}(a_t^*)$
        \State $\mathbf{o}_{t+1}, r_t, done = \text{Environment.step}(u_t)$
        \State $\mathcal{D} = \mathcal{D} \cup \{(\mathbf{o}_t, a_t^*, r_t, \mathbf{o}_{t+1}, done)\}$
        \If{$|\mathcal{D}| \geq \text{batch\_size}$}
            \State 采样批次数据，更新Critic网络$Q_{\phi_1}, Q_{\phi_2}$
            \If{$t \bmod \text{policy\_freq} = 0$}
                \State 延迟更新Actor网络$\pi_\theta$，软更新目标网络
            \EndIf
        \EndIf
        \State $t = t + 1$
    \EndWhile
\EndFor
\State \Return $\pi_\theta, Q_{\phi_1}, Q_{\phi_2}$
\end{algorithmic}
\end{algorithm}

\section{算法说明}

该算法实现了DWA-TD3融合架构的核心训练流程，采用紧凑的单列布局，主要特点包括：

\begin{itemize}
\setlength{\itemsep}{0.3em}
\item \textbf{约束满足与策略优化解耦}：DWA层生成安全控制集合，TD3层在此基础上进行全局优化
\item \textbf{双重安全保障}：通过DWA的运动学约束和避障约束确保动作安全性
\item \textbf{价值驱动决策}：使用双Critic网络评估动作价值，选择最优安全动作
\item \textbf{经验回放机制}：存储有效的状态-动作对，支持离线学习和策略改进
\item \textbf{延迟更新策略}：采用TD3特有的延迟更新机制，降低过估计偏差
\end{itemize}

\end{document}
