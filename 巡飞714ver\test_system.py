"""
巡飞弹系统测试脚本
测试训练好的模型在各个场景中的表现
"""

import numpy as np
import torch
import time

from loitering_munition_env import LoiteringMunitionEnvironment
from scenario_config import get_scenario_config
from td3_network import TD3Agent, map_action_to_control

def test_trained_model(model_path, stage='stage3', num_episodes=10):
    """测试训练好的模型"""
    print(f"🧪 测试训练模型: {model_path}")
    print(f"📊 测试场景: {stage}")
    print("=" * 50)
    
    # 加载模型
    agent = TD3Agent(state_dim=15, action_dim=3, max_action=1.0)
    agent.load(model_path)
    print("✅ 模型加载成功")
    
    # 创建测试环境
    scenario_config = get_scenario_config(stage)
    env = LoiteringMunitionEnvironment(
        bounds=scenario_config['environment_bounds'],
        fixed_scenario_config=scenario_config
    )
    print(f"✅ 测试环境创建成功: {scenario_config['description']}")
    
    # 测试统计
    results = {
        'success_count': 0,
        'collision_count': 0,
        'timeout_count': 0,
        'episode_rewards': [],
        'episode_steps': [],
        'episode_times': []
    }
    
    for episode in range(num_episodes):
        print(f"\n🎯 测试Episode {episode + 1}/{num_episodes}")
        
        # 重置环境
        state = env.reset(verbose=(episode == 0))
        episode_reward = 0
        episode_steps = 0
        start_time = time.time()
        
        trajectory = []
        
        while True:
            # 记录轨迹
            trajectory.append(env.state.copy())
            
            # 智能体选择动作（无噪声）
            action = agent.select_action(state, noise=0.0)
            control = map_action_to_control(action)
            
            # 执行动作
            next_state, reward, done, info = env.step(control)
            
            state = next_state
            episode_reward += reward
            episode_steps += 1
            
            # 打印关键信息
            if episode_steps % 100 == 0:
                pos = env.state[:3]
                goal_dist = np.linalg.norm(pos - env.goal)
                print(f"   步骤 {episode_steps}: 位置=({pos[0]:.0f},{pos[1]:.0f},{pos[2]:.0f}), "
                      f"目标距离={goal_dist:.0f}m")
            
            if done:
                episode_time = time.time() - start_time
                
                # 统计结果
                if info.get('success'):
                    results['success_count'] += 1
                    result_str = "✅ 成功到达目标"
                elif info.get('collision'):
                    results['collision_count'] += 1
                    result_str = "❌ 发生碰撞"
                elif info.get('timeout'):
                    results['timeout_count'] += 1
                    result_str = "⏰ 超时"
                else:
                    result_str = "❓ 其他原因"
                
                results['episode_rewards'].append(episode_reward)
                results['episode_steps'].append(episode_steps)
                results['episode_times'].append(episode_time)
                
                print(f"   结果: {result_str}")
                print(f"   奖励: {episode_reward:.2f}, 步数: {episode_steps}, 时间: {episode_time:.1f}s")
                
                # 分析轨迹
                analyze_trajectory(trajectory, env.goal)
                break
    
    # 打印测试总结
    print_test_summary(results, num_episodes)
    
    return results

def analyze_trajectory(trajectory, goal):
    """分析飞行轨迹"""
    if len(trajectory) < 2:
        return
    
    traj = np.array(trajectory)
    
    # 计算轨迹统计
    total_distance = 0
    for i in range(1, len(traj)):
        total_distance += np.linalg.norm(traj[i][:3] - traj[i-1][:3])
    
    straight_distance = np.linalg.norm(traj[-1][:3] - traj[0][:3])
    path_efficiency = straight_distance / total_distance if total_distance > 0 else 0
    
    # 速度统计
    velocities = traj[:, 3]  # V
    avg_velocity = np.mean(velocities)
    max_velocity = np.max(velocities)
    min_velocity = np.min(velocities)
    
    # 高度变化
    altitudes = traj[:, 2]  # z
    max_altitude = np.max(altitudes)
    min_altitude = np.min(altitudes)
    
    print(f"   📈 轨迹分析:")
    print(f"      总飞行距离: {total_distance:.0f}m")
    print(f"      直线距离: {straight_distance:.0f}m")
    print(f"      路径效率: {path_efficiency:.2f}")
    print(f"      平均速度: {avg_velocity:.1f}m/s")
    print(f"      速度范围: {min_velocity:.1f}-{max_velocity:.1f}m/s")
    print(f"      高度范围: {min_altitude:.0f}-{max_altitude:.0f}m")

def print_test_summary(results, num_episodes):
    """打印测试总结"""
    print("\n📊 测试总结")
    print("=" * 50)
    
    success_rate = results['success_count'] / num_episodes
    collision_rate = results['collision_count'] / num_episodes
    timeout_rate = results['timeout_count'] / num_episodes
    
    avg_reward = np.mean(results['episode_rewards'])
    avg_steps = np.mean(results['episode_steps'])
    avg_time = np.mean(results['episode_times'])
    
    print(f"🎯 成功率: {success_rate:.1%} ({results['success_count']}/{num_episodes})")
    print(f"💥 碰撞率: {collision_rate:.1%} ({results['collision_count']}/{num_episodes})")
    print(f"⏰ 超时率: {timeout_rate:.1%} ({results['timeout_count']}/{num_episodes})")
    print(f"🏆 平均奖励: {avg_reward:.2f}")
    print(f"👣 平均步数: {avg_steps:.1f}")
    print(f"⏱️ 平均时间: {avg_time:.1f}秒")

def test_all_stages(model_dir):
    """测试所有阶段的模型"""
    print("🧪 测试所有阶段模型")
    print("=" * 60)
    
    stages = ['stage1', 'stage2', 'stage3']
    all_results = {}
    
    for stage in stages:
        model_path = f"{model_dir}/{stage}_model.pth"
        print(f"\n🚀 测试 {stage} 模型")
        print("-" * 40)
        
        try:
            results = test_trained_model(model_path, stage, num_episodes=5)
            all_results[stage] = results
        except Exception as e:
            print(f"❌ {stage} 测试失败: {e}")
            all_results[stage] = None
    
    # 打印对比结果
    print("\n📊 各阶段对比")
    print("=" * 60)
    print(f"{'阶段':<10} {'成功率':<10} {'平均奖励':<12} {'平均步数':<10}")
    print("-" * 50)
    
    for stage in stages:
        if all_results[stage]:
            results = all_results[stage]
            success_rate = results['success_count'] / 5  # 5个测试episode
            avg_reward = np.mean(results['episode_rewards'])
            avg_steps = np.mean(results['episode_steps'])
            
            print(f"{stage:<10} {success_rate:<10.1%} {avg_reward:<12.2f} {avg_steps:<10.1f}")
        else:
            print(f"{stage:<10} {'失败':<10} {'N/A':<12} {'N/A':<10}")

def quick_test():
    """快速测试系统功能"""
    print("⚡ 快速系统测试")
    print("=" * 40)
    
    try:
        # 测试环境创建
        print("1. 测试环境创建...")
        scenario_config = get_scenario_config('stage1')
        env = LoiteringMunitionEnvironment(
            bounds=scenario_config['environment_bounds'],
            fixed_scenario_config=scenario_config
        )
        state = env.reset(verbose=True)
        print(f"   ✅ 环境创建成功，状态维度: {len(state)}")
        
        # 测试网络创建
        print("2. 测试网络创建...")
        agent = TD3Agent(state_dim=15, action_dim=3, max_action=1.0)
        action = agent.select_action(state, noise=0.0)
        control = map_action_to_control(action)
        print(f"   ✅ 网络创建成功，动作: {action}, 控制: {control}")
        
        # 测试环境步进
        print("3. 测试环境步进...")
        next_state, reward, done, info = env.step(control)
        print(f"   ✅ 环境步进成功，奖励: {reward:.3f}, 完成: {done}")
        
        print("\n🎉 快速测试通过！系统运行正常。")
        return True
        
    except Exception as e:
        print(f"\n❌ 快速测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='巡飞弹系统测试')
    parser.add_argument('--mode', choices=['quick', 'single', 'all'], 
                       default='quick', help='测试模式')
    parser.add_argument('--model', type=str, help='模型文件路径')
    parser.add_argument('--model-dir', type=str, help='模型目录路径')
    parser.add_argument('--stage', choices=['stage1', 'stage2', 'stage3'], 
                       default='stage3', help='测试阶段')
    parser.add_argument('--episodes', type=int, default=10, help='测试轮数')
    
    args = parser.parse_args()
    
    if args.mode == 'quick':
        quick_test()
    elif args.mode == 'single':
        if not args.model:
            print("❌ 单模型测试需要指定 --model 参数")
            return
        test_trained_model(args.model, args.stage, args.episodes)
    elif args.mode == 'all':
        if not args.model_dir:
            print("❌ 全阶段测试需要指定 --model-dir 参数")
            return
        test_all_stages(args.model_dir)

if __name__ == "__main__":
    main()
