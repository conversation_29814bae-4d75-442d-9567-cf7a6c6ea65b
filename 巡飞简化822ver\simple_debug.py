"""
简单调试脚本 - 分析为什么巡飞弹不朝向目标
"""

import numpy as np
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from loitering_munition_environment import LoiteringMunitionEnvironment
from loitering_munition_dwa import LoiteringMunitionDWA
from environment_config import EnvironmentConfig

def simple_debug():
    """简单调试"""
    print("=== 简单调试开始 ===")
    
    try:
        # 创建环境
        config = EnvironmentConfig()
        env = LoiteringMunitionEnvironment(config)
        print("✅ 环境创建成功")
        
        # 创建DWA
        dwa = LoiteringMunitionDWA()
        print("✅ DWA创建成功")
        
        # 重置环境
        state = env.reset()
        print("✅ 环境重置成功")
        
        print(f"起点: {env.start}")
        print(f"目标: {env.goal}")
        print(f"当前状态: {env.state}")
        print(f"初始距离: {np.linalg.norm(env.start - env.goal):.2f}m")
        
        # 计算目标方向
        goal_direction = (env.goal - env.state[:3]) / np.linalg.norm(env.goal - env.state[:3])
        print(f"目标方向: {goal_direction}")
        
        # 测试DWA
        print("\n--- 测试DWA ---")
        safe_controls = dwa.generate_safe_controls(
            env.state, env.goal, env.obstacles + env.dynamic_obstacles,
            env.bounds, env.a_T_max, env.a_N_max
        )
        
        print(f"DWA生成安全控制数量: {len(safe_controls)}")
        
        if len(safe_controls) > 0:
            print("前5个安全控制:")
            for i, control in enumerate(safe_controls[:5]):
                print(f"  {i}: a_T={control[0]:.2f}, a_N={control[1]:.2f}, φ_dot={np.degrees(control[2]):.1f}°/s")
                
                # 预测运动方向
                try:
                    predicted_states = dwa._predict_trajectory(env.state, control, dwa.dt, 5)
                    if len(predicted_states) > 1:
                        movement = predicted_states[-1][:3] - predicted_states[0][:3]
                        if np.linalg.norm(movement) > 0:
                            movement_dir = movement / np.linalg.norm(movement)
                            alignment = np.dot(goal_direction, movement_dir)
                            print(f"     预测对齐度: {alignment:.3f} {'✅' if alignment > 0.1 else '❌'}")
                except Exception as e:
                    print(f"     预测失败: {e}")
        
        # 测试单步执行
        print("\n--- 测试单步执行 ---")
        if len(safe_controls) > 0:
            # 选择第一个安全控制
            control = safe_controls[0]
            print(f"选择控制: {control}")
            
            # 转换为归一化动作
            action = dwa.get_normalized_action(control)
            print(f"归一化动作: {action}")
            
            # 转换为环境控制输入
            control_input = np.array([
                action[0] * env.a_T_max,
                action[1] * env.a_N_max,
                action[2] * (np.pi/2)
            ])
            print(f"环境控制输入: {control_input}")
            
            # 记录执行前位置
            prev_pos = env.state[:3].copy()
            print(f"执行前位置: {prev_pos}")
            
            # 执行动作
            next_state, reward, done, info = env.step(control_input)
            
            # 分析结果
            new_pos = next_state[:3]
            movement = new_pos - prev_pos
            movement_dist = np.linalg.norm(movement)
            
            print(f"执行后位置: {new_pos}")
            print(f"实际移动: {movement}")
            print(f"移动距离: {movement_dist:.3f}m")
            
            if movement_dist > 1e-6:
                movement_dir = movement / movement_dist
                alignment = np.dot(goal_direction, movement_dir)
                print(f"实际对齐度: {alignment:.3f} {'✅' if alignment > 0.1 else '❌'}")
            else:
                print("几乎没有移动 ❌")
            
            print(f"奖励: {reward:.2f}")
            print(f"完成: {done}")
            print(f"信息: {info}")
            
            # 检查新距离
            new_dist = np.linalg.norm(new_pos - env.goal)
            dist_change = new_dist - np.linalg.norm(prev_pos - env.goal)
            print(f"新距离: {new_dist:.2f}m")
            print(f"距离变化: {dist_change:.3f}m {'✅' if dist_change < 0 else '❌'}")
        
        print("\n=== 调试完成 ===")
        
    except Exception as e:
        print(f"❌ 调试过程中出错: {e}")
        import traceback
        traceback.print_exc()

def test_dwa_parameters():
    """测试DWA参数"""
    print("\n=== 测试DWA参数 ===")
    
    try:
        dwa = LoiteringMunitionDWA()
        
        print(f"DWA参数:")
        print(f"  dt: {dwa.dt}")
        print(f"  predict_time: {dwa.predict_time}")
        print(f"  v_resolution: {dwa.v_resolution}")
        print(f"  yaw_rate_resolution: {dwa.yaw_rate_resolution}")
        print(f"  to_goal_cost_gain: {dwa.to_goal_cost_gain}")
        print(f"  speed_cost_gain: {dwa.speed_cost_gain}")
        print(f"  obstacle_cost_gain: {dwa.obstacle_cost_gain}")
        
        # 检查参数是否合理
        if dwa.dt <= 0:
            print("❌ dt参数异常")
        if dwa.predict_time <= 0:
            print("❌ predict_time参数异常")
        if dwa.to_goal_cost_gain <= 0:
            print("❌ to_goal_cost_gain参数异常 - 这可能导致不朝向目标")
        
    except Exception as e:
        print(f"❌ DWA参数测试失败: {e}")

if __name__ == "__main__":
    test_dwa_parameters()
    simple_debug()
