# DWA分辨率参数对比分析实验

## 🎯 实验目的

本实验旨在支撑论文中"仿真结果1-"部分，验证DWA（动态窗口算法）中不同分辨率设置对控制精度和计算效率的影响，为论文中参数选择的合理性提供实验数据支撑。

## 📊 实验设计

### 研究问题
- **控制精度影响**: 不同分辨率设置如何影响巡飞弹的轨迹控制精度？
- **计算效率权衡**: 分辨率参数与计算时间、动作集大小的关系？
- **参数优化**: 论文中推荐的分辨率设置是否在精度和效率间取得最优平衡？

### 测试配置组合

| 配置名称 | 切向加速度分辨率 | 法向加速度分辨率 | 倾斜角分辨率 | 特点 |
|---------|-----------------|-----------------|-------------|------|
| 粗分辨率 | 3.0 m/s² | 12.0 m/s² | 0.3 rad | 快速计算 |
| 中分辨率 | 2.0 m/s² | 8.0 m/s² | 0.2 rad | 平衡性能 |
| **论文设置** | **1.5 m/s²** | **6.0 m/s²** | **0.15 rad** | **推荐设置** |
| 细分辨率 | 1.0 m/s² | 4.0 m/s² | 0.1 rad | 高精度 |
| 极细分辨率 | 0.5 m/s² | 2.0 m/s² | 0.05 rad | 最高精度 |

### 评估指标

1. **计算效率指标**
   - 平均计算时间（秒）
   - 平均动作集大小
   
2. **控制性能指标**
   - 控制精度（基于目标达到误差）
   - 路径平滑度（基于轨迹曲率变化）
   - 目标达成率
   - 碰撞避免率

## 🚀 运行实验

### 方法1: 简化测试（推荐首次运行）

```bash
cd 巡飞简化ver
python test_dwa_resolution_simple.py
```

这个简化版本快速验证基本功能，生成初步对比结果。

### 方法2: 完整实验

```bash
cd 巡飞简化ver
python dwa_resolution_analysis.py
```

完整实验需要更长时间，但提供详细的统计分析和专业图表。

### 方法3: 使用启动器

```bash
cd 巡飞简化ver
python run_dwa_resolution_experiment.py
```

自动检查依赖并运行完整实验。

## 📈 实验输出

### 1. 数据文件
- `dwa_resolution_analysis_YYYYMMDD_HHMMSS.json` - 详细实验数据
- `dwa_resolution_summary_YYYYMMDD_HHMMSS.csv` - 摘要统计表格

### 2. 图表文件
- `dwa_resolution_analysis_YYYYMMDD_HHMMSS.png` - 综合对比分析图
- `dwa_parameter_table_YYYYMMDD_HHMMSS.png` - 参数对比表格
- `dwa_resolution_paper_figure_YYYYMMDD_HHMMSS.png` - 论文格式图表

### 3. 图表内容

#### (a) 计算效率分析
- 不同分辨率配置的计算时间对比
- 动作集大小随分辨率变化趋势

#### (b) 控制性能对比
- 控制精度指标对比
- 路径平滑度分析

#### (c) 效率vs精度权衡
- 散点图展示计算时间与控制精度的关系
- 论文设置在权衡中的位置

#### (d) 分辨率参数影响趋势
- 分辨率参数与性能指标的关系曲线

## 🔬 预期结果

### 主要验证点

1. **计算效率递减**: 分辨率越细，计算时间越长，动作集越大
2. **精度递增**: 分辨率越细，控制精度越高，但存在收益递减
3. **最优平衡**: 论文中的设置在计算效率和控制精度间取得良好平衡
4. **实用性验证**: 极细分辨率虽然精度高，但计算负担过大

### 支撑论文观点

- 验证论文表7中DWA参数设置的合理性
- 为"分辨率对控制精度和训练效率影响"提供定量数据
- 证明参数选择平衡了计算效率与控制精度的要求

## 🛠️ 故障排除

### 常见问题

1. **导入错误**: 检查是否在正确的目录下运行
2. **内存不足**: 简化测试版本需要更少内存
3. **图表显示问题**: 检查matplotlib和中文字体设置

### 环境要求
- Python 3.7+
- NumPy, Matplotlib, Pandas
- 中文字体支持（SimHei, Microsoft YaHei, 或 Arial Unicode MS）

## 📚 实验理论基础

### DWA算法原理
动态窗口算法通过在速度空间中搜索可行动作集合，评估每个动作的安全性和目标导向性。分辨率参数直接影响搜索空间的精细程度：

- **分辨率过粗**: 搜索空间稀疏，可能错过最优解
- **分辨率过细**: 搜索空间过密，计算负担重，实时性差
- **合适分辨率**: 在精度和效率间取得平衡

### 巡飞弹约束条件
- 最小/最大速度约束（15-60 m/s）
- 最大加速度约束（切向8 m/s²，法向39.24 m/s²）
- 最大倾斜角约束（60°）
- 最小安全距离约束（5m）

## 🎯 实验价值

这个实验为论文提供了：

1. **定量验证**: 用数据支撑参数选择的合理性
2. **对比分析**: 展示不同设置的性能差异
3. **实用指导**: 为实际应用提供参数调优建议
4. **理论支撑**: 验证DWA算法在巡飞弹控制中的有效性

通过这个实验，读者可以清晰地看到论文中DWA参数设置的科学性和实用性。







