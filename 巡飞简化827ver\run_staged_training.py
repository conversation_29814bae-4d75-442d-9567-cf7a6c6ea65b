"""
主训练脚本 - 融合版本
执行巡飞弹分阶段训练的主入口
"""

import argparse
import sys
import os
import numpy as np
import torch

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from staged_training_framework import LoiteringMunitionStagedTrainer
from environment_config import print_config_summary

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='巡飞弹分阶段训练 - 融合版本')
    parser.add_argument('--start-stage', type=int, default=1, choices=[1, 2, 3],
                       help='开始阶段 (1: 简单环境, 2: 复杂环境, 3: 动态环境)')
    parser.add_argument('--end-stage', type=int, default=3, choices=[1, 2, 3],
                       help='结束阶段 (1: 简单环境, 2: 复杂环境, 3: 动态环境)')
    parser.add_argument('--seed', type=int, default=42,
                       help='随机种子')
    parser.add_argument('--viz-interval', type=int, default=10,
                       help='可视化间隔（每隔多少个episode生成一次轨迹图）')
    parser.add_argument('--show-config', action='store_true',
                       help='显示配置信息')
    parser.add_argument('--gpu', action='store_true',
                       help='使用GPU加速（如果可用）')
    
    args = parser.parse_args()
    
    # 显示配置信息
    if args.show_config:
        print_config_summary()
        return
    
    # 检查GPU可用性
    if args.gpu and torch.cuda.is_available():
        print(f"🚀 使用GPU加速: {torch.cuda.get_device_name()}")
    elif args.gpu:
        print("⚠️  GPU不可用，使用CPU")
    else:
        print("💻 使用CPU")
    
    # 验证阶段参数
    if args.start_stage > args.end_stage:
        print("❌ 错误：开始阶段不能大于结束阶段")
        return
    
    # 显示训练计划
    print("\n📋 分阶段训练计划:")
    print("=" * 40)
    stage_names = {1: "简单环境", 2: "复杂环境", 3: "动态环境"}
    for stage in range(args.start_stage, args.end_stage + 1):
        print(f"阶段 {stage}: {stage_names[stage]}")
    print()
    
    try:
        # 创建训练器
        trainer = LoiteringMunitionStagedTrainer(
            start_stage=args.start_stage,
            end_stage=args.end_stage,
            seed=args.seed,
            visualization_interval=args.viz_interval
        )
        
        # 执行训练
        results, final_controller = trainer.run_staged_training()
        
        print("\n🎉 训练完成!")
        print(f"📁 结果保存在: {trainer.output_dir}")
        
        # 显示最终结果摘要
        print("\n📊 最终结果摘要:")
        print("=" * 40)
        for stage_key, stage_result in results["stages"].items():
            if "error" not in stage_result:
                print(f"{stage_key}: 成功率 {stage_result['success_rate']:.2%}, "
                      f"平均奖励 {stage_result['final_avg_reward']:.2f}")
            else:
                print(f"{stage_key}: 训练失败 - {stage_result['error']}")
        
        return results, final_controller
        
    except KeyboardInterrupt:
        print("\n⚠️  训练被用户中断")
        return None
    except Exception as e:
        print(f"\n❌ 训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None

def run_stage1_full_training():
    """第一阶段完整训练函数"""
    print("🎯 第一阶段完整训练模式")
    print("=" * 40)

    try:
        # 创建第一阶段完整训练器
        trainer = LoiteringMunitionStagedTrainer(
            start_stage=1,
            end_stage=1,
            seed=42,
            visualization_interval=10  # 每10个episode生成图表
        )

        # 修改配置为第一阶段完整训练
        from environment_config import TRAINING_STAGES
        TRAINING_STAGES["stage1_simple"]["random_episodes"] = 150
        TRAINING_STAGES["stage1_simple"]["fixed_episodes"] = 50
        TRAINING_STAGES["stage1_simple"]["total_episodes"] = 200

        print("🚀 第一阶段完整训练配置:")
        print("  📊 随机场景探索: 150 episodes")
        print("  🎯 固定场景强化: 50 episodes")
        print("  📈 总计: 200 episodes")
        print("  🎨 可视化间隔: 每10个episode生成轨迹图")
        print("  ⏱️  预计训练时间: 约10-15小时")

        # 确认开始训练
        confirm = input("\n确认开始第一阶段完整训练? (y/N): ").strip().lower()
        if confirm != 'y':
            print("❌ 训练已取消")
            return None

        print("\n🚀 开始第一阶段完整训练...")

        # 执行训练
        results, final_controller = trainer.run_staged_training()

        print("\n✅ 第一阶段完整训练完成!")
        return results, final_controller

    except Exception as e:
        print(f"\n❌ 第一阶段训练失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def run_single_episode_test():
    """单回合测试函数"""
    print("🎯 单回合测试模式")
    print("=" * 30)

    try:
        # 创建测试训练器（只运行1个episode）
        trainer = LoiteringMunitionStagedTrainer(
            start_stage=1,
            end_stage=1,
            seed=42,
            visualization_interval=1  # 每个episode都生成图表
        )

        # 修改配置为单回合测试
        from environment_config import TRAINING_STAGES
        TRAINING_STAGES["stage1_simple"]["random_episodes"] = 1
        TRAINING_STAGES["stage1_simple"]["fixed_episodes"] = 0
        TRAINING_STAGES["stage1_simple"]["total_episodes"] = 1

        print("⚡ 使用单回合测试配置（1个episode）")
        print("📊 将生成详细的轨迹可视化图表")

        # 执行训练
        results, final_controller = trainer.run_staged_training()

        print("\n✅ 单回合测试完成!")
        return results, final_controller

    except Exception as e:
        print(f"\n❌ 单回合测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def run_quick_test():
    """快速测试函数"""
    print("🧪 快速测试模式")
    print("=" * 30)

    try:
        # 创建测试训练器（只训练第一阶段的少量episode）
        trainer = LoiteringMunitionStagedTrainer(
            start_stage=1,
            end_stage=1,
            seed=42,
            visualization_interval=3
        )

        # 修改配置为快速测试
        from environment_config import TRAINING_STAGES
        TRAINING_STAGES["stage1_simple"]["random_episodes"] = 5
        TRAINING_STAGES["stage1_simple"]["fixed_episodes"] = 3
        TRAINING_STAGES["stage1_simple"]["total_episodes"] = 8

        print("⚡ 使用快速测试配置（8个episode）")

        # 执行训练
        results, final_controller = trainer.run_staged_training()

        print("\n✅ 快速测试完成!")
        return results, final_controller

    except Exception as e:
        print(f"\n❌ 快速测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def run_demo():
    """演示模式"""
    print("🎭 演示模式")
    print("=" * 30)
    
    # 显示配置信息
    print_config_summary()
    
    print("\n选择运行模式:")
    print("1. 完整训练（所有三个阶段）")
    print("2. 第一阶段完整训练（150+50=200 episodes）")
    print("3. 单阶段训练")
    print("4. 单回合测试（1个episode）")
    print("5. 快速测试（8个episodes）")
    print("6. 退出")
    
    try:
        choice = input("\n请输入选择 (1-6): ").strip()

        if choice == "1":
            print("\n🚀 开始完整训练...")
            return main()
        elif choice == "2":
            return run_stage1_full_training()
        elif choice == "3":
            stage = input("请输入阶段号 (1-3): ").strip()
            if stage in ["1", "2", "3"]:
                stage_num = int(stage)
                trainer = LoiteringMunitionStagedTrainer(
                    start_stage=stage_num,
                    end_stage=stage_num,
                    seed=42,
                    visualization_interval=10
                )
                return trainer.run_staged_training()
            else:
                print("❌ 无效的阶段号")
                return None
        elif choice == "4":
            return run_single_episode_test()
        elif choice == "5":
            return run_quick_test()
        elif choice == "6":
            print("👋 再见!")
            return None
        else:
            print("❌ 无效的选择")
            return None
            
    except KeyboardInterrupt:
        print("\n👋 再见!")
        return None

if __name__ == "__main__":
    # 检查命令行参数
    if len(sys.argv) == 1:
        # 没有命令行参数，运行演示模式
        run_demo()
    elif "--demo" in sys.argv:
        # 明确指定演示模式
        run_demo()
    elif "--single-episode" in sys.argv:
        # 单回合测试模式
        run_single_episode_test()
    elif "--quick-test" in sys.argv:
        # 快速测试模式
        run_quick_test()
    else:
        # 正常命令行模式
        main()
