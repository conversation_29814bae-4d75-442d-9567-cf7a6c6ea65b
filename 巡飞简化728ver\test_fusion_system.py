"""
融合系统测试脚本
验证巡飞弹分阶段训练系统的各个组件功能
"""

import numpy as np
import torch
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from loitering_munition_environment import LoiteringMunitionEnvironment
from loitering_munition_dwa import LoiteringMunitionDWA
from td3_network import StabilizedTD3Controller
from environment_config import (
    get_environment_config, get_training_stage_config, get_td3_config,
    get_loitering_munition_config, get_dwa_config, print_config_summary
)

def test_environment():
    """测试环境模块"""
    print("🧪 测试环境模块...")
    
    try:
        # 测试简单环境
        env_config = get_environment_config("stage1_simple")
        lm_config = get_loitering_munition_config()
        
        env = LoiteringMunitionEnvironment(
            bounds=lm_config['bounds'],
            environment_config=env_config,
            reward_type='simplified'
        )
        
        # 重置环境
        state = env.reset()
        print(f"  ✅ 环境重置成功，状态维度: {state.shape}")
        print(f"  📍 起点: {env.start}")
        print(f"  🎯 目标: {env.goal}")
        print(f"  🚧 静态障碍物数量: {len(env.obstacles)}")
        print(f"  🔄 动态障碍物数量: {len(env.dynamic_obstacles)}")
        
        # 测试步进
        action = np.array([0.0, 0.0, 0.0])  # 无控制输入
        next_state, reward, done, info = env.step(action)
        print(f"  ✅ 环境步进成功，奖励: {reward:.3f}")
        
        # 测试场景保存和加载
        scenario = env.save_scenario()
        env2 = LoiteringMunitionEnvironment(
            bounds=lm_config['bounds'],
            environment_config=env_config,
            reward_type='simplified'
        )
        state2 = env2.reset(scenario)
        print(f"  ✅ 场景保存和加载成功")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 环境测试失败: {e}")
        return False

def test_dwa_controller():
    """测试DWA控制器"""
    print("\n🧪 测试DWA控制器...")
    
    try:
        # 创建DWA控制器
        dwa_config = get_dwa_config()
        dwa = LoiteringMunitionDWA(dt=0.1)
        
        # 创建测试环境
        env_config = get_environment_config("stage1_simple")
        lm_config = get_loitering_munition_config()
        env = LoiteringMunitionEnvironment(
            bounds=lm_config['bounds'],
            environment_config=env_config
        )
        state = env.reset()
        
        # 测试安全控制集生成
        safe_controls = dwa.generate_safe_control_set(
            env.state, env.obstacles + env.dynamic_obstacles, env.goal, max_actions=10
        )
        print(f"  ✅ 生成安全控制集，数量: {len(safe_controls)}")
        
        if safe_controls:
            # 测试控制评价
            control = safe_controls[0]
            score = dwa.evaluate_control(control, env.state, env.goal, env.obstacles + env.dynamic_obstacles)
            print(f"  ✅ 控制评价成功，分数: {score:.3f}")
            
            # 测试最优控制选择
            best_control = dwa.select_best_control(env.state, env.obstacles + env.dynamic_obstacles, env.goal)
            print(f"  ✅ 最优控制选择成功: {best_control}")
            
            # 测试归一化和反归一化
            normalized = dwa.get_normalized_action(best_control)
            denormalized = dwa.denormalize_action(normalized)
            print(f"  ✅ 动作归一化测试成功")
        
        return True
        
    except Exception as e:
        print(f"  ❌ DWA控制器测试失败: {e}")
        return False

def test_td3_network():
    """测试TD3网络"""
    print("\n🧪 测试TD3网络...")
    
    try:
        # 创建TD3控制器
        td3_config = get_td3_config()
        controller = StabilizedTD3Controller(td3_config)
        
        # 测试动作选择
        state = np.random.randn(td3_config['state_dim'])
        action = controller.select_action(state, noise=0.1)
        print(f"  ✅ 动作选择成功，动作维度: {action.shape}")
        
        # 测试经验存储
        next_state = np.random.randn(td3_config['state_dim'])
        reward = np.random.randn()
        done = False
        
        controller.replay_buffer.add(state, action, reward, next_state, done)
        print(f"  ✅ 经验存储成功，缓冲区大小: {controller.replay_buffer.size()}")
        
        # 添加更多经验以测试训练
        for _ in range(100):
            state = np.random.randn(td3_config['state_dim'])
            action = np.random.randn(td3_config['action_dim'])
            reward = np.random.randn()
            next_state = np.random.randn(td3_config['state_dim'])
            done = np.random.choice([True, False])
            controller.replay_buffer.add(state, action, reward, next_state, done)
        
        # 测试训练
        controller.train(batch_size=32)
        print(f"  ✅ 网络训练成功")
        
        # 测试模型保存和加载
        model_path = "test_model.pth"
        controller.save(model_path)
        
        controller2 = StabilizedTD3Controller(td3_config)
        controller2.load(model_path)
        print(f"  ✅ 模型保存和加载成功")
        
        # 清理测试文件
        if os.path.exists(model_path):
            os.remove(model_path)
        
        return True
        
    except Exception as e:
        print(f"  ❌ TD3网络测试失败: {e}")
        return False

def test_integration():
    """测试系统集成"""
    print("\n🧪 测试系统集成...")
    
    try:
        # 创建所有组件
        env_config = get_environment_config("stage1_simple")
        lm_config = get_loitering_munition_config()
        td3_config = get_td3_config()
        
        env = LoiteringMunitionEnvironment(
            bounds=lm_config['bounds'],
            environment_config=env_config,
            reward_type='simplified'
        )
        
        dwa = LoiteringMunitionDWA(dt=lm_config['dt'])
        controller = StabilizedTD3Controller(td3_config)
        
        # 运行一个完整的episode
        state = env.reset()
        total_reward = 0
        steps = 0
        max_steps = 50  # 限制步数以加快测试
        
        print(f"  🎯 目标距离: {np.linalg.norm(env.state[:3] - env.goal):.2f}m")
        
        while steps < max_steps:
            # 使用DWA生成安全动作
            safe_controls = dwa.generate_safe_control_set(
                env.state, env.obstacles + env.dynamic_obstacles, env.goal, max_actions=5
            )
            
            if safe_controls:
                control = safe_controls[0]
                action = dwa.get_normalized_action(control)
            else:
                action = np.array([0.0, 0.0, 0.0])
            
            # 执行动作
            control_input = dwa.denormalize_action(action)
            next_state, reward, done, info = env.step(control_input)
            total_reward += reward
            steps += 1
            
            # 存储经验
            controller.replay_buffer.add(state, action, reward, next_state, done)
            
            if done:
                break
            
            state = next_state
        
        print(f"  ✅ 完整episode运行成功")
        print(f"  📊 步数: {steps}, 总奖励: {total_reward:.2f}")
        print(f"  🎯 最终距离: {np.linalg.norm(env.state[:3] - env.goal):.2f}m")
        
        if info.get('success', False):
            print(f"  🏆 任务成功完成!")
        elif info.get('collision', False):
            print(f"  💥 发生碰撞")
        elif info.get('out_of_bounds', False):
            print(f"  🚫 超出边界")
        else:
            print(f"  ⏰ 达到最大步数")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 系统集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_all_environments():
    """测试所有环境配置"""
    print("\n🧪 测试所有环境配置...")
    
    env_names = ["stage1_simple", "stage2_complex", "stage3_dynamic"]
    lm_config = get_loitering_munition_config()
    
    for env_name in env_names:
        try:
            env_config = get_environment_config(env_name)
            env = LoiteringMunitionEnvironment(
                bounds=lm_config['bounds'],
                environment_config=env_config,
                reward_type='simplified'
            )
            
            state = env.reset()
            print(f"  ✅ {env_name}: 静态障碍物 {len(env.obstacles)}, 动态障碍物 {len(env.dynamic_obstacles)}")
            
        except Exception as e:
            print(f"  ❌ {env_name} 测试失败: {e}")
            return False
    
    return True

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始融合系统测试")
    print("=" * 50)
    
    # 显示配置信息
    print_config_summary()
    
    tests = [
        ("环境模块", test_environment),
        ("DWA控制器", test_dwa_controller),
        ("TD3网络", test_td3_network),
        ("所有环境配置", test_all_environments),
        ("系统集成", test_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果摘要
    print(f"\n{'='*20} 测试结果摘要 {'='*20}")
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 测试统计: {passed}/{len(results)} 通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！融合系统准备就绪。")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关组件。")
        return False

if __name__ == "__main__":
    if len(sys.argv) > 1:
        test_name = sys.argv[1].lower()
        if test_name == "env":
            test_environment()
        elif test_name == "dwa":
            test_dwa_controller()
        elif test_name == "td3":
            test_td3_network()
        elif test_name == "integration":
            test_integration()
        elif test_name == "all-env":
            test_all_environments()
        else:
            print("可用的测试: env, dwa, td3, integration, all-env")
    else:
        run_all_tests()
