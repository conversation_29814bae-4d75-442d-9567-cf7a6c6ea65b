"""
DWA分辨率实验简化测试版本
验证实验框架基本功能
"""

import numpy as np
import time
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def test_basic_imports():
    """测试基本导入"""
    print("🔍 测试模块导入...")
    
    try:
        from loitering_munition_dwa import LoiteringMunitionDWA
        print("✅ DWA模块导入成功")
    except ImportError as e:
        print(f"❌ DWA模块导入失败: {e}")
        return False
        
    try:
        from loitering_munition_environment import LoiteringMunitionEnvironment
        print("✅ 环境模块导入成功")
    except ImportError as e:
        print(f"❌ 环境模块导入失败: {e}")
        return False
        
    try:
        import environment_config
        print("✅ 配置模块导入成功")
    except ImportError as e:
        print(f"❌ 配置模块导入失败: {e}")
        return False
        
    return True

def test_dwa_resolution_comparison():
    """测试DWA分辨率对比"""
    print("\n🧪 测试DWA分辨率对比...")
    
    # 导入模块
    from loitering_munition_dwa import LoiteringMunitionDWA
    from loitering_munition_environment import LoiteringMunitionEnvironment
    from environment_config import get_environment_config
    
    # 定义两个分辨率配置进行对比
    configs = {
        "粗分辨率": {
            "a_T_resolution": 4.0,
            "a_N_resolution": 15.0,
            "mu_resolution": 0.5
        },
        "论文设置": {
            "a_T_resolution": 1.5,
            "a_N_resolution": 6.0,
            "mu_resolution": 0.15
        }
    }
    
    results = {}
    
    for config_name, config in configs.items():
        print(f"\n  测试配置: {config_name}")
        
        # 创建环境
        env_config = get_environment_config("test_simple")
        env = LoiteringMunitionEnvironment(environment_config=env_config)
        env.reset()
        
        # 创建DWA控制器
        dwa = LoiteringMunitionDWA(dt=0.1)
        dwa.a_T_resolution = config["a_T_resolution"]
        dwa.a_N_resolution = config["a_N_resolution"]
        dwa.mu_resolution = config["mu_resolution"]
        
        # 测试单步性能
        current_state = env.state
        obstacles = env.obstacles  # 直接访问障碍物属性
        goal = env.goal
        
        # 计算动作集并测量时间
        start_time = time.time()
        safe_actions = dwa.generate_safe_control_set(current_state, obstacles, goal, max_actions=20)
        computation_time = time.time() - start_time
        
        action_set_size = len(safe_actions)
        
        results[config_name] = {
            "computation_time": computation_time,
            "action_set_size": action_set_size,
            "config": config
        }
        
        print(f"    计算时间: {computation_time:.4f} 秒")
        print(f"    动作集大小: {action_set_size}")
        
    return results

def test_trajectory_generation():
    """测试轨迹生成"""
    print("\n🛩️ 测试轨迹生成...")
    
    from loitering_munition_dwa import LoiteringMunitionDWA
    from loitering_munition_environment import LoiteringMunitionEnvironment
    from environment_config import get_environment_config
    
    # 创建环境
    env_config = get_environment_config("test_simple")
    env = LoiteringMunitionEnvironment(environment_config=env_config)
    env.reset()
    
    # 创建DWA控制器（使用论文设置）
    dwa = LoiteringMunitionDWA(dt=0.1)
    dwa.a_T_resolution = 1.5
    dwa.a_N_resolution = 6.0
    dwa.mu_resolution = 0.15
    
    # 生成短轨迹
    trajectory = []
    max_steps = 50
    
    for step in range(max_steps):
        current_state = env.state
        trajectory.append(current_state[:3].copy())
        
        obstacles = env.obstacles
        goal = env.goal
        
        # 生成安全动作
        safe_actions = dwa.generate_safe_control_set(current_state, obstacles, goal, max_actions=10)
        
        if len(safe_actions) == 0:
            print(f"    第{step}步无可行动作，停止")
            break
            
        # 执行最优动作
        best_action = safe_actions[0]
        next_state, reward, done, info = env.step(best_action)
        
        if done:
            print(f"    第{step}步完成任务")
            break
            
    trajectory = np.array(trajectory)
    print(f"    生成轨迹点数: {len(trajectory)}")
    
    # 计算基本指标
    if len(trajectory) > 1:
        total_distance = np.sum(np.linalg.norm(np.diff(trajectory, axis=0), axis=1))
        goal_distance = np.linalg.norm(trajectory[-1] - env.goal)
        print(f"    总飞行距离: {total_distance:.1f} m")
        print(f"    最终目标距离: {goal_distance:.1f} m")
        
    return trajectory

def generate_simple_comparison_plot(results):
    """生成简单对比图"""
    print("\n📊 生成对比图表...")
    
    configs = list(results.keys())
    comp_times = [results[c]["computation_time"] for c in configs]
    action_sizes = [results[c]["action_set_size"] for c in configs]
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # 计算时间对比
    bars1 = ax1.bar(configs, comp_times, color=['#FF6B6B', '#4ECDC4'])
    ax1.set_title('计算时间对比', fontweight='bold')
    ax1.set_ylabel('计算时间 (秒)')
    ax1.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, time_val in zip(bars1, comp_times):
        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.0001,
                f'{time_val:.4f}', ha='center', va='bottom')
    
    # 动作集大小对比
    bars2 = ax2.bar(configs, action_sizes, color=['#FF6B6B', '#4ECDC4'])
    ax2.set_title('动作集大小对比', fontweight='bold')
    ax2.set_ylabel('动作集大小')
    ax2.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, size_val in zip(bars2, action_sizes):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                f'{size_val}', ha='center', va='bottom')
    
    plt.suptitle('DWA分辨率对比测试结果', fontsize=14, fontweight='bold')
    plt.tight_layout()
    
    filename = "dwa_resolution_simple_test.png"
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"    图表已保存: {filename}")
    
    plt.show()

def main():
    """主函数"""
    print("🎯 DWA分辨率实验简化测试")
    print("=" * 40)
    
    # 1. 测试基本导入
    if not test_basic_imports():
        print("❌ 基本导入测试失败，请检查依赖")
        return
    
    # 2. 测试DWA分辨率对比
    try:
        results = test_dwa_resolution_comparison()
        print("\n✅ DWA分辨率对比测试成功")
    except Exception as e:
        print(f"❌ DWA分辨率对比测试失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 3. 测试轨迹生成
    try:
        trajectory = test_trajectory_generation()
        print("✅ 轨迹生成测试成功")
    except Exception as e:
        print(f"❌ 轨迹生成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 4. 生成对比图表
    try:
        generate_simple_comparison_plot(results)
        print("✅ 图表生成成功")
    except Exception as e:
        print(f"❌ 图表生成失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print("\n🎉 所有测试完成！")
    print("\n📈 主要发现:")
    for config_name, result in results.items():
        print(f"  {config_name}:")
        print(f"    - 计算时间: {result['computation_time']:.4f} 秒")
        print(f"    - 动作集大小: {result['action_set_size']}")
        
    print("\n💡 这个简化测试验证了DWA分辨率参数对计算效率的影响")
    print("   可以运行完整实验 'dwa_resolution_analysis.py' 获得详细分析")

if __name__ == "__main__":
    main()
