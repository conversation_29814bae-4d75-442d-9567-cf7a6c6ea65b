"""
简化的训练结果测试 - 巡飞简化ver
验证训练结果和生成简化ver1格式文件
"""

import os
import json
import pickle
import pandas as pd
import numpy as np
import shutil
import glob
from datetime import datetime

def main():
    """主函数"""
    print("🧪 简化训练结果测试工具")
    print("=" * 50)
    
    # 指定训练目录
    training_dir = "loitering_munition_staged_training_20250720_055444"
    
    if not os.path.exists(training_dir):
        print(f"❌ 训练目录不存在: {training_dir}")
        return False
    
    print(f"✅ 找到训练目录: {training_dir}")
    
    # 检查文件
    files_to_check = [
        'staged_training_results.json',
        'staged_training_data.pkl',
        'stage_1_model.pth'
    ]
    
    print(f"\n📊 检查训练文件:")
    for file in files_to_check:
        file_path = os.path.join(training_dir, file)
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"  ✅ {file} ({size} bytes)")
        else:
            print(f"  ❌ {file} (缺失)")
    
    # 加载JSON数据
    json_file = os.path.join(training_dir, 'staged_training_results.json')
    if os.path.exists(json_file):
        with open(json_file, 'r', encoding='utf-8') as f:
            json_data = json.load(f)
        
        print(f"\n📈 训练性能分析:")
        if 'stages' in json_data:
            for stage_name, stage_data in json_data['stages'].items():
                if 'error' not in stage_data:
                    print(f"  {stage_name}:")
                    print(f"    总episodes: {stage_data.get('total_episodes', 'N/A')}")
                    print(f"    成功率: {stage_data.get('success_rate', 0):.2%}")
                    print(f"    平均奖励: {stage_data.get('final_avg_reward', 0):.1f}")
    
    # 生成简化ver1格式文件
    output_dir = f"{training_dir}_simplified_ver1_format"
    print(f"\n🔄 生成简化ver1格式文件到: {output_dir}")
    
    if os.path.exists(output_dir):
        print(f"  目录已存在，跳过生成")
    else:
        os.makedirs(output_dir, exist_ok=True)
        
        # 复制模型文件
        model_src = os.path.join(training_dir, 'stage_1_model.pth')
        model_dst = os.path.join(output_dir, 'simplified_reward_model.pth')
        if os.path.exists(model_src):
            shutil.copy2(model_src, model_dst)
            print(f"  ✅ 复制模型文件")
        
        # 复制数据文件
        pkl_src = os.path.join(training_dir, 'staged_training_data.pkl')
        pkl_dst = os.path.join(output_dir, 'simplified_reward_training_data.pkl')
        if os.path.exists(pkl_src):
            shutil.copy2(pkl_src, pkl_dst)
            print(f"  ✅ 复制数据文件")
        
        # 生成JSON报告
        if os.path.exists(json_file):
            simplified_report = {
                'training_config': {
                    'total_episodes': json_data.get('summary', {}).get('total_episodes', 0),
                    'training_time': json_data.get('summary', {}).get('total_training_time', 0),
                    'reward_type': 'simplified'
                },
                'performance_metrics': {},
                'stage_results': {}
            }
            
            if 'stages' in json_data:
                for stage_name, stage_data in json_data['stages'].items():
                    if 'error' not in stage_data:
                        simplified_report['stage_results'][stage_name] = {
                            'success_rate': stage_data.get('success_rate', 0),
                            'average_reward': stage_data.get('final_avg_reward', 0),
                            'total_episodes': stage_data.get('total_episodes', 0)
                        }
            
            report_path = os.path.join(output_dir, 'simplified_reward_training_report.json')
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(simplified_report, f, indent=2, ensure_ascii=False)
            print(f"  ✅ 生成JSON报告")
        
        # 生成CSV文件
        if 'stages' in json_data:
            rewards_data = []
            episode_count = 0
            
            for stage_name, stage_data in json_data['stages'].items():
                if 'error' not in stage_data and 'all_rewards' in stage_data:
                    for reward in stage_data['all_rewards']:
                        episode_count += 1
                        rewards_data.append({
                            'episode': episode_count,
                            'reward': reward,
                            'stage': stage_name
                        })
            
            if rewards_data:
                df = pd.DataFrame(rewards_data)
                csv_path = os.path.join(output_dir, 'simplified_reward_training_rewards.csv')
                df.to_csv(csv_path, index=False)
                print(f"  ✅ 生成CSV文件 ({len(rewards_data)} 条记录)")
        
        # 复制轨迹图片
        trajectory_files = glob.glob(os.path.join(training_dir, '*_3d_trajectory.png'))
        copied_count = 0
        
        for traj_file in trajectory_files:
            filename = os.path.basename(traj_file)
            # 转换文件名格式
            new_filename = filename.replace('Stage1_RandomExplore', 'Phase1_Random')
            new_filename = new_filename.replace('Stage1_FixedTrain', 'Phase1_Fixed')
            
            new_path = os.path.join(output_dir, new_filename)
            shutil.copy2(traj_file, new_path)
            copied_count += 1
        
        print(f"  ✅ 复制轨迹图片: {copied_count} 个")
        
        # 复制总结图片
        summary_files = glob.glob(os.path.join(training_dir, '*summary*.png'))
        if summary_files:
            summary_dst = os.path.join(output_dir, 'training_summary.png')
            shutil.copy2(summary_files[0], summary_dst)
            print(f"  ✅ 复制总结图片")
    
    # 检查生成的文件
    print(f"\n📁 检查生成的简化ver1格式文件:")
    expected_files = [
        'simplified_reward_model.pth',
        'simplified_reward_training_data.pkl',
        'simplified_reward_training_report.json',
        'simplified_reward_training_rewards.csv',
        'training_summary.png'
    ]
    
    success_count = 0
    for file in expected_files:
        file_path = os.path.join(output_dir, file)
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"  ✅ {file} ({size} bytes)")
            success_count += 1
        else:
            print(f"  ❌ {file} (缺失)")
    
    # 检查轨迹图片
    trajectory_files = glob.glob(os.path.join(output_dir, 'Phase*_3d_trajectory.png'))
    print(f"  📊 轨迹图片: {len(trajectory_files)} 个")
    
    print(f"\n🎯 测试结果:")
    print(f"  预期核心文件: {len(expected_files)}")
    print(f"  成功生成: {success_count}")
    print(f"  轨迹图片: {len(trajectory_files)}")
    
    if success_count >= 4:  # 至少4个核心文件
        print(f"\n🎉 测试成功！简化ver1格式文件生成完成")
        print(f"📁 输出目录: {output_dir}")
        print(f"\n💡 与简化ver1的文件格式完全兼容:")
        print(f"  ✅ simplified_reward_model.pth")
        print(f"  ✅ simplified_reward_training_data.pkl")
        print(f"  ✅ simplified_reward_training_report.json")
        print(f"  ✅ simplified_reward_training_rewards.csv")
        print(f"  ✅ Phase1_Random_episode_XXX_3d_trajectory.png")
        print(f"  ✅ Phase1_Fixed_episode_XXX_3d_trajectory.png")
        print(f"  ✅ training_summary.png")
        return True
    else:
        print(f"\n⚠️ 测试部分成功，有些文件生成失败")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print(f"\n✅ 所有测试通过！训练结果已成功转换为简化ver1格式")
    else:
        print(f"\n❌ 测试失败，请检查相关问题")
