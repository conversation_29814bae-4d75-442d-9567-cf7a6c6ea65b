# 简化奖励函数专用训练脚本使用说明

## 📋 概述

`train_simplified_reward.py` 是从对比训练代码中抽离出来的专用简化奖励函数训练脚本。该脚本专门使用简化的奖励函数进行训练，并在训练过程中定期生成三维轨迹图。

## 🎯 主要特性

- **专用简化奖励函数**: 使用论文风格的简化奖励函数设计
- **200次训练**: 默认训练200个episodes
- **定期3D可视化**: 每隔指定间隔生成三维轨迹图
- **完整数据保存**: 保存模型、训练数据、报告和可视化结果
- **实时训练监控**: 显示训练进度和统计信息

## 🚀 使用方法

### 基本使用
```bash
cd 简化ver
python train_simplified_reward.py
```

### 自定义参数
```bash
# 训练300个episodes，每5个episodes生成一次3D图
python train_simplified_reward.py --episodes 300 --viz-interval 5

# 使用不同的随机种子
python train_simplified_reward.py --seed 123

# 完整参数示例
python train_simplified_reward.py --episodes 200 --seed 42 --viz-interval 10
```

### 参数说明
- `--episodes`: 训练episodes数量 (默认: 200)
- `--seed`: 随机种子 (默认: 42)
- `--viz-interval`: 3D轨迹图生成间隔 (默认: 每10个episodes)

## 📊 输出结果

训练完成后会在 `simplified_reward_training_YYYYMMDD_HHMMSS/` 目录下生成：

### 1. 模型文件
- `simplified_reward_model.pth`: 训练好的TD3模型

### 2. 数据文件
- `simplified_reward_training_data.pkl`: 完整训练数据 (Python pickle格式)
- `simplified_reward_training_report.json`: 详细训练报告 (JSON格式)
- `simplified_reward_training_rewards.csv`: 奖励数据 (CSV格式)

### 3. 可视化文件
- `episode_001_3d_trajectory.png`: Episode 1的3D轨迹图
- `episode_011_3d_trajectory.png`: Episode 11的3D轨迹图
- `episode_021_3d_trajectory.png`: Episode 21的3D轨迹图
- ... (根据viz-interval参数生成)
- `training_summary.png`: 训练总结图表

## 🎨 3D轨迹图说明

每个3D轨迹图包含：
- **绿色圆点**: 起始位置
- **红色星号**: 目标位置
- **灰色球体**: 障碍物
- **彩色轨迹线**: 无人机飞行轨迹
  - 蓝色实线: 成功到达目标
  - 红色虚线: 发生碰撞
  - 橙色点线: 超时

## 📈 简化奖励函数特点

与复杂奖励函数相比，简化奖励函数具有以下特点：

### 1. 明确的终止信号
- 成功到达: +100
- 碰撞: -100
- 越界: -100

### 2. 核心距离奖励
- 距离目标的负值: -distance/50.0
- 强烈鼓励接近目标

### 3. 效率激励
- 每步小惩罚: -0.1
- 鼓励快速完成任务

### 4. 最小安全约束
- 仅在真正接近危险时惩罚
- 避免过度复杂的安全计算

## 📊 训练监控

训练过程中会显示：
```
Episode  15: Reward=  -45.2, Success=0.133, Steps=156, Result=timeout
Episode  20: Reward=  -38.7, Success=0.150, Steps=142, Result=success
```

- **Reward**: 最近5个episodes的平均奖励
- **Success**: 当前成功率
- **Steps**: 最近5个episodes的平均步数
- **Result**: 当前episode的结果

## 🔧 与原始train_dwa_rl.py的区别

1. **专用奖励函数**: 强制使用simplified奖励函数
2. **定期3D可视化**: 自动生成多个3D轨迹图
3. **简化输出**: 专注于简化奖励函数的性能
4. **独立运行**: 不依赖对比训练框架

## 📝 注意事项

1. **内存使用**: 每个3D图会消耗一定内存，建议合理设置viz-interval
2. **训练时间**: 200个episodes大约需要10-20分钟（取决于硬件）
3. **文件大小**: 完整训练数据可能较大，注意磁盘空间
4. **依赖环境**: 需要matplotlib、numpy等依赖库

## 🎯 推荐使用场景

- 验证简化奖励函数的训练效果
- 生成训练过程的可视化记录
- 对比不同参数设置的影响
- 为论文或报告准备可视化材料

## 🔄 后续分析

训练完成后，可以使用生成的数据进行：
- 性能分析和对比
- 轨迹质量评估
- 学习曲线分析
- 成功率统计
