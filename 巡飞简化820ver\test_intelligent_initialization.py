"""
测试智能初始化功能
验证巡飞弹初始朝向是否正确指向目标
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from loitering_munition_environment import LoiteringMunitionEnvironment
from loitering_munition_dwa import LoiteringMunitionDWA
from environment_config import get_environment_config, get_loitering_munition_config

def test_intelligent_initialization():
    """测试智能初始化功能"""
    print("🧠 智能初始化测试")
    print("=" * 50)
    
    # 创建环境
    env_config = get_environment_config('stage1_simple')
    lm_config = get_loitering_munition_config()
    
    env = LoiteringMunitionEnvironment(
        bounds=[1000, 1000, 200],
        environment_config=env_config,
        reward_type='simplified'
    )
    
    dwa = LoiteringMunitionDWA(dt=lm_config['dt'])
    
    # 测试多个不同的起点-终点组合
    test_scenarios = [
        {
            'name': '东北方向',
            'start': np.array([200, 200, 50]),
            'goal': np.array([800, 800, 150])
        },
        {
            'name': '正北方向',
            'start': np.array([500, 200, 100]),
            'goal': np.array([500, 800, 100])
        },
        {
            'name': '西南方向',
            'start': np.array([800, 800, 150]),
            'goal': np.array([200, 200, 50])
        },
        {
            'name': '向上倾斜',
            'start': np.array([300, 300, 30]),
            'goal': np.array([700, 700, 170])
        },
        {
            'name': '向下倾斜',
            'start': np.array([700, 700, 170]),
            'goal': np.array([300, 300, 30])
        }
    ]
    
    results = []
    
    for i, scenario in enumerate(test_scenarios):
        print(f"\n📍 测试场景 {i+1}: {scenario['name']}")
        print("-" * 30)
        
        # 手动设置起点和终点
        env.start = scenario['start']
        env.goal = scenario['goal']
        
        # 重置环境（会调用智能初始化）
        state = env.reset()
        
        # 获取DWA建议的初始状态
        dwa_state = dwa.get_initial_state(env.start, env.goal)
        
        # 计算理论值
        direction = env.goal - env.start
        distance = np.linalg.norm(direction)
        theoretical_psi = np.arctan2(direction[1], direction[0])
        theoretical_gamma = np.arcsin(direction[2] / distance)
        theoretical_gamma = np.clip(theoretical_gamma, -np.pi/3, np.pi/3)
        
        print(f"起点: [{env.start[0]:.0f}, {env.start[1]:.0f}, {env.start[2]:.0f}]")
        print(f"终点: [{env.goal[0]:.0f}, {env.goal[1]:.0f}, {env.goal[2]:.0f}]")
        print(f"距离: {distance:.1f}m")
        
        print(f"\n环境初始化结果:")
        print(f"  速度: {env.state[3]:.1f} m/s")
        print(f"  倾斜角: {np.degrees(env.state[4]):.1f}°")
        print(f"  偏航角: {np.degrees(env.state[5]):.1f}°")
        
        print(f"\nDWA建议结果:")
        print(f"  速度: {dwa_state[3]:.1f} m/s")
        print(f"  倾斜角: {np.degrees(dwa_state[4]):.1f}°")
        print(f"  偏航角: {np.degrees(dwa_state[5]):.1f}°")
        
        print(f"\n理论计算结果:")
        print(f"  理论倾斜角: {np.degrees(theoretical_gamma):.1f}°")
        print(f"  理论偏航角: {np.degrees(theoretical_psi):.1f}°")
        
        # 验证一致性
        gamma_error = abs(env.state[4] - theoretical_gamma)
        psi_error = abs(env.state[5] - theoretical_psi)
        
        print(f"\n精度验证:")
        print(f"  倾斜角误差: {np.degrees(gamma_error):.3f}°")
        print(f"  偏航角误差: {np.degrees(psi_error):.3f}°")
        
        # 检查是否指向目标
        is_pointing_correct = gamma_error < 0.01 and psi_error < 0.01
        print(f"  指向正确: {'✅' if is_pointing_correct else '❌'}")
        
        results.append({
            'scenario': scenario['name'],
            'start': env.start.copy(),
            'goal': env.goal.copy(),
            'state': env.state.copy(),
            'dwa_state': dwa_state.copy(),
            'theoretical_gamma': theoretical_gamma,
            'theoretical_psi': theoretical_psi,
            'gamma_error': gamma_error,
            'psi_error': psi_error,
            'pointing_correct': is_pointing_correct
        })
    
    return results

def visualize_initialization_results(results):
    """可视化初始化结果"""
    print(f"\n🎨 生成可视化图表")
    print("=" * 30)
    
    fig = plt.figure(figsize=(15, 10))
    
    # 3D轨迹图
    ax1 = fig.add_subplot(2, 2, 1, projection='3d')
    
    colors = ['red', 'blue', 'green', 'orange', 'purple']
    
    for i, result in enumerate(results):
        start = result['start']
        goal = result['goal']
        state = result['state']
        
        # 绘制起点和终点
        ax1.scatter(*start, color=colors[i], s=100, marker='o', alpha=0.7)
        ax1.scatter(*goal, color=colors[i], s=100, marker='*', alpha=0.7)
        
        # 绘制连接线
        ax1.plot([start[0], goal[0]], [start[1], goal[1]], [start[2], goal[2]], 
                color=colors[i], linestyle='--', alpha=0.5, label=result['scenario'])
        
        # 绘制初始朝向向量
        V = state[3]
        gamma = state[4]
        psi = state[5]
        
        # 计算朝向向量（长度为50m）
        length = 50
        dx = length * np.cos(gamma) * np.cos(psi)
        dy = length * np.cos(gamma) * np.sin(psi)
        dz = length * np.sin(gamma)
        
        ax1.quiver(start[0], start[1], start[2], dx, dy, dz, 
                  color=colors[i], arrow_length_ratio=0.1, alpha=0.8)
    
    ax1.set_xlabel('X (m)')
    ax1.set_ylabel('Y (m)')
    ax1.set_zlabel('Z (m)')
    ax1.set_title('初始朝向可视化')
    ax1.legend()
    
    # 角度误差分析
    ax2 = fig.add_subplot(2, 2, 2)
    scenarios = [r['scenario'] for r in results]
    gamma_errors = [np.degrees(r['gamma_error']) for r in results]
    psi_errors = [np.degrees(r['psi_error']) for r in results]
    
    x = np.arange(len(scenarios))
    width = 0.35
    
    ax2.bar(x - width/2, gamma_errors, width, label='倾斜角误差', alpha=0.7)
    ax2.bar(x + width/2, psi_errors, width, label='偏航角误差', alpha=0.7)
    
    ax2.set_xlabel('测试场景')
    ax2.set_ylabel('角度误差 (度)')
    ax2.set_title('初始朝向精度分析')
    ax2.set_xticks(x)
    ax2.set_xticklabels(scenarios, rotation=45)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 速度一致性检查
    ax3 = fig.add_subplot(2, 2, 3)
    velocities = [r['state'][3] for r in results]
    ax3.bar(scenarios, velocities, alpha=0.7, color='skyblue')
    ax3.axhline(y=25.0, color='red', linestyle='--', label='目标巡航速度 (25 m/s)')
    ax3.set_xlabel('测试场景')
    ax3.set_ylabel('初始速度 (m/s)')
    ax3.set_title('初始速度一致性检查')
    ax3.set_xticklabels(scenarios, rotation=45)
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 成功率统计
    ax4 = fig.add_subplot(2, 2, 4)
    success_count = sum(1 for r in results if r['pointing_correct'])
    total_count = len(results)
    success_rate = success_count / total_count
    
    labels = ['指向正确', '指向错误']
    sizes = [success_count, total_count - success_count]
    colors_pie = ['lightgreen', 'lightcoral']
    
    ax4.pie(sizes, labels=labels, colors=colors_pie, autopct='%1.1f%%', startangle=90)
    ax4.set_title(f'初始朝向准确率\n({success_count}/{total_count} = {success_rate:.1%})')
    
    plt.tight_layout()
    plt.savefig('intelligent_initialization_test.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"📊 可视化图表已保存: intelligent_initialization_test.png")

def compare_with_old_method():
    """对比旧的固定初始化方法"""
    print(f"\n🔄 对比旧方法")
    print("=" * 30)
    
    # 创建环境
    env_config = get_environment_config('stage1_simple')
    env = LoiteringMunitionEnvironment(
        bounds=[1000, 1000, 200],
        environment_config=env_config,
        reward_type='simplified'
    )
    
    # 测试场景
    env.start = np.array([200, 200, 50])
    env.goal = np.array([800, 800, 150])
    
    # 新方法（智能初始化）
    state_new = env.reset()
    
    # 旧方法（固定初始化）
    state_old = np.array([
        env.start[0], env.start[1], env.start[2],
        25.0, 0.0, 0.0  # 固定的gamma=0, psi=0
    ])
    
    # 计算目标方向
    direction = env.goal - env.start
    target_angle = np.arctan2(direction[1], direction[0])
    
    print(f"测试场景: 从 {env.start} 到 {env.goal}")
    print(f"目标方向角: {np.degrees(target_angle):.1f}°")
    
    print(f"\n旧方法 (固定初始化):")
    print(f"  偏航角: {np.degrees(state_old[5]):.1f}°")
    print(f"  与目标方向偏差: {np.degrees(abs(state_old[5] - target_angle)):.1f}°")
    
    print(f"\n新方法 (智能初始化):")
    print(f"  偏航角: {np.degrees(state_new[5]):.1f}°")
    print(f"  与目标方向偏差: {np.degrees(abs(state_new[5] - target_angle)):.1f}°")
    
    improvement = abs(state_old[5] - target_angle) - abs(state_new[5] - target_angle)
    print(f"\n改进效果: {np.degrees(improvement):.1f}° (偏差减少)")
    
    return improvement > 0

def main():
    """主函数"""
    print("🧠 智能初始化功能验证")
    print("=" * 60)
    
    try:
        # 1. 测试智能初始化
        results = test_intelligent_initialization()
        
        # 2. 生成可视化
        visualize_initialization_results(results)
        
        # 3. 对比旧方法
        improvement = compare_with_old_method()
        
        # 4. 总结
        print(f"\n🎯 测试总结")
        print("=" * 30)
        
        success_count = sum(1 for r in results if r['pointing_correct'])
        total_count = len(results)
        success_rate = success_count / total_count
        
        print(f"测试场景数量: {total_count}")
        print(f"指向正确数量: {success_count}")
        print(f"准确率: {success_rate:.1%}")
        print(f"相比旧方法改进: {'✅' if improvement else '❌'}")
        
        if success_rate >= 0.8:
            print(f"\n✅ 智能初始化功能验证成功！")
            print(f"🎉 巡飞弹现在能够智能地指向目标，大大提高训练效率")
        else:
            print(f"\n⚠️  智能初始化需要进一步调试")
        
        return success_rate >= 0.8
        
    except Exception as e:
        print(f"❌ 智能初始化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
