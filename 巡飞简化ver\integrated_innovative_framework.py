"""
集成创新框架 - 巡飞弹安全强化学习完整解决方案
"""

import numpy as np
import torch
import torch.nn as nn
from typing import Dict, List, Tuple, Optional
import json
import os
from datetime import datetime

# 导入创新组件
from innovative_safety_framework import MultiLevelSafetyConstraint, SafetyLevel
from dynamic_risk_reward import DynamicRiskAwareReward, RiskLevel
from hierarchical_curriculum import HierarchicalCurriculumLearning, CurriculumLevel

class IntegratedInnovativeFramework:
    """集成创新框架 - 巡飞弹安全强化学习完整解决方案"""
    
    def __init__(self, config_path: str = None):
        # 初始化核心组件
        self.safety_framework = MultiLevelSafetyConstraint()
        self.reward_function = DynamicRiskAwareReward()
        self.curriculum_learning = HierarchicalCurriculumLearning()
        
        # 框架状态
        self.current_stage = None
        self.training_history = []
        self.performance_metrics = {}
        self.innovation_metrics = {}
        
        # 配置管理
        self.config = self._load_config(config_path)
        
        # 创新特性开关
        self.innovation_features = {
            'multi_level_safety': True,
            'dynamic_risk_reward': True,
            'hierarchical_curriculum': True,
            'adaptive_learning': True,
            'real_time_optimization': True
        }
        
        print("🚀 集成创新框架初始化完成")
        print("=" * 60)
        print("🎯 创新特性:")
        for feature, enabled in self.innovation_features.items():
            status = "✅" if enabled else "❌"
            print(f"  {status} {feature}")
        print("=" * 60)
    
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        default_config = {
            'framework_name': 'Integrated_Innovative_Framework',
            'version': '1.0.0',
            'innovation_level': 'advanced',
            'safety_priority': 'high',
            'learning_strategy': 'curriculum_based',
            'adaptation_rate': 0.01,
            'performance_threshold': 0.8
        }
        
        if config_path and os.path.exists(config_path):
            with open(config_path, 'r') as f:
                user_config = json.load(f)
                default_config.update(user_config)
        
        return default_config
    
    def start_training(self, stage_name: str = 'stage1'):
        """开始训练"""
        print(f"\n🎯 开始集成创新训练 - 阶段: {stage_name}")
        
        # 1. 初始化课程学习阶段
        try:
            stage = self.curriculum_learning.start_stage(stage_name)
            self.current_stage = stage_name
        except ValueError as e:
            print(f"❌ 无法开始阶段: {e}")
            return False
        
        # 2. 获取阶段配置
        stage_config = self.curriculum_learning.get_stage_config(stage_name)
        
        # 3. 初始化训练环境
        training_env = self._create_training_environment(stage_config)
        
        # 4. 开始训练循环
        success = self._run_training_loop(training_env, stage_config)
        
        return success
    
    def _create_training_environment(self, stage_config: Dict):
        """创建训练环境"""
        env_config = stage_config.get('environment', {})
        
        # 这里应该创建实际的环境实例
        # 为了演示，我们创建一个模拟环境
        training_env = {
            'config': env_config,
            'state': None,
            'step_count': 0,
            'episode_count': 0
        }
        
        return training_env
    
    def _run_training_loop(self, training_env: Dict, stage_config: Dict):
        """运行训练循环"""
        training_config = stage_config.get('training', {})
        max_episodes = training_config.get('episodes', 100)
        max_steps = training_config.get('max_steps', 1000)
        
        print(f"📊 训练配置: {max_episodes} episodes, {max_steps} steps/episode")
        
        episode_rewards = []
        episode_successes = []
        episode_risks = []
        
        for episode in range(max_episodes):
            print(f"\n🔄 Episode {episode + 1}/{max_episodes}")
            
            # 重置环境
            state = self._reset_environment(training_env)
            episode_reward = 0
            episode_risk_levels = []
            
            for step in range(max_steps):
                # 1. 生成安全动作集合（多层级安全约束）
                safe_actions = self._generate_safe_actions(state, training_env)
                
                if len(safe_actions) == 0:
                    print(f"⚠️ Episode {episode + 1} 无安全动作可用，提前结束")
                    break
                
                # 2. 选择动作（这里应该使用TD3智能体）
                action = self._select_action(safe_actions, state)
                
                # 3. 执行动作
                next_state, reward, done, info = self._execute_action(state, action, training_env)
                
                # 4. 计算动态风险感知奖励
                dynamic_reward, reward_components, risk_level = self.reward_function.calculate_reward(
                    state, action, next_state, self._get_context(training_env)
                )
                
                episode_reward += dynamic_reward
                episode_risk_levels.append(risk_level)
                
                # 5. 更新状态
                state = next_state
                
                # 6. 检查终止条件
                if done:
                    success = info.get('success', False)
                    episode_successes.append(success)
                    break
            
            # 记录episode结果
            episode_rewards.append(episode_reward)
            avg_risk = np.mean([self._risk_level_to_score(rl) for rl in episode_risk_levels])
            episode_risks.append(avg_risk)
            
            # 更新性能指标
            self._update_performance_metrics(episode, episode_reward, success, avg_risk)
            
            # 自适应调整（如果启用）
            if self.innovation_features['adaptive_learning']:
                self._adaptive_adjustment(episode, episode_rewards, episode_successes, episode_risks)
            
            # 检查阶段完成条件
            if self._check_stage_completion(episode, episode_rewards, episode_successes):
                print(f"✅ 阶段 {self.current_stage} 完成!")
                break
        
        # 更新课程学习进度
        final_metrics = self._calculate_final_metrics(episode_rewards, episode_successes, episode_risks)
        self.curriculum_learning.update_performance(self.current_stage, final_metrics)
        
        return True
    
    def _generate_safe_actions(self, state: np.ndarray, training_env: Dict) -> List[np.ndarray]:
        """生成安全动作集合"""
        # 生成候选动作（这里应该基于当前状态生成）
        candidate_actions = self._generate_candidate_actions(state)
        
        # 应用多层级安全约束
        context = self._get_context(training_env)
        safe_actions, violations = self.safety_framework.generate_safe_actions(
            state, candidate_actions, context
        )
        
        return safe_actions
    
    def _generate_candidate_actions(self, state: np.ndarray) -> List[np.ndarray]:
        """生成候选动作"""
        # 简化的动作生成（实际应该基于DWA或其他方法）
        actions = []
        
        # 生成一些候选控制输入
        for a_T in np.linspace(-5, 5, 5):
            for a_N in np.linspace(-20, 20, 5):
                for mu in np.linspace(-0.5, 0.5, 3):
                    actions.append(np.array([a_T, a_N, mu]))
        
        return actions
    
    def _select_action(self, safe_actions: List[np.ndarray], state: np.ndarray) -> np.ndarray:
        """选择动作"""
        # 这里应该使用TD3智能体选择最优动作
        # 为了演示，随机选择一个安全动作
        if len(safe_actions) > 0:
            return safe_actions[np.random.randint(0, len(safe_actions))]
        else:
            # 如果没有安全动作，返回零动作
            return np.array([0.0, 0.0, 0.0])
    
    def _execute_action(self, state: np.ndarray, action: np.ndarray, training_env: Dict):
        """执行动作"""
        # 这里应该调用实际的环境step函数
        # 为了演示，我们模拟一个简单的状态更新
        
        # 简化的状态更新
        next_state = state.copy()
        next_state[:3] += np.random.normal(0, 1, 3)  # 位置变化
        next_state[3] += action[0] * 0.1  # 速度变化
        next_state[4] += action[1] * 0.01  # 倾斜角变化
        next_state[5] += action[2] * 0.01  # 偏航角变化
        
        # 检查是否到达目标
        goal = np.array([1000, 1000, 1000])  # 假设目标
        distance_to_goal = np.linalg.norm(next_state[:3] - goal)
        
        done = distance_to_goal < 10 or training_env['step_count'] >= 1000
        success = distance_to_goal < 10
        
        # 基础奖励
        reward = -distance_to_goal * 0.01
        
        info = {'success': success, 'distance': distance_to_goal}
        
        training_env['step_count'] += 1
        
        return next_state, reward, done, info
    
    def _reset_environment(self, training_env: Dict) -> np.ndarray:
        """重置环境"""
        # 简化的状态初始化
        initial_state = np.array([100, 100, 100, 25, 0, 0])  # [x, y, z, V, gamma, psi]
        training_env['step_count'] = 0
        return initial_state
    
    def _get_context(self, training_env: Dict) -> Dict:
        """获取上下文信息"""
        env_config = training_env.get('config', {})
        
        context = {
            'goal': np.array([1000, 1000, 1000]),
            'start': np.array([100, 100, 100]),
            'bounds': env_config.get('environment_size', [2000, 2000, 200]),
            'obstacles': self._generate_obstacles(env_config),
            'fuel': 800,
            'max_fuel': 1000,
            'time': training_env.get('step_count', 0),
            'max_time': 2000
        }
        
        return context
    
    def _generate_obstacles(self, env_config: Dict) -> List[Dict]:
        """生成障碍物"""
        obstacles = []
        obstacle_count = env_config.get('obstacle_count', (3, 5))
        count = np.random.randint(obstacle_count[0], obstacle_count[1] + 1)
        
        for _ in range(count):
            center = np.random.uniform(200, 800, 3)
            radius = np.random.uniform(50, 150)
            obstacles.append({'center': center, 'radius': radius})
        
        return obstacles
    
    def _update_performance_metrics(self, episode: int, reward: float, success: bool, risk: float):
        """更新性能指标"""
        if episode not in self.performance_metrics:
            self.performance_metrics[episode] = {
                'reward': reward,
                'success': success,
                'risk': risk,
                'timestamp': episode
            }
    
    def _adaptive_adjustment(self, episode: int, rewards: List[float], successes: List[bool], risks: List[float]):
        """自适应调整"""
        if episode < 10:  # 前10个episode不调整
            return
        
        # 分析最近性能
        recent_rewards = rewards[-10:]
        recent_successes = successes[-10:]
        recent_risks = risks[-10:]
        
        avg_reward = np.mean(recent_rewards)
        success_rate = np.mean(recent_successes)
        avg_risk = np.mean(recent_risks)
        
        # 根据性能调整参数
        if success_rate < 0.3 and avg_risk > 0.7:
            # 性能差且风险高，加强安全约束
            self._strengthen_safety_constraints()
        elif success_rate > 0.8 and avg_risk < 0.3:
            # 性能好且风险低，可以适当放松约束
            self._relax_safety_constraints()
    
    def _strengthen_safety_constraints(self):
        """加强安全约束"""
        print("🔒 加强安全约束")
        # 这里应该调整安全约束参数
    
    def _relax_safety_constraints(self):
        """放松安全约束"""
        print("🔓 放松安全约束")
        # 这里应该调整安全约束参数
    
    def _check_stage_completion(self, episode: int, rewards: List[float], successes: List[bool]) -> bool:
        """检查阶段完成条件"""
        if episode < 50:  # 最少50个episode
            return False
        
        # 检查最近的成功率
        recent_successes = successes[-20:]
        success_rate = np.mean(recent_successes)
        
        # 检查平均奖励
        recent_rewards = rewards[-20:]
        avg_reward = np.mean(recent_rewards)
        
        # 完成条件
        completion_criteria = self.curriculum_learning.stages[self.current_stage].completion_criteria
        
        return (success_rate >= completion_criteria.get('success_rate', 0.8) and
                avg_reward >= completion_criteria.get('avg_reward', 50.0))
    
    def _calculate_final_metrics(self, rewards: List[float], successes: List[bool], risks: List[float]) -> Dict:
        """计算最终指标"""
        return {
            'success_rate': np.mean(successes),
            'avg_reward': np.mean(rewards),
            'episodes': len(rewards),
            'consecutive_success': self._calculate_consecutive_success(successes),
            'collision_rate': 1.0 - np.mean(successes),
            'avg_risk': np.mean(risks)
        }
    
    def _calculate_consecutive_success(self, successes: List[bool]) -> int:
        """计算连续成功次数"""
        max_consecutive = 0
        current_consecutive = 0
        
        for success in successes:
            if success:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0
        
        return max_consecutive
    
    def _risk_level_to_score(self, risk_level: RiskLevel) -> float:
        """将风险等级转换为数值分数"""
        return {
            RiskLevel.LOW: 0.2,
            RiskLevel.MEDIUM: 0.5,
            RiskLevel.HIGH: 0.8,
            RiskLevel.CRITICAL: 1.0
        }[risk_level]
    
    def get_innovation_metrics(self) -> Dict:
        """获取创新指标"""
        return {
            'framework_version': self.config['version'],
            'innovation_level': self.config['innovation_level'],
            'active_features': sum(self.innovation_features.values()),
            'total_features': len(self.innovation_features),
            'curriculum_progress': self.curriculum_learning.get_curriculum_progress(),
            'risk_statistics': self.reward_function.get_risk_statistics(),
            'reward_statistics': self.reward_function.get_reward_statistics(),
            'performance_summary': self._get_performance_summary()
        }
    
    def _get_performance_summary(self) -> Dict:
        """获取性能摘要"""
        if not self.performance_metrics:
            return {}
        
        rewards = [m['reward'] for m in self.performance_metrics.values()]
        successes = [m['success'] for m in self.performance_metrics.values()]
        risks = [m['risk'] for m in self.performance_metrics.values()]
        
        return {
            'total_episodes': len(self.performance_metrics),
            'avg_reward': np.mean(rewards),
            'success_rate': np.mean(successes),
            'avg_risk': np.mean(risks),
            'best_reward': np.max(rewards),
            'worst_reward': np.min(rewards)
        }
    
    def save_framework_state(self, filepath: str):
        """保存框架状态"""
        state = {
            'config': self.config,
            'current_stage': self.current_stage,
            'performance_metrics': self.performance_metrics,
            'innovation_metrics': self.get_innovation_metrics(),
            'timestamp': datetime.now().isoformat()
        }
        
        with open(filepath, 'w') as f:
            json.dump(state, f, indent=2)
        
        print(f"💾 框架状态已保存到: {filepath}")
    
    def load_framework_state(self, filepath: str):
        """加载框架状态"""
        if os.path.exists(filepath):
            with open(filepath, 'r') as f:
                state = json.load(f)
            
            self.config = state.get('config', self.config)
            self.current_stage = state.get('current_stage')
            self.performance_metrics = state.get('performance_metrics', {})
            
            print(f"📂 框架状态已从 {filepath} 加载")
        else:
            print(f"❌ 状态文件不存在: {filepath}")

# 使用示例
if __name__ == "__main__":
    # 创建集成创新框架
    framework = IntegratedInnovativeFramework()
    
    # 开始训练
    success = framework.start_training('stage1')
    
    if success:
        # 获取创新指标
        metrics = framework.get_innovation_metrics()
        print("\n📊 创新指标:")
        print(json.dumps(metrics, indent=2, ensure_ascii=False))
        
        # 保存框架状态
        framework.save_framework_state('framework_state.json')
    else:
        print("❌ 训练失败")
