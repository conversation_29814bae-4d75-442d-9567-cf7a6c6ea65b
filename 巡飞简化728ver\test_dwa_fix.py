"""
测试修复后的DWA算法效果
验证巡飞弹是否能朝向目标移动
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

# 直接导入修复后的类
from loitering_munition_dwa import LoiteringMunitionDWA

def test_dwa_direction():
    """测试DWA是否能选择朝向目标的控制"""
    print("=== 测试DWA方向选择 ===")
    
    # 创建DWA实例
    dwa = LoiteringMunitionDWA()
    
    # 设置测试场景
    current_state = np.array([200, 200, 200, 25, 0.0, 0.0])  # 起点
    goal = np.array([1800, 1800, 1800])  # 目标
    obstacles = []  # 暂时不考虑障碍物
    
    print(f"起点: {current_state[:3]}")
    print(f"目标: {goal}")
    print(f"初始距离: {np.linalg.norm(goal - current_state[:3]):.2f}m")
    
    # 计算目标方向
    goal_direction = (goal - current_state[:3]) / np.linalg.norm(goal - current_state[:3])
    print(f"目标方向: {goal_direction}")
    
    # 生成安全控制
    safe_controls = dwa.generate_safe_control_set(
        current_state, obstacles, goal, max_actions=20
    )
    
    print(f"\n生成的安全控制数量: {len(safe_controls)}")
    
    if len(safe_controls) == 0:
        print("❌ 没有生成安全控制")
        return False
    
    # 分析前10个控制的方向性
    positive_controls = 0
    control_analysis = []
    
    for i, control in enumerate(safe_controls[:10]):
        # 计算评价分数
        score = dwa.evaluate_control(control, current_state, goal, obstacles)
        
        # 预测运动方向
        predicted_states = dwa._predict_trajectory(current_state, control, dwa.dt, 10)
        
        if len(predicted_states) > 1:
            movement = predicted_states[-1][:3] - predicted_states[0][:3]
            movement_magnitude = np.linalg.norm(movement)
            
            if movement_magnitude > 1e-6:
                movement_direction = movement / movement_magnitude
                alignment = np.dot(goal_direction, movement_direction)
                
                control_analysis.append({
                    'control': control,
                    'score': score,
                    'alignment': alignment,
                    'movement_magnitude': movement_magnitude
                })
                
                if alignment > 0.1:  # 朝向目标
                    positive_controls += 1
                
                print(f"控制{i}: score={score:.3f}, 对齐度={alignment:.3f} {'✅' if alignment > 0.1 else '❌'}")
    
    print(f"\n朝向目标的控制数量: {positive_controls}/{min(10, len(safe_controls))}")
    
    # 检查最高分控制是否朝向目标
    if control_analysis:
        best_control = max(control_analysis, key=lambda x: x['score'])
        print(f"\n最高分控制:")
        print(f"  分数: {best_control['score']:.3f}")
        print(f"  对齐度: {best_control['alignment']:.3f}")
        print(f"  朝向目标: {'✅' if best_control['alignment'] > 0.1 else '❌'}")
        
        return best_control['alignment'] > 0.1
    
    return False

def test_single_step_movement():
    """测试单步移动效果"""
    print("\n=== 测试单步移动效果 ===")
    
    # 创建简化的环境状态
    class SimpleEnv:
        def __init__(self):
            self.state = np.array([200, 200, 200, 25, 0.0, 0.0])
            self.goal = np.array([1800, 1800, 1800])
            self.obstacles = []
            self.a_T_max = 8.0
            self.a_N_max = 39.24
            self.dt = 0.1
            self.g = 9.81
        
        def step(self, control_input):
            """简化的运动学更新"""
            a_T, a_N, mu = control_input
            x, y, z, V, gamma, psi = self.state
            
            # 简化的运动学方程（单步）
            dt = self.dt
            
            # 更新位置
            x += V * np.cos(gamma) * np.cos(psi) * dt
            y += V * np.cos(gamma) * np.sin(psi) * dt
            z += V * np.sin(gamma) * dt
            
            # 更新速度和角度
            V += (a_T - self.g * np.sin(gamma)) * dt
            V = np.clip(V, 15.0, 60.0)
            
            gamma += (a_N * np.cos(mu) / V) * dt
            gamma = np.clip(gamma, -np.pi/3, np.pi/3)
            
            psi += (a_N * np.sin(mu) / (V * np.cos(gamma))) * dt
            
            self.state = np.array([x, y, z, V, gamma, psi])
            return self.state
    
    # 创建环境和DWA
    env = SimpleEnv()
    dwa = LoiteringMunitionDWA()
    
    print(f"初始位置: {env.state[:3]}")
    print(f"目标位置: {env.goal}")
    print(f"初始距离: {np.linalg.norm(env.goal - env.state[:3]):.2f}m")
    
    # 生成安全控制并选择最佳的
    safe_controls = dwa.generate_safe_control_set(
        env.state, env.obstacles, env.goal, max_actions=20
    )
    
    if len(safe_controls) == 0:
        print("❌ 没有安全控制")
        return False
    
    # 选择最佳控制
    best_control = None
    best_score = -float('inf')
    
    for control in safe_controls:
        score = dwa.evaluate_control(control, env.state, env.goal, env.obstacles)
        if score > best_score:
            best_score = score
            best_control = control
    
    print(f"选择的最佳控制: {best_control}")
    print(f"控制分数: {best_score:.3f}")
    
    # 执行控制
    prev_pos = env.state[:3].copy()
    prev_dist = np.linalg.norm(env.goal - prev_pos)
    
    new_state = env.step(best_control)
    new_pos = new_state[:3]
    new_dist = np.linalg.norm(env.goal - new_pos)
    
    # 分析结果
    movement = new_pos - prev_pos
    movement_magnitude = np.linalg.norm(movement)
    
    print(f"\n移动结果:")
    print(f"  新位置: {new_pos}")
    print(f"  移动向量: {movement}")
    print(f"  移动距离: {movement_magnitude:.3f}m")
    print(f"  距离变化: {new_dist - prev_dist:.3f}m")
    print(f"  接近目标: {'✅' if new_dist < prev_dist else '❌'}")
    
    if movement_magnitude > 1e-6:
        goal_direction = (env.goal - prev_pos) / np.linalg.norm(env.goal - prev_pos)
        movement_direction = movement / movement_magnitude
        alignment = np.dot(goal_direction, movement_direction)
        print(f"  方向对齐度: {alignment:.3f}")
        print(f"  朝向目标: {'✅' if alignment > 0.1 else '❌'}")
        
        return alignment > 0.1 and new_dist < prev_dist
    
    return False

def test_multi_step_trajectory():
    """测试多步轨迹"""
    print("\n=== 测试多步轨迹 ===")
    
    # 重用上面的SimpleEnv
    class SimpleEnv:
        def __init__(self):
            self.state = np.array([200, 200, 200, 25, 0.0, 0.0])
            self.goal = np.array([1800, 1800, 1800])
            self.obstacles = []
            self.a_T_max = 8.0
            self.a_N_max = 39.24
            self.dt = 0.1
            self.g = 9.81
        
        def step(self, control_input):
            a_T, a_N, mu = control_input
            x, y, z, V, gamma, psi = self.state
            
            dt = self.dt
            x += V * np.cos(gamma) * np.cos(psi) * dt
            y += V * np.cos(gamma) * np.sin(psi) * dt
            z += V * np.sin(gamma) * dt
            
            V += (a_T - self.g * np.sin(gamma)) * dt
            V = np.clip(V, 15.0, 60.0)
            
            gamma += (a_N * np.cos(mu) / V) * dt
            gamma = np.clip(gamma, -np.pi/3, np.pi/3)
            
            psi += (a_N * np.sin(mu) / (V * np.cos(gamma))) * dt
            
            self.state = np.array([x, y, z, V, gamma, psi])
            return self.state
    
    env = SimpleEnv()
    dwa = LoiteringMunitionDWA()
    
    trajectory = [env.state[:3].copy()]
    distances = [np.linalg.norm(env.goal - env.state[:3])]
    
    print(f"开始多步测试，初始距离: {distances[0]:.2f}m")
    
    # 执行20步
    for step in range(20):
        # 生成安全控制
        safe_controls = dwa.generate_safe_control_set(
            env.state, env.obstacles, env.goal, max_actions=20
        )
        
        if len(safe_controls) == 0:
            print(f"步骤{step}: 没有安全控制")
            break
        
        # 选择最佳控制
        best_control = None
        best_score = -float('inf')
        
        for control in safe_controls:
            score = dwa.evaluate_control(control, env.state, env.goal, env.obstacles)
            if score > best_score:
                best_score = score
                best_control = control
        
        # 执行控制
        env.step(best_control)
        trajectory.append(env.state[:3].copy())
        distances.append(np.linalg.norm(env.goal - env.state[:3]))
        
        if step % 5 == 0:
            print(f"步骤{step}: 距离={distances[-1]:.2f}m")
    
    # 分析轨迹
    trajectory = np.array(trajectory)
    final_distance = distances[-1]
    initial_distance = distances[0]
    distance_improvement = initial_distance - final_distance
    
    print(f"\n轨迹分析:")
    print(f"  初始距离: {initial_distance:.2f}m")
    print(f"  最终距离: {final_distance:.2f}m")
    print(f"  距离改善: {distance_improvement:.2f}m")
    print(f"  改善百分比: {(distance_improvement/initial_distance)*100:.1f}%")
    print(f"  朝向目标: {'✅' if distance_improvement > 0 else '❌'}")
    
    # 计算总移动距离
    total_movement = 0
    for i in range(1, len(trajectory)):
        total_movement += np.linalg.norm(trajectory[i] - trajectory[i-1])
    
    print(f"  总移动距离: {total_movement:.2f}m")
    print(f"  效率: {distance_improvement/total_movement:.3f}" if total_movement > 0 else "  效率: N/A")
    
    return distance_improvement > 0

if __name__ == "__main__":
    print("🔧 测试修复后的DWA算法")
    print("=" * 50)
    
    # 测试1: 方向选择
    test1_passed = test_dwa_direction()
    
    # 测试2: 单步移动
    test2_passed = test_single_step_movement()
    
    # 测试3: 多步轨迹
    test3_passed = test_multi_step_trajectory()
    
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"  方向选择测试: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"  单步移动测试: {'✅ 通过' if test2_passed else '❌ 失败'}")
    print(f"  多步轨迹测试: {'✅ 通过' if test3_passed else '❌ 失败'}")
    
    if all([test1_passed, test2_passed, test3_passed]):
        print("\n🎉 所有测试通过！DWA修复成功，巡飞弹现在应该能朝向目标移动。")
    else:
        print("\n⚠️  部分测试失败，可能还需要进一步调试。")
