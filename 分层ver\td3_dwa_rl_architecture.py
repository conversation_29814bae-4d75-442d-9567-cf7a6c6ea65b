"""
TD3-DWA-RL分层安全控制架构
基于Twin Delayed Deep Deterministic Policy Gradient (TD3)的改进实现

核心思想：
1. DWA负责生成安全动作集合（安全约束层）
2. TD3负责从安全动作集中选择最优动作（智能决策层）
3. 保证100%安全性的同时实现智能优化

TD3改进特点：
- Twin Critic Networks (双重评价网络)
- Delayed Policy Updates (延迟策略更新)
- Target Policy Smoothing (目标策略平滑)
- 更稳定的训练过程
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from collections import deque
import random
import copy

class SafeActionSetGenerator:
    """DWA安全动作集生成器"""
    def __init__(self, config):
        self.dt = config['dt']
        self.predict_time = config['predict_time']
        self.max_velocity = config['max_velocity']
        self.max_acceleration = config['max_acceleration']
        self.velocity_resolution = config['velocity_resolution']
        self.min_safe_distance = config['min_safe_distance']
        
    def calc_dynamic_window(self, state):
        """计算动态窗口"""
        vs = [0, 0, 0, self.max_velocity[0], self.max_velocity[1], self.max_velocity[2]]
        
        vd = [state[3] - self.max_acceleration[0] * self.dt,
              state[4] - self.max_acceleration[1] * self.dt,
              state[5] - self.max_acceleration[2] * self.dt,
              state[3] + self.max_acceleration[0] * self.dt,
              state[4] + self.max_acceleration[1] * self.dt,
              state[5] + self.max_acceleration[2] * self.dt]
        
        dw = [max(vs[0], vd[0]), max(vs[1], vd[1]), max(vs[2], vd[2]),
              min(vs[3], vd[3]), min(vs[4], vd[4]), min(vs[5], vd[5])]
        
        return dw
    
    def predict_trajectory(self, state, velocity, predict_time):
        """预测轨迹"""
        trajectory = []
        x, y, z = state[0], state[1], state[2]
        vx, vy, vz = velocity
        
        for i in range(int(predict_time / self.dt)):
            x += vx * self.dt
            y += vy * self.dt
            z += vz * self.dt
            trajectory.append([x, y, z, vx, vy, vz])
            
        return np.array(trajectory)
    
    def is_trajectory_safe(self, trajectory, obstacles):
        """检查轨迹是否安全"""
        for point in trajectory:
            for obs in obstacles:
                distance = np.linalg.norm(point[:3] - obs['center'])
                if distance <= obs['radius'] + self.min_safe_distance:
                    return False
        return True
    
    def evaluate_action(self, state, velocity, goal, obstacles):
        """评估单个动作"""
        trajectory = self.predict_trajectory(state, velocity, self.predict_time)
        
        if not self.is_trajectory_safe(trajectory, obstacles):
            return None
        
        final_pos = trajectory[-1][:3]
        goal_direction = goal - final_pos
        velocity_direction = trajectory[-1][3:6]
        
        if np.linalg.norm(velocity_direction) > 0 and np.linalg.norm(goal_direction) > 0:
            heading_score = np.dot(goal_direction, velocity_direction) / (
                np.linalg.norm(goal_direction) * np.linalg.norm(velocity_direction))
        else:
            heading_score = 0
        
        goal_dist = np.linalg.norm(final_pos - goal)
        velocity_score = np.linalg.norm(velocity_direction)
        
        return {
            'velocity': velocity,
            'trajectory': trajectory,
            'heading_score': heading_score,
            'goal_distance': goal_dist,
            'velocity_score': velocity_score,
            'total_score': heading_score * 0.4 - goal_dist * 0.004 + velocity_score * 0.2
        }
    
    def generate_safe_action_set(self, state, goal, obstacles, target_actions=20):
        """生成安全动作集"""
        dw = self.calc_dynamic_window(state)
        safe_actions = []
        
        for vx in np.arange(dw[0], dw[3], self.velocity_resolution):
            for vy in np.arange(dw[1], dw[4], self.velocity_resolution):
                for vz in np.arange(dw[2], dw[5], self.velocity_resolution):
                    velocity = [vx, vy, vz]
                    action_eval = self.evaluate_action(state, velocity, goal, obstacles)
                    
                    if action_eval is not None:
                        safe_actions.append(action_eval)
        
        if not safe_actions:
            emergency_action = {
                'velocity': [0, 0, 0],
                'trajectory': np.array([[state[0], state[1], state[2], 0, 0, 0]]),
                'heading_score': 0,
                'goal_distance': np.linalg.norm(state[:3] - goal),
                'velocity_score': 0,
                'total_score': -1000
            }
            return [emergency_action]
        
        safe_actions.sort(key=lambda x: x['total_score'], reverse=True)
        return safe_actions[:target_actions]

class ActorNetwork(nn.Module):
    """TD3 Actor网络：从安全动作集中选择动作"""
    def __init__(self, state_dim, max_actions, hidden_dim=256):
        super(ActorNetwork, self).__init__()
        self.max_actions = max_actions
        
        # 状态编码器
        self.state_encoder = nn.Sequential(
            nn.Linear(state_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # 动作编码器
        self.action_encoder = nn.Sequential(
            nn.Linear(3, hidden_dim//2),
            nn.LayerNorm(hidden_dim//2),
            nn.ReLU(),
            nn.Linear(hidden_dim//2, hidden_dim//2),
            nn.LayerNorm(hidden_dim//2),
            nn.ReLU()
        )
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(
            embed_dim=hidden_dim + hidden_dim//2,
            num_heads=8,
            dropout=0.1,
            batch_first=True
        )
        
        # 策略输出层
        self.policy_head = nn.Sequential(
            nn.Linear(hidden_dim + hidden_dim//2, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim//2),
            nn.ReLU(),
            nn.Linear(hidden_dim//2, 1)
        )
        
    def forward(self, state, actions, mask=None):
        """前向传播"""
        batch_size, num_actions, _ = actions.shape
        
        # 编码状态
        state_encoded = self.state_encoder(state)
        state_encoded = state_encoded.unsqueeze(1).repeat(1, num_actions, 1)
        
        # 编码动作
        actions_flat = actions.view(-1, 3)
        actions_encoded = self.action_encoder(actions_flat)
        actions_encoded = actions_encoded.view(batch_size, num_actions, -1)
        
        # 融合状态和动作
        combined = torch.cat([state_encoded, actions_encoded], dim=-1)
        
        # 注意力机制
        attended, _ = self.attention(combined, combined, combined, key_padding_mask=~mask if mask is not None else None)
        
        # 策略输出
        logits = self.policy_head(attended).squeeze(-1)
        
        if mask is not None:
            logits = logits.masked_fill(~mask, float('-inf'))
        
        return logits
    
    def get_action_probabilities(self, state, actions, mask=None, temperature=1.0):
        """获取动作概率分布"""
        logits = self.forward(state, actions, mask)
        probabilities = F.softmax(logits / temperature, dim=-1)
        return probabilities

class CriticNetwork(nn.Module):
    """TD3 Critic网络：双重评价网络"""
    def __init__(self, state_dim, action_dim=3, hidden_dim=256):
        super(CriticNetwork, self).__init__()
        
        # 第一个Q网络
        self.q1_state_encoder = nn.Sequential(
            nn.Linear(state_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU()
        )
        
        self.q1_action_encoder = nn.Sequential(
            nn.Linear(action_dim, hidden_dim//2),
            nn.LayerNorm(hidden_dim//2),
            nn.ReLU(),
            nn.Linear(hidden_dim//2, hidden_dim//2),
            nn.LayerNorm(hidden_dim//2),
            nn.ReLU()
        )
        
        self.q1_value_head = nn.Sequential(
            nn.Linear(hidden_dim + hidden_dim//2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim//2),
            nn.ReLU(),
            nn.Linear(hidden_dim//2, 1)
        )
        
        # 第二个Q网络
        self.q2_state_encoder = nn.Sequential(
            nn.Linear(state_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU()
        )
        
        self.q2_action_encoder = nn.Sequential(
            nn.Linear(action_dim, hidden_dim//2),
            nn.LayerNorm(hidden_dim//2),
            nn.ReLU(),
            nn.Linear(hidden_dim//2, hidden_dim//2),
            nn.LayerNorm(hidden_dim//2),
            nn.ReLU()
        )
        
        self.q2_value_head = nn.Sequential(
            nn.Linear(hidden_dim + hidden_dim//2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim//2),
            nn.ReLU(),
            nn.Linear(hidden_dim//2, 1)
        )
        
    def forward(self, state, action):
        """前向传播，返回两个Q值"""
        # Q1网络
        state_encoded_1 = self.q1_state_encoder(state)
        action_encoded_1 = self.q1_action_encoder(action)
        combined_1 = torch.cat([state_encoded_1, action_encoded_1], dim=-1)
        q1_value = self.q1_value_head(combined_1)
        
        # Q2网络
        state_encoded_2 = self.q2_state_encoder(state)
        action_encoded_2 = self.q2_action_encoder(action)
        combined_2 = torch.cat([state_encoded_2, action_encoded_2], dim=-1)
        q2_value = self.q2_value_head(combined_2)
        
        return q1_value, q2_value
    
    def q1(self, state, action):
        """只返回Q1值"""
        state_encoded = self.q1_state_encoder(state)
        action_encoded = self.q1_action_encoder(action)
        combined = torch.cat([state_encoded, action_encoded], dim=-1)
        return self.q1_value_head(combined)

class ReplayBuffer:
    """经验回放缓冲区"""
    def __init__(self, capacity=100000):
        self.capacity = capacity
        self.buffer = deque(maxlen=capacity)
        
    def add(self, state, action, reward, next_state, done, safe_actions, goal, obstacles):
        """添加经验"""
        experience = {
            'state': state,
            'action': action,
            'reward': reward,
            'next_state': next_state,
            'done': done,
            'safe_actions': safe_actions,
            'goal': goal,
            'obstacles': obstacles
        }
        self.buffer.append(experience)
    
    def sample(self, batch_size):
        """采样经验"""
        return random.sample(self.buffer, min(batch_size, len(self.buffer)))
    
    def __len__(self):
        return len(self.buffer)

class TD3_DWA_RL_Controller:
    """TD3-DWA-RL混合控制器"""
    def __init__(self, config):
        self.config = config
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        # 网络初始化
        self.actor = ActorNetwork(
            config['state_dim'],
            config['max_actions'],
            config['hidden_dim']
        ).to(self.device)

        self.critic = CriticNetwork(
            config['state_dim'],
            action_dim=3,
            hidden_dim=config['hidden_dim']
        ).to(self.device)

        # Target networks
        self.target_actor = copy.deepcopy(self.actor)
        self.target_critic = copy.deepcopy(self.critic)

        # 优化器
        self.actor_optimizer = optim.Adam(self.actor.parameters(), lr=config['actor_lr'])
        self.critic_optimizer = optim.Adam(self.critic.parameters(), lr=config['critic_lr'])

        # 经验回放
        self.replay_buffer = ReplayBuffer(capacity=config['buffer_size'])

        # TD3参数
        self.gamma = config['gamma']
        self.tau = config['tau']
        self.policy_noise = config['policy_noise']
        self.noise_clip = config['noise_clip']
        self.policy_freq = config['policy_freq']

        # DWA组件
        self.safe_action_generator = SafeActionSetGenerator(config['dwa'])

        # 训练统计
        self.total_it = 0

    def get_action(self, state, goal, obstacles, add_noise=False):
        """获取控制动作"""
        # DWA生成安全动作集
        safe_actions = self.safe_action_generator.generate_safe_action_set(
            state, goal, obstacles, target_actions=self.config['max_actions']
        )

        if len(safe_actions) == 0:
            return [0, 0, 0], {'num_safe_actions': 0}

        # 准备输入
        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
        actions_tensor = torch.FloatTensor(np.array([action['velocity'] for action in safe_actions])).unsqueeze(0)

        # 填充到固定长度
        num_actions = len(safe_actions)
        if num_actions < self.config['max_actions']:
            padding = np.zeros((self.config['max_actions'] - num_actions, 3))
            actions_tensor = torch.cat([
                actions_tensor,
                torch.FloatTensor(padding).unsqueeze(0)
            ], dim=1)

        actions_tensor = actions_tensor.to(self.device)

        # 创建mask
        mask = torch.ones(1, self.config['max_actions'], dtype=torch.bool).to(self.device)
        if num_actions < self.config['max_actions']:
            mask[0, num_actions:] = False

        # Actor选择动作
        with torch.no_grad():
            probabilities = self.actor.get_action_probabilities(
                state_tensor, actions_tensor, mask, temperature=0.1 if not add_noise else 1.0
            )

            if add_noise:
                # 训练时添加噪声
                noise = torch.randn_like(probabilities) * 0.1
                probabilities = F.softmax(probabilities + noise, dim=-1)

            action_idx = probabilities.argmax().item()

        selected_action = safe_actions[action_idx] if action_idx < len(safe_actions) else safe_actions[0]

        return selected_action['velocity'], {
            'safe_actions': safe_actions,
            'selected_idx': action_idx,
            'num_safe_actions': len(safe_actions),
            'action_probabilities': probabilities.cpu().numpy()
        }

    def train_step(self, batch_size=256):
        """TD3训练步骤"""
        if len(self.replay_buffer) < batch_size:
            return None, None

        self.total_it += 1

        # 采样经验
        batch = self.replay_buffer.sample(batch_size)

        # 准备批次数据 - 优化张量创建性能
        states = torch.FloatTensor(np.array([exp['state'] for exp in batch])).to(self.device)
        actions = torch.FloatTensor(np.array([exp['action'] for exp in batch])).to(self.device)
        rewards = torch.FloatTensor(np.array([exp['reward'] for exp in batch])).to(self.device)
        next_states = torch.FloatTensor(np.array([exp['next_state'] for exp in batch])).to(self.device)
        dones = torch.BoolTensor(np.array([exp['done'] for exp in batch])).to(self.device)

        with torch.no_grad():
            # 为下一状态选择动作（带噪声）
            next_actions = []
            for i, exp in enumerate(batch):
                next_action, _ = self.get_action(
                    exp['next_state'], exp['goal'], exp['obstacles'], add_noise=True
                )
                next_actions.append(next_action)

            next_actions = torch.FloatTensor(np.array(next_actions)).to(self.device)

            # 目标策略平滑
            noise = (torch.randn_like(next_actions) * self.policy_noise).clamp(
                -self.noise_clip, self.noise_clip
            )
            next_actions = (next_actions + noise).clamp(-5, 5)  # 速度限制

            # 计算目标Q值（取两个Q网络的最小值）
            target_q1, target_q2 = self.target_critic(next_states, next_actions)
            target_q = torch.min(target_q1, target_q2)
            target_q = rewards.unsqueeze(1) + (self.gamma * target_q * (~dones).unsqueeze(1))

        # 当前Q值
        current_q1, current_q2 = self.critic(states, actions)

        # Critic损失
        critic_loss = F.mse_loss(current_q1, target_q) + F.mse_loss(current_q2, target_q)

        # 更新Critic
        self.critic_optimizer.zero_grad()
        critic_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.critic.parameters(), 1.0)
        self.critic_optimizer.step()

        actor_loss = None

        # 延迟策略更新
        if self.total_it % self.policy_freq == 0:
            # Actor损失
            actor_actions = []
            for i, exp in enumerate(batch):
                actor_action, _ = self.get_action(
                    exp['state'], exp['goal'], exp['obstacles'], add_noise=False
                )
                actor_actions.append(actor_action)

            actor_actions = torch.FloatTensor(np.array(actor_actions)).to(self.device)
            actor_loss = -self.critic.q1(states, actor_actions).mean()

            # 更新Actor
            self.actor_optimizer.zero_grad()
            actor_loss.backward()
            torch.nn.utils.clip_grad_norm_(self.actor.parameters(), 1.0)
            self.actor_optimizer.step()

            # 软更新目标网络
            self._soft_update_target_networks()

            actor_loss = actor_loss.item()

        return critic_loss.item(), actor_loss

    def _soft_update_target_networks(self):
        """软更新目标网络"""
        for target_param, param in zip(self.target_actor.parameters(), self.actor.parameters()):
            target_param.data.copy_(self.tau * param.data + (1 - self.tau) * target_param.data)

        for target_param, param in zip(self.target_critic.parameters(), self.critic.parameters()):
            target_param.data.copy_(self.tau * param.data + (1 - self.tau) * target_param.data)

    def save_model(self, filepath):
        """保存模型"""
        torch.save({
            'actor_state_dict': self.actor.state_dict(),
            'critic_state_dict': self.critic.state_dict(),
            'actor_optimizer_state_dict': self.actor_optimizer.state_dict(),
            'critic_optimizer_state_dict': self.critic_optimizer.state_dict(),
        }, filepath)

    def load_model(self, filepath):
        """加载模型"""
        checkpoint = torch.load(filepath, map_location=self.device)
        self.actor.load_state_dict(checkpoint['actor_state_dict'])
        self.critic.load_state_dict(checkpoint['critic_state_dict'])
        self.actor_optimizer.load_state_dict(checkpoint['actor_optimizer_state_dict'])
        self.critic_optimizer.load_state_dict(checkpoint['critic_optimizer_state_dict'])

# TD3配置参数
td3_config = {
    'dwa': {
        'dt': 0.1,
        'predict_time': 2.0,
        'max_velocity': [3, 3, 3],
        'max_acceleration': [5, 5, 5],
        'velocity_resolution': 0.3,
        'min_safe_distance': 1.5
    },
    'state_dim': 14,  # 更新为支持复杂环境的观测维度
    'max_actions': 20,
    'hidden_dim': 256,
    'actor_lr': 0.0003,
    'critic_lr': 0.001,
    'buffer_size': 100000,
    'gamma': 0.99,
    'tau': 0.005,
    'policy_noise': 0.2,
    'noise_clip': 0.5,
    'policy_freq': 2  # 延迟策略更新频率
}
