"""
简化的3D无人机环境
专门用于验证DWA-RL架构的基础版本

特点：
- 静止的球形障碍物（3-5个）
- 固定的目标点
- 简单的奖励函数
- 100x100x100的3D空间
"""

import numpy as np
import random

class SimpleUAVEnvironment:
    """简化的无人机3D环境"""
    def __init__(self, bounds=[100, 100, 100]):
        self.bounds = bounds
        self.reset()
        
    def reset(self):
        """重置环境"""
        # 固定起点和终点（便于调试）
        self.start = np.array([10.0, 10.0, 10.0], dtype=np.float64)
        self.goal = np.array([80.0, 80.0, 80.0], dtype=np.float64)
        
        # 生成3-5个静止障碍物
        self.obstacles = []
        num_obstacles = random.randint(3, 5)
        
        # 预定义一些障碍物位置（确保环境可解）
        obstacle_centers = [
            [30, 30, 30],
            [50, 20, 40],
            [40, 60, 50],
            [60, 40, 30],
            [70, 70, 60]
        ]
        
        for i in range(num_obstacles):
            center = np.array(obstacle_centers[i], dtype=np.float64)
            radius = random.uniform(4, 8)
            self.obstacles.append({'center': center, 'radius': radius})
        
        # 初始状态 [x, y, z, vx, vy, vz]
        self.state = np.concatenate([self.start, [0.0, 0.0, 0.0]]).astype(np.float64)
        self.step_count = 0
        self.max_steps = 500
        
        return self._get_observation()
    
    def _get_observation(self):
        """获取观测状态"""
        pos = self.state[:3]
        vel = self.state[3:6]
        goal_vec = self.goal - pos
        goal_dist = np.linalg.norm(goal_vec)
        
        # 最近障碍物距离
        min_obs_dist = float('inf')
        for obs in self.obstacles:
            dist = np.linalg.norm(pos - obs['center']) - obs['radius']
            min_obs_dist = min(min_obs_dist, max(0, dist))
        
        # 归一化目标方向向量
        if goal_dist > 0:
            goal_direction = goal_vec / goal_dist
        else:
            goal_direction = [0, 0, 0]
        
        observation = np.concatenate([
            pos / 100.0,  # 归一化位置 (3维)
            vel / 5.0,    # 归一化速度 (3维)
            goal_direction,  # 目标方向 (3维)
            [goal_dist / 100.0],  # 归一化目标距离 (1维)
            [min_obs_dist / 20.0],  # 归一化障碍物距离 (1维)
            [len(self.obstacles) / 10.0]  # 障碍物数量 (1维)
        ])
        
        return observation
    
    def step(self, action):
        """环境步进"""
        # 更新状态
        dt = 0.1
        action = np.array(action, dtype=np.float64)
        self.state[:3] += action * dt  # 位置更新
        self.state[3:6] = action       # 速度更新
        
        self.step_count += 1
        
        # 计算奖励
        reward, done, info = self._calculate_reward()
        
        return self._get_observation(), reward, done, info
    
    def _calculate_reward(self):
        """计算奖励函数"""
        pos = self.state[:3]
        vel = self.state[3:6]
        
        # 基础奖励：接近目标
        goal_dist = np.linalg.norm(pos - self.goal)
        goal_reward = -goal_dist / 100.0
        
        # 到达目标奖励
        if goal_dist < 5.0:
            return 100.0, True, {'success': True, 'reason': 'goal_reached'}
        
        # 碰撞惩罚
        for obs in self.obstacles:
            dist = np.linalg.norm(pos - obs['center'])
            if dist <= obs['radius']:
                return -100.0, True, {'collision': True, 'reason': 'collision'}
        
        # 边界惩罚
        if (pos < 0).any() or (pos > self.bounds).any():
            return -50.0, True, {'out_of_bounds': True, 'reason': 'out_of_bounds'}
        
        # 速度奖励（鼓励适度移动）
        speed = np.linalg.norm(vel)
        speed_reward = min(speed / 3.0, 1.0) * 0.1
        
        # 安全奖励（距离障碍物的距离）
        min_obs_dist = float('inf')
        for obs in self.obstacles:
            dist = np.linalg.norm(pos - obs['center']) - obs['radius']
            min_obs_dist = min(min_obs_dist, dist)
        
        safety_reward = min(min_obs_dist / 10.0, 1.0) * 0.2
        
        # 时间惩罚（鼓励快速到达）
        time_penalty = -0.01
        
        # 超时
        done = self.step_count >= self.max_steps
        if done:
            info = {'timeout': True, 'reason': 'timeout'}
        else:
            info = {}
        
        total_reward = goal_reward + speed_reward + safety_reward + time_penalty
        
        return total_reward, done, info
    
    def get_state_info(self):
        """获取当前状态信息（用于调试）"""
        pos = self.state[:3]
        vel = self.state[3:6]
        goal_dist = np.linalg.norm(pos - self.goal)
        
        # 计算最近障碍物距离
        min_obs_dist = float('inf')
        closest_obs = None
        for i, obs in enumerate(self.obstacles):
            dist = np.linalg.norm(pos - obs['center']) - obs['radius']
            if dist < min_obs_dist:
                min_obs_dist = dist
                closest_obs = i
        
        return {
            'position': pos,
            'velocity': vel,
            'goal_distance': goal_dist,
            'min_obstacle_distance': min_obs_dist,
            'closest_obstacle_id': closest_obs,
            'step_count': self.step_count
        }
    
    def render_info(self):
        """打印环境信息（用于调试）"""
        info = self.get_state_info()
        print(f"位置: [{info['position'][0]:.1f}, {info['position'][1]:.1f}, {info['position'][2]:.1f}]")
        print(f"速度: [{info['velocity'][0]:.1f}, {info['velocity'][1]:.1f}, {info['velocity'][2]:.1f}]")
        print(f"目标距离: {info['goal_distance']:.1f}")
        print(f"最近障碍物距离: {info['min_obstacle_distance']:.1f}")
        print(f"步数: {info['step_count']}")
        print("-" * 40)

class EnvironmentVisualizer:
    """简单的环境可视化工具"""
    def __init__(self, env):
        self.env = env
    
    def print_environment_layout(self):
        """打印环境布局"""
        print("=" * 50)
        print("🌍 环境布局")
        print("=" * 50)
        print(f"🏠 起点: [{self.env.start[0]:.1f}, {self.env.start[1]:.1f}, {self.env.start[2]:.1f}]")
        print(f"🎯 终点: [{self.env.goal[0]:.1f}, {self.env.goal[1]:.1f}, {self.env.goal[2]:.1f}]")
        print(f"📏 直线距离: {np.linalg.norm(self.env.goal - self.env.start):.1f}")
        print()
        print("🚧 障碍物:")
        for i, obs in enumerate(self.env.obstacles):
            center = obs['center']
            radius = obs['radius']
            print(f"  障碍物{i+1}: 中心[{center[0]:.1f}, {center[1]:.1f}, {center[2]:.1f}], 半径{radius:.1f}")
        print("=" * 50)
    
    def print_trajectory_summary(self, trajectory):
        """打印轨迹摘要"""
        if not trajectory:
            return
        
        print("\n📊 轨迹摘要:")
        print(f"总步数: {len(trajectory)}")
        
        start_pos = trajectory[0]['position']
        end_pos = trajectory[-1]['position']
        total_distance = sum([
            np.linalg.norm(trajectory[i]['position'] - trajectory[i-1]['position'])
            for i in range(1, len(trajectory))
        ])
        
        print(f"起始位置: [{start_pos[0]:.1f}, {start_pos[1]:.1f}, {start_pos[2]:.1f}]")
        print(f"结束位置: [{end_pos[0]:.1f}, {end_pos[1]:.1f}, {end_pos[2]:.1f}]")
        print(f"总飞行距离: {total_distance:.1f}")
        print(f"直线距离: {np.linalg.norm(end_pos - start_pos):.1f}")
        print(f"路径效率: {np.linalg.norm(end_pos - start_pos) / total_distance:.2f}")
        
        # 安全性统计
        min_safety_dist = min([t['min_obstacle_distance'] for t in trajectory])
        print(f"最小安全距离: {min_safety_dist:.1f}")
        
        if trajectory[-1].get('success', False):
            print("✅ 成功到达目标!")
        else:
            print("❌ 未能到达目标")
