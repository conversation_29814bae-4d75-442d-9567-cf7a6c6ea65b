{"experiment_info": {"stage_number": 1, "environment": "stage1_simple", "total_episodes": 10, "random_episodes": 6, "fixed_episodes": 4, "timestamp": "20250718_203628", "training_time_seconds": 80.70713663101196, "visualization_interval": 3, "visualization_count": 3}, "performance_metrics": {"total_episodes": 10, "random_episodes": 6, "fixed_episodes": 4, "total_training_time": 80.70713663101196, "visualization_count": 3, "phase1_success_rate": 0.8333333333333334, "phase2_success_rate": 0.75, "overall_success_rate": 0.8, "avg_reward": -487.4708560480161, "avg_steps": 347.5, "collision_rate": 0.0, "timeout_rate": 0.2}, "episode_rewards": [-634.9387950216006, -340.14195127431805, -444.7412138476228, -440.73539232139706, -391.51384141609213, -600.453081455359, -481.7182879370049, -442.576314844895, -430.90519322538705, -666.9844891364847], "episode_steps": [425, 307, 324, 321, 306, 366, 330, 329, 327, 440], "success_episodes": [1, 2, 3, 4, 5, 6, 7, 8], "collision_episodes": [], "timeout_episodes": [0, 9], "visualization_episodes": [2, 5, 8], "phase_transitions": [{"phase": "random_to_fixed", "episode": 6, "selected_scenario": {"scenario": {"obstacles": [{"center": [30.0, 30.0, 30.0], "radius": 7.794887385535577}, {"center": [50.0, 20.0, 40.0], "radius": 6.449640513822719}, {"center": [40.0, 60.0, 50.0], "radius": 5.284684061507001}, {"center": [60.0, 40.0, 30.0], "radius": 7.841947814820349}, {"center": [70.0, 70.0, 60.0], "radius": 6.764152068568787}], "start": [10.0, 10.0, 10.0], "goal": [80.0, 80.0, 80.0]}, "complexity_score": 74.53732168239674, "episode_reward": -600.453081455359, "episode_success": true, "episode_num": 5}, "phase1_success_rate": 0.8333333333333334}], "most_complex_scenario": {"scenario": {"obstacles": [{"center": [30.0, 30.0, 30.0], "radius": 7.794887385535577}, {"center": [50.0, 20.0, 40.0], "radius": 6.449640513822719}, {"center": [40.0, 60.0, 50.0], "radius": 5.284684061507001}, {"center": [60.0, 40.0, 30.0], "radius": 7.841947814820349}, {"center": [70.0, 70.0, 60.0], "radius": 6.764152068568787}], "start": [10.0, 10.0, 10.0], "goal": [80.0, 80.0, 80.0]}, "complexity_score": 74.53732168239674, "episode_reward": -600.453081455359, "episode_success": true, "episode_num": 5}}