"""
复制论文文件到集成系统目录
"""

import shutil
import os

def copy_paper_files():
    """复制论文相关文件"""
    print("📄 复制论文文件...")
    
    # 要复制的文件列表
    files_to_copy = [
        'DWA_RL_Framework_Paper.tex',
        'DWA_RL_Technical_Specifications.tex',
        'DWA_RL_Framework_Flowchart.tex'
    ]
    
    source_dir = '..'  # 上级目录
    target_dir = '.'   # 当前目录
    
    copied_files = []
    
    for filename in files_to_copy:
        source_path = os.path.join(source_dir, filename)
        target_path = os.path.join(target_dir, filename)
        
        try:
            if os.path.exists(source_path):
                shutil.copy2(source_path, target_path)
                print(f"   ✅ {filename}")
                copied_files.append(filename)
            else:
                print(f"   ❌ {filename} (源文件不存在)")
        except Exception as e:
            print(f"   ❌ {filename} (复制失败: {e})")
    
    if copied_files:
        print(f"\n✅ 成功复制 {len(copied_files)} 个论文文件")
        print("📁 论文文件已集成到系统目录中")
    else:
        print("\n⚠️ 未能复制任何论文文件")
        print("请手动将论文文件复制到当前目录")

if __name__ == "__main__":
    copy_paper_files()
