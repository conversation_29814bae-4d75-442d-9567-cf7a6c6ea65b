"""
巡飞弹六自由度运动学环境 - 完整集成版本
实现论文中描述的巡飞弹运动学模型和三项奖励函数
"""

import numpy as np
import random
import math

class LoiteringMunitionEnvironment:
    """巡飞弹六自由度运动学环境"""
    
    def __init__(self, bounds=[2000, 2000, 200], fixed_scenario_config=None):
        self.bounds = bounds  # [x_max, y_max, z_max]
        self.fixed_scenario_config = fixed_scenario_config
        
        # 巡飞弹物理参数
        self.g = 9.81  # 重力加速度
        self.dt = 0.1  # 时间步长
        
        # 运动约束参数（论文中的参数）
        self.V_min = 15.0    # 最小速度（失速速度）
        self.V_max = 60.0    # 最大速度
        self.a_T_max = 8.0   # 最大切向加速度
        self.a_N_max = 39.24 # 最大法向加速度（4g）
        self.gamma_max = np.pi/3  # 最大航迹倾斜角（60°）
        self.d_safe = 2.0    # 最小安全距离
        
        self.reset()
        
    def reset(self, verbose=False):
        """重置环境"""
        if self.fixed_scenario_config:
            self._reset_with_fixed_scenario(verbose=verbose)
        else:
            self._reset_with_default_scenario()
        
        # 巡飞弹初始状态 [x, y, z, V, γ, ψ] - 使用改进的初始化
        if self.fixed_scenario_config:
            self.state = np.array([
                self.start[0], self.start[1], self.start[2],
                self.fixed_scenario_config['initial_velocity'],
                self.fixed_scenario_config['initial_gamma'],
                self.fixed_scenario_config['initial_psi']
            ], dtype=np.float64)
        else:
            # 使用智能初始状态设置
            self.state = self._get_intelligent_initial_state()

        # 确保这些属性总是被设置
        self.step_count = 0
        self.max_steps = 3000  # 增加最大步数，给更多时间到达目标
        self._prev_goal_dist = np.linalg.norm(self.state[:3] - self.goal)

        return self._get_observation()

    def _get_intelligent_initial_state(self):
        """获取智能的初始状态设置"""
        V_cruise = 25.0  # 巡航速度
        gamma_max = np.pi/3  # 最大倾斜角

        # 计算初始朝向（指向目标）
        direction = self.goal - self.start
        distance = np.linalg.norm(direction)

        if distance > 0:
            # 计算初始偏航角和倾斜角
            psi_initial = np.arctan2(direction[1], direction[0])
            gamma_initial = np.arcsin(direction[2] / distance)
            # 限制倾斜角范围
            gamma_initial = np.clip(gamma_initial, -gamma_max, gamma_max)
        else:
            psi_initial = 0.0
            gamma_initial = 0.0

        return np.array([
            self.start[0], self.start[1], self.start[2],
            V_cruise,      # 使用巡航速度作为初始速度
            gamma_initial, # 智能计算的初始倾斜角
            psi_initial    # 智能计算的初始偏航角
        ], dtype=np.float64)
    
    def _reset_with_fixed_scenario(self, verbose=True):
        """使用固定场景配置重置环境"""
        self.start = self.fixed_scenario_config['start'].copy()
        self.goal = self.fixed_scenario_config['goal'].copy()
        self.bounds = self.fixed_scenario_config['environment_bounds']
        
        # 静态障碍物
        self.obstacles = []
        for obs_config in self.fixed_scenario_config['static_obstacles']:
            self.obstacles.append({
                'center': obs_config['center'].copy(),
                'radius': obs_config['radius'],
                'type': obs_config.get('type', 'unknown')
            })
        
        # 动态障碍物
        self.dynamic_obstacles = []
        if self.fixed_scenario_config['dynamic_obstacles']:
            for obs_config in self.fixed_scenario_config['dynamic_obstacles']:
                dynamic_obs = {
                    'center': obs_config['center'].copy(),
                    'radius': obs_config['radius'],
                    'motion_type': obs_config['motion_type'],
                    'motion_params': obs_config['motion_params'].copy(),
                    'time': 0.0,
                    'type': obs_config.get('type', 'unknown')
                }
                self.dynamic_obstacles.append(dynamic_obs)
        
        if verbose:
            print(f"🎯 {self.fixed_scenario_config['description']}")
            print(f"📊 静态威胁: {len(self.obstacles)}个, 动态威胁: {len(self.dynamic_obstacles)}个")
            print(f"🚀 起点: [{self.start[0]:.0f}, {self.start[1]:.0f}, {self.start[2]:.0f}]")
            print(f"🎯 目标: [{self.goal[0]:.0f}, {self.goal[1]:.0f}, {self.goal[2]:.0f}]")
    
    def _reset_with_default_scenario(self):
        """默认场景"""
        self.start = np.array([100.0, 100.0, 50.0], dtype=np.float64)
        self.goal = np.array([1800.0, 1800.0, 120.0], dtype=np.float64)
        self.obstacles = []
        self.dynamic_obstacles = []
    
    def step(self, control_input):
        """执行一步仿真 - 六自由度运动学"""
        # 控制输入：[a_T, a_N, μ]
        a_T = np.clip(control_input[0], -self.a_T_max, self.a_T_max)
        a_N = np.clip(control_input[1], -self.a_N_max, self.a_N_max)
        mu = np.clip(control_input[2], -np.pi/2, np.pi/2)
        
        # 当前状态
        x, y, z, V, gamma, psi = self.state
        
        # 巡飞弹六自由度运动学方程积分
        # 位置更新
        x_new = x + V * np.cos(gamma) * np.cos(psi) * self.dt
        y_new = y + V * np.cos(gamma) * np.sin(psi) * self.dt
        z_new = z + V * np.sin(gamma) * self.dt
        
        # 速度更新
        V_new = V + (a_T - self.g * np.sin(gamma)) * self.dt
        V_new = np.clip(V_new, self.V_min, self.V_max)
        
        # 角度更新
        if V > 0.1:  # 避免除零
            gamma_new = gamma + (a_N * np.cos(mu) - self.g * np.cos(gamma)) / V * self.dt
            psi_new = psi + (a_N * np.sin(mu)) / (V * np.cos(gamma)) * self.dt
        else:
            gamma_new = gamma
            psi_new = psi
        
        # 角度约束
        gamma_new = np.clip(gamma_new, -self.gamma_max, self.gamma_max)
        psi_new = psi_new % (2 * np.pi)  # 保持在[0, 2π]范围内
        
        # 更新状态
        self.state = np.array([x_new, y_new, z_new, V_new, gamma_new, psi_new], dtype=np.float64)
        
        # 更新动态障碍物
        self._update_dynamic_obstacles()
        
        # 计算奖励和终止条件
        reward, done, info = self._calculate_simplified_reward()
        
        self.step_count += 1
        if self.step_count >= self.max_steps:
            done = True
            info['timeout'] = True
        
        return self._get_observation(), reward, done, info
    
    def _update_dynamic_obstacles(self):
        """更新动态障碍物位置"""
        for obs in self.dynamic_obstacles:
            obs['time'] += self.dt
            t = obs['time']
            
            if obs['motion_type'] == 'linear':
                # 线性运动
                velocity = obs['motion_params']['velocity']
                bounds = obs['motion_params']['bounds']
                
                new_pos = obs['center'] + velocity * t
                
                # 边界反弹
                if new_pos[0] <= bounds['x'][0] or new_pos[0] >= bounds['x'][1]:
                    obs['motion_params']['velocity'][0] *= -1
                if new_pos[1] <= bounds['y'][0] or new_pos[1] >= bounds['y'][1]:
                    obs['motion_params']['velocity'][1] *= -1
                if new_pos[2] <= bounds['z'][0] or new_pos[2] >= bounds['z'][1]:
                    obs['motion_params']['velocity'][2] *= -1
                
                obs['center'] = np.clip(new_pos, 
                                      [bounds['x'][0], bounds['y'][0], bounds['z'][0]],
                                      [bounds['x'][1], bounds['y'][1], bounds['z'][1]])
            
            elif obs['motion_type'] == 'circular':
                # 圆周运动
                center_orbit = obs['motion_params']['center_orbit']
                radius_orbit = obs['motion_params']['radius_orbit']
                angular_speed = obs['motion_params']['angular_speed']
                phase = obs['motion_params']['phase']
                
                angle = angular_speed * t + phase
                obs['center'] = center_orbit + np.array([
                    radius_orbit * np.cos(angle),
                    radius_orbit * np.sin(angle),
                    0
                ])
            
            elif obs['motion_type'] == 'oscillating':
                # 振荡运动
                center_base = obs['motion_params']['center_base']
                amplitude = obs['motion_params']['amplitude']
                frequency = obs['motion_params']['frequency']
                phase = obs['motion_params']['phase']
                
                obs['center'] = center_base + amplitude * np.sin(frequency * t + phase)
    
    def _calculate_simplified_reward(self):
        """计算论文中的三项奖励函数"""
        pos = self.state[:3]
        
        # 计算到目标的距离
        goal_dist = np.linalg.norm(pos - self.goal)
        
        # 计算距离改善
        distance_improvement = self._prev_goal_dist - goal_dist
        self._prev_goal_dist = goal_dist

        # 终端奖励：到达目标（增加奖励和范围）
        if goal_dist < 50.0:
            return 2000.0, True, {'success': True, 'reason': 'goal_reached'}

        # 终端惩罚：碰撞检查（增加安全距离，减少惩罚）
        for obs in self.obstacles + self.dynamic_obstacles:
            dist = np.linalg.norm(pos - obs['center'])
            if dist <= obs['radius'] + 5.0:  # 5米安全距离
                return -500.0, True, {'collision': True, 'reason': 'collision'}

        # 终端惩罚：越界检查（减少惩罚）
        if (pos[0] < 0 or pos[0] > self.bounds[0] or
            pos[1] < 0 or pos[1] > self.bounds[1] or
            pos[2] < 0 or pos[2] > self.bounds[2]):
            return -200.0, True, {'out_of_bounds': True, 'reason': 'out_of_bounds'}
        
        # 改进的奖励函数组件
        # 1. 距离改善奖励（鼓励接近目标）
        distance_reward = distance_improvement * 2.0  # 每米改善给2.0奖励

        # 2. 距离引导奖励（基于当前距离的连续奖励）
        max_distance = np.linalg.norm(self.start - self.goal)
        distance_progress = 1.0 - (goal_dist / max_distance)
        progress_reward = distance_progress * 20.0  # 基于进度的奖励

        # 3. 前进奖励（鼓励朝目标方向移动）
        V = self.state[3]
        goal_direction = (self.goal - pos) / (goal_dist + 1e-6)
        velocity_direction = np.array([
            V * np.cos(self.state[4]) * np.cos(self.state[5]),
            V * np.cos(self.state[4]) * np.sin(self.state[5]),
            V * np.sin(self.state[4])
        ])
        if np.linalg.norm(velocity_direction) > 0:
            velocity_direction = velocity_direction / np.linalg.norm(velocity_direction)
            alignment = np.dot(goal_direction, velocity_direction)
            forward_reward = max(0, alignment) * 10.0
        else:
            forward_reward = 0.0

        # 4. 安全距离奖励（平衡安全和进取）
        min_obs_dist = float('inf')
        for obs in self.obstacles + self.dynamic_obstacles:
            dist = np.linalg.norm(pos - obs['center']) - obs['radius']
            min_obs_dist = min(min_obs_dist, dist)

        if min_obs_dist < float('inf'):
            if min_obs_dist > 30.0:
                safety_reward = 5.0  # 安全距离奖励
            elif min_obs_dist > 15.0:
                safety_reward = 2.0
            elif min_obs_dist > 5.0:
                safety_reward = 0.0
            else:
                safety_reward = -min_obs_dist * 2.0  # 过近惩罚
        else:
            safety_reward = 0.0

        # 5. 速度奖励（鼓励保持合理速度）
        target_speed = 25.0  # 目标巡航速度
        speed_reward = max(0, 5.0 - abs(V - target_speed) * 0.2)

        # 6. 时间惩罚（减少）
        time_penalty = -0.5

        # 总奖励（更平衡的组合）
        total_reward = (distance_reward + progress_reward + forward_reward +
                       safety_reward + speed_reward + time_penalty)
        
        return total_reward, False, {}
    
    def _get_observation(self):
        """获取15维观测向量"""
        pos = self.state[:3]
        V, gamma, psi = self.state[3:6]
        
        # 目标方向
        goal_direction = self.goal - pos
        goal_dist = np.linalg.norm(goal_direction)
        if goal_dist > 0:
            goal_direction = goal_direction / goal_dist
        
        # 最近障碍物距离
        min_obs_dist = float('inf')
        for obs in self.obstacles + self.dynamic_obstacles:
            dist = np.linalg.norm(pos - obs['center']) - obs['radius']
            min_obs_dist = min(min_obs_dist, dist)
        
        # 构建15维观测向量
        observation = np.array([
            pos[0] / self.bounds[0],  # 归一化位置
            pos[1] / self.bounds[1],
            pos[2] / self.bounds[2],
            V / self.V_max,           # 归一化速度
            gamma / self.gamma_max,   # 归一化航迹倾斜角
            psi / (2 * np.pi),        # 归一化偏航角
            goal_direction[0],        # 目标方向
            goal_direction[1],
            goal_direction[2],
            goal_dist / 100.0,        # 归一化目标距离
            min_obs_dist / 50.0,      # 归一化障碍物距离
            len(self.obstacles) / 20.0,  # 静态障碍物数量
            len(self.dynamic_obstacles) / 10.0,  # 动态障碍物数量
            1.0 if self.dynamic_obstacles else 0.0,  # 动态标志
            pos[2] / self.bounds[2]   # 地形高度
        ], dtype=np.float64)
        
        return observation
